terraform {
  required_version = ">= 1.0.0"
}

locals {
  name_prefix = "${var.env}-${var.app_name}"

  # Process services configuration
  services = var.services_config != null ? var.services_config : {}
}

# Create ECS Cluster
resource "aws_ecs_cluster" "this" {
  name = "${local.name_prefix}-cluster"

  setting {
    name  = "containerInsights"
    value = var.enable_container_insights ? "enabled" : "disabled"
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-cluster"
    }
  )
}

# Create ALB using the alb module
module "alb" {
  source   = "../alb"
  env      = var.env
  app_name = var.app_name

  # Network settings
  internal           = var.internal_alb
  vpc_id             = var.vpc_id
  subnet_ids         = var.alb_subnet_ids
  security_group_ids = var.alb_security_group_ids

  # ALB settings
  enable_deletion_protection = var.enable_deletion_protection
  idle_timeout               = var.idle_timeout
  drop_invalid_header_fields = var.drop_invalid_header_fields
  enable_http2               = var.enable_http2

  # HTTP listener
  create_http_listener   = var.create_http_listener
  http_listener_type     = var.http_listener_type
  fixed_response_content = var.fixed_response_content

  # HTTPS listener
  create_https_listener       = var.create_https_listener
  certificate_arn             = var.certificate_arn
  additional_certificate_arns = var.additional_certificate_arns
  ssl_policy                  = var.ssl_policy

  # Default target group - will be overridden by service-specific target groups
  create_default_target_group = var.create_default_target_group

  # Access logs
  access_logs_bucket = var.access_logs_bucket
  access_logs_prefix = var.access_logs_prefix

  # Tags
  tags = var.tags
}

# Create ECS Services with path-based routing
resource "aws_ecs_task_definition" "services" {
  for_each = local.services

  family                   = "${local.name_prefix}-${each.key}-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = try(each.value.task_cpu, var.default_task_cpu)
  memory                   = try(each.value.task_memory, var.default_task_memory)
  execution_role_arn       = aws_iam_role.execution_role.arn
  task_role_arn            = aws_iam_role.task_role.arn

  container_definitions = jsonencode([
    {
      name      = "${local.name_prefix}-${each.key}-container"
      image     = each.value.container_image
      essential = true

      portMappings = [
        {
          containerPort = try(each.value.container_port, var.default_container_port)
          hostPort      = try(each.value.container_port, var.default_container_port)
          protocol      = "tcp"
        }
      ]

      environment = [
        for k, v in try(each.value.environment_variables, {}) : {
          name  = k
          value = v
        }
      ]

      secrets = [
        for k, v in try(each.value.secrets, {}) : {
          name      = k
          valueFrom = v
        }
      ]
      cpu = try(each.value.task_cpu, var.default_task_cpu)

      memory = try(each.value.task_memory, var.default_task_memory)

      # Only include command if it's not null
      command = try(each.value.command, null) != null ? each.value.command : null

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.services[each.key].name
          "awslogs-region"        = data.aws_region.current.region
          "awslogs-stream-prefix" = each.key
        }
      }
    }
  ])

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}-task"
    }
  )
}

# CloudWatch Log Groups for each service
resource "aws_cloudwatch_log_group" "services" {
  for_each = local.services

  name              = "/ecs/${local.name_prefix}/${each.key}"
  retention_in_days = try(each.value.log_retention_days, var.default_log_retention_days)

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "/ecs/${local.name_prefix}/${each.key}"
    }
  )
}

# Target Groups for each service
resource "aws_lb_target_group" "services" {
  for_each = local.services

  name        = "${local.name_prefix}-${each.key}-tg"
  port        = try(each.value.container_port, var.default_container_port)
  protocol    = var.use_https_for_target_groups && var.create_https_listener ? "HTTPS" : "HTTP"
  vpc_id      = var.vpc_id != null ? var.vpc_id : data.aws_vpc.default.id
  target_type = "ip"

  health_check {
    enabled             = true
    interval            = try(each.value.health_check_interval, var.default_health_check_interval)
    path                = try(each.value.health_check_path, var.default_health_check_path)
    port                = "traffic-port"
    protocol            = var.use_https_for_target_groups && var.create_https_listener ? "HTTPS" : "HTTP"
    timeout             = try(each.value.health_check_timeout, var.default_health_check_timeout)
    healthy_threshold   = try(each.value.health_check_healthy_threshold, var.default_health_check_healthy_threshold)
    unhealthy_threshold = try(each.value.health_check_unhealthy_threshold, var.default_health_check_unhealthy_threshold)
    matcher             = try(each.value.health_check_matcher, var.default_health_check_matcher)
  }

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}-tg"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}

# ALB Listener Rules for each service
resource "aws_lb_listener_rule" "services" {
  for_each = local.services

  listener_arn = var.create_https_listener ? module.alb.https_listener_arn : module.alb.http_listener_arn
  priority     = try(each.value.listener_rule_priority, 100 + index(keys(local.services), each.key))

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.services[each.key].arn
  }

  condition {
    path_pattern {
      values = each.value.path_patterns
    }
  }

  dynamic "condition" {
    for_each = try(each.value.host_headers, null) != null ? [1] : []
    content {
      host_header {
        values = each.value.host_headers
      }
    }
  }

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}-rule"
    }
  )
}

# Security Group for ECS Tasks
resource "aws_security_group" "ecs_tasks" {
  name        = "${local.name_prefix}-ecs-tasks-sg"
  description = "Security group for ${local.name_prefix} ECS tasks"
  vpc_id      = var.vpc_id != null ? var.vpc_id : data.aws_vpc.default.id

  # Allow inbound traffic from ALB
  dynamic "ingress" {
    for_each = toset(distinct([for s in local.services : try(s.container_port, var.default_container_port)]))
    content {
      from_port       = ingress.value
      to_port         = ingress.value
      protocol        = "tcp"
      security_groups = [module.alb.security_group_id]
      description     = "Allow inbound traffic from ALB to port ${ingress.value}"
    }
  }

  # Allow HTTP traffic from anywhere
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP traffic from anywhere"
  }

  # Allow HTTPS traffic from anywhere
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTPS traffic from anywhere"
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-ecs-tasks-sg"
    }
  )
}

# ECS Services
resource "aws_ecs_service" "services" {
  for_each = local.services

  name            = "${local.name_prefix}-${each.key}-service"
  cluster         = aws_ecs_cluster.this.id
  task_definition = aws_ecs_task_definition.services[each.key].arn
  launch_type     = "FARGATE"
  desired_count   = try(each.value.desired_count, var.default_desired_count)

  network_configuration {
    subnets          = var.service_subnet_ids != null ? var.service_subnet_ids : data.aws_subnets.default.ids
    security_groups  = [aws_security_group.ecs_tasks.id]
    assign_public_ip = try(each.value.assign_public_ip, var.default_assign_public_ip)
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.services[each.key].arn
    container_name   = "${local.name_prefix}-${each.key}-container"
    container_port   = try(each.value.container_port, var.default_container_port)
  }

  # Deployment circuit breaker is optional
  # We'll skip it for now to avoid issues with the plan

  health_check_grace_period_seconds = try(each.value.health_check_grace_period_seconds, var.default_health_check_grace_period_seconds)

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}-service"
    }
  )

  depends_on = [aws_lb_listener_rule.services]
}

# ECS Task Execution Role
resource "aws_iam_role" "execution_role" {
  name = "${local.name_prefix}-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-execution-role"
    }
  )
}

# Attach policies to execution role
resource "aws_iam_role_policy_attachment" "execution_role_policy" {
  role       = aws_iam_role.execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# ECS Task Role
resource "aws_iam_role" "task_role" {
  name = "${local.name_prefix}-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-task-role"
    }
  )
}

# Attach custom policies to task role if provided
resource "aws_iam_role_policy_attachment" "task_role_policy" {
  count      = length(var.task_role_policy_arns)
  role       = aws_iam_role.task_role.name
  policy_arn = var.task_role_policy_arns[count.index]
}
