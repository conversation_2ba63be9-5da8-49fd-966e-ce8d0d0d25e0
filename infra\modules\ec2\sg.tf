resource "aws_security_group" "this" {
  name        = "${var.app_id}-SG"
  description = "Security group to allow access to ${var.app_id} instance"
  vpc_id      = data.aws_vpc.this.id

  ingress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    self      = true
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_id}-sg"
  }
}
