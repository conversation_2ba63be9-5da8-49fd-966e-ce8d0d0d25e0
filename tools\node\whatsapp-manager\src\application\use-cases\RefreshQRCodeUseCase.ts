import { inject, injectable } from 'tsyringe';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { IWhatsAppService } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';
import { QRCodeManager } from '../../infrastructure/whatsapp/QRCodeManager';
import { RateLimiter } from '../../infrastructure/whatsapp/RateLimiter';

/**
 * Refresh QR code request
 */
export interface RefreshQRCodeRequest {
  userId: string;
  force?: boolean; // Force refresh even if current QR is still valid
}

/**
 * Refresh QR code response
 */
export interface RefreshQRCodeResponse {
  success: boolean;
  qrCode?: string;
  expiresAt?: Date;
  timeToExpiry?: number;
  isNewQR: boolean;
  message: string;
}

/**
 * Use case for refreshing QR codes for WhatsApp authentication
 * Handles QR code regeneration with rate limiting and validation
 */
@injectable()
export class RefreshQRCodeUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('IWhatsAppService') private whatsappService: IWhatsAppService,
    @inject('QRCodeManager') private qrCodeManager: QRCodeManager,
    @inject('RateLimiter') private rateLimiter: RateLimiter,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Execute the refresh QR code use case
   */
  async execute(request: RefreshQRCodeRequest): Promise<RefreshQRCodeResponse> {
    const { userId, force = false } = request;

    try {
      this.logger.info('Refreshing QR code', { userId, force });

      // Validate input
      this.validateRequest(request);

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'qr_generation');
      if (!rateLimitResult.allowed) {
        this.logger.warn('QR refresh rate limit exceeded', {
          userId,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        });

        return {
          success: false,
          isNewQR: false,
          message: `Rate limit exceeded. Try again after ${rateLimitResult.resetTime.toISOString()}`
        };
      }

      // Check if session exists
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        this.logger.error('Session not found for QR refresh', { userId });
        return {
          success: false,
          isNewQR: false,
          message: 'Session not found. Please create a session first.'
        };
      }

      // Check session status
      if (session.status === 'connected') {
        this.logger.warn('Attempted to refresh QR for connected session', { userId });
        return {
          success: false,
          isNewQR: false,
          message: 'Session is already connected. QR refresh not needed.'
        };
      }

      // Check if current QR is still valid (unless force refresh)
      if (!force) {
        const currentQR = this.qrCodeManager.getUserQRCode(userId);
        if (currentQR && currentQR.isValidForScanning()) {
          const timeToExpiry = currentQR.getTimeToExpiry();
          
          // If QR has more than 30 seconds left, don't refresh
          if (timeToExpiry > 30) {
            this.logger.debug('Current QR still valid, not refreshing', {
              userId,
              timeToExpiry
            });

            return {
              success: true,
              qrCode: currentQR.qrImageData,
              expiresAt: currentQR.expiresAt,
              timeToExpiry,
              isNewQR: false,
              message: 'Current QR code is still valid'
            };
          }
        }
      }

      // Generate new QR code through WhatsApp service
      const refreshResult = await this.whatsappService.regenerateQR(userId);
      
      if (!refreshResult.qrCode) {
        this.logger.error('Failed to generate new QR code', { userId });
        
        // Record failure for rate limiting
        this.rateLimiter.recordFailure(userId, 'qr_generation');
        
        return {
          success: false,
          isNewQR: false,
          message: 'Failed to generate new QR code. Please try again.'
        };
      }

      // Get the new QR code details
      const newQR = this.qrCodeManager.getUserQRCode(userId);
      if (!newQR) {
        this.logger.error('QR code not found after generation', { userId });
        return {
          success: false,
          isNewQR: false,
          message: 'QR code generation failed'
        };
      }

      // Update session status if needed
      if (session.status !== 'qr_pending') {
        await this.sessionRepository.updateSessionStatus(userId, 'qr_pending');
      }

      this.logger.info('QR code refreshed successfully', {
        userId,
        qrId: newQR.id,
        expiresAt: newQR.expiresAt,
        timeToExpiry: newQR.getTimeToExpiry()
      });

      return {
        success: true,
        qrCode: newQR.qrImageData,
        expiresAt: newQR.expiresAt,
        timeToExpiry: newQR.getTimeToExpiry(),
        isNewQR: true,
        message: 'QR code refreshed successfully'
      };

    } catch (error) {
      this.logger.error('Failed to refresh QR code', {
        userId,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'qr_generation');

      return {
        success: false,
        isNewQR: false,
        message: `Failed to refresh QR code: ${(error as Error).message}`
      };
    }
  }

  /**
   * Get current QR code status without refreshing
   */
  async getQRStatus(userId: string): Promise<{
    hasQR: boolean;
    isValid: boolean;
    qrCode?: string;
    expiresAt?: Date;
    timeToExpiry?: number;
    status: string;
  }> {
    try {
      this.logger.debug('Getting QR status', { userId });

      // Check if session exists
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        return {
          hasQR: false,
          isValid: false,
          status: 'no_session'
        };
      }

      // Check current QR
      const currentQR = this.qrCodeManager.getUserQRCode(userId);
      if (!currentQR) {
        return {
          hasQR: false,
          isValid: false,
          status: session.status
        };
      }

      const isValid = currentQR.isValidForScanning();
      const timeToExpiry = currentQR.getTimeToExpiry();

      return {
        hasQR: true,
        isValid,
        qrCode: isValid ? currentQR.qrImageData : undefined,
        expiresAt: currentQR.expiresAt,
        timeToExpiry,
        status: session.status
      };

    } catch (error) {
      this.logger.error('Failed to get QR status', {
        userId,
        error: (error as Error).message
      });

      return {
        hasQR: false,
        isValid: false,
        status: 'error'
      };
    }
  }

  /**
   * Check if QR refresh is allowed for user
   */
  async canRefreshQR(userId: string): Promise<{
    allowed: boolean;
    reason?: string;
    remaining?: number;
    resetTime?: Date;
  }> {
    try {
      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'qr_generation');
      if (!rateLimitResult.allowed) {
        return {
          allowed: false,
          reason: 'rate_limit_exceeded',
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        };
      }

      // Check if session exists
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        return {
          allowed: false,
          reason: 'session_not_found'
        };
      }

      // Check session status
      if (session.status === 'connected') {
        return {
          allowed: false,
          reason: 'already_connected'
        };
      }

      return {
        allowed: true
      };

    } catch (error) {
      this.logger.error('Failed to check QR refresh permission', {
        userId,
        error: (error as Error).message
      });

      return {
        allowed: false,
        reason: 'error'
      };
    }
  }

  /**
   * Validate the refresh request
   */
  private validateRequest(request: RefreshQRCodeRequest): void {
    if (!request.userId || request.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (request.userId.length > 100) {
      throw new Error('User ID is too long');
    }

    // Basic format validation
    if (!/^[a-zA-Z0-9_-]+$/.test(request.userId)) {
      throw new Error('User ID contains invalid characters');
    }
  }

  /**
   * Get QR refresh statistics for user
   */
  async getRefreshStatistics(userId: string): Promise<{
    usage: {
      count: number;
      remaining: number;
      resetTime: Date;
      windowMs: number;
    } | null;
    currentQR: {
      exists: boolean;
      isValid: boolean;
      timeToExpiry?: number;
    };
  }> {
    try {
      // Get rate limit usage
      const usage = this.rateLimiter.getUserUsage(userId, 'qr_generation');

      // Get current QR status
      const currentQR = this.qrCodeManager.getUserQRCode(userId);
      const qrStatus = {
        exists: !!currentQR,
        isValid: currentQR ? currentQR.isValidForScanning() : false,
        timeToExpiry: currentQR ? currentQR.getTimeToExpiry() : undefined
      };

      return {
        usage,
        currentQR: qrStatus
      };

    } catch (error) {
      this.logger.error('Failed to get refresh statistics', {
        userId,
        error: (error as Error).message
      });

      return {
        usage: null,
        currentQR: {
          exists: false,
          isValid: false
        }
      };
    }
  }
}
