locals {
  this = {
    tags = merge({
      "environment" = var.env
    }, var.tags)
  }

  # Conditional domain naming
  # Production: api.{domain} (e.g., api.ezychat.ai)
  # Non-production: api.{env}.{domain} (e.g., api.uat.ezychat.ai, api.dev.ezychat.ai)
  api_domain_name = var.env == "prod" ? "${var.api_subdomain}.${var.domain_name}" : "${var.api_subdomain}.${var.env}.${var.domain_name}"

  app_domain_name = var.env == "prod" ? "${var.app_subdomain}.${var.domain_name}" : "${var.app_subdomain}.${var.env}.${var.domain_name}"

  # Wildcard certificate domain - covers all subdomains
  certificate_domain_name = "*.${var.domain_name}"
}
