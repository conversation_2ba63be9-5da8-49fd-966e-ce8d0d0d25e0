# ECS Scheduler Module

This module creates a scheduler for ECS services that can automatically scale services up and down based on a schedule. It's useful for development and testing environments where you want to save costs by shutting down services outside of working hours.

## Features

- Automatically scales ECS services up and down based on a schedule
- Configurable schedule (default: shutdown at 8 PM SGT and startup at 8 AM SGT on weekdays)
- Can be enabled/disabled as needed

## Usage

```hcl
module "ecs_scheduler" {
  source = "../../modules/ecs_scheduler"
  
  env              = "dev"
  app_name         = "api"
  ecs_cluster_name = module.api_platform.cluster_name
  service_name     = "dev-api-test-service-service"
  
  # Optional: customize desired count when scaling up
  default_desired_count = 1
  
  # Optional: enable/disable the scheduler
  enabled = true
  
  tags = {
    Environment = "Development"
    ManagedBy   = "Terraform"
  }
}
```

## How It Works

1. The module creates two EventBridge scheduler schedules:
   - One for scaling down services at 8 PM SGT (12 PM UTC) on weekdays
   - One for scaling up services at 8 AM SGT (0 AM UTC) on weekdays

2. The schedules directly call the ECS UpdateService API to:
   - Set the desired count to 0 for scale down
   - Set the desired count to the configured value for scale up

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| env | Environment name (e.g., dev, staging, prod) | string | n/a | yes |
| app_name | Application name | string | n/a | yes |
| ecs_cluster_name | Name of the ECS cluster | string | n/a | yes |
| service_name | Name of the ECS service to schedule | string | n/a | yes |
| default_desired_count | Default desired count for services when scaling up | number | 1 | no |
| enabled | Whether the scheduler is enabled | bool | true | no |
| tags | Tags to apply to resources | map(string) | {} | no |

## Outputs

| Name | Description |
|------|-------------|
| scale_down_schedule_arn | ARN of the EventBridge schedule for scaling down |
| scale_up_schedule_arn | ARN of the EventBridge schedule for scaling up |
| scheduler_role_arn | ARN of the IAM role for the scheduler |
