variable "env" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "app_name" {
  description = "Application name"
  type        = string
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "internal" {
  description = "If true, the LB will be internal"
  type        = bool
  default     = false
}

variable "vpc_id" {
  description = "VPC ID where the load balancer will be deployed. If not specified, the default VPC will be used."
  type        = string
  default     = null
}

variable "subnet_ids" {
  description = "A list of subnet IDs to attach to the LB. If not specified, the default VPC's subnets will be used."
  type        = list(string)
  default     = null
}

variable "security_group_ids" {
  description = "A list of security group IDs to assign to the LB. If not specified, a new security group will be created."
  type        = list(string)
  default     = null
}

variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks to allow in the ALB security group"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "enable_deletion_protection" {
  description = "If true, deletion of the load balancer will be disabled via the AWS API"
  type        = bool
  default     = false
}

variable "idle_timeout" {
  description = "The time in seconds that the connection is allowed to be idle"
  type        = number
  default     = 60
}

variable "drop_invalid_header_fields" {
  description = "Indicates whether HTTP headers with header fields that are not valid are removed by the load balancer"
  type        = bool
  default     = true
}

variable "enable_http2" {
  description = "Indicates whether HTTP/2 is enabled in the load balancer"
  type        = bool
  default     = true
}

variable "access_logs_bucket" {
  description = "The S3 bucket name to store the logs in"
  type        = string
  default     = null
}

variable "access_logs_prefix" {
  description = "The S3 bucket prefix. Logs are stored in the root if not configured."
  type        = string
  default     = null
}

variable "create_http_listener" {
  description = "Whether to create HTTP listener"
  type        = bool
  default     = true
}

variable "http_listener_type" {
  description = "The type of routing action for HTTP listener. Valid values are 'forward', 'redirect', 'fixed-response'"
  type        = string
  default     = "redirect"
  validation {
    condition     = contains(["forward", "redirect", "fixed-response"], var.http_listener_type)
    error_message = "Valid values for http_listener_type are 'forward', 'redirect', or 'fixed-response'."
  }
}

variable "fixed_response_content" {
  description = "The content for fixed-response action"
  type        = string
  default     = "OK"
}

variable "create_https_listener" {
  description = "Whether to create HTTPS listener"
  type        = bool
  default     = true
}

variable "certificate_arn" {
  description = "The ARN of the default SSL server certificate"
  type        = string
  default     = null
}

variable "additional_certificate_arns" {
  description = "List of additional certificate ARNs for the HTTPS listener (for multi-domain support)"
  type        = list(string)
  default     = []
}

variable "ssl_policy" {
  description = "The name of the SSL Policy for the listener"
  type        = string
  default     = "ELBSecurityPolicy-TLS13-1-2-2021-06"
}

variable "default_target_group_arn" {
  description = "The ARN of the default target group"
  type        = string
  default     = null
}

variable "create_default_target_group" {
  description = "Whether to create default target group"
  type        = bool
  default     = true
}

variable "target_group_port" {
  description = "Port for the default target group"
  type        = number
  default     = 80
}

variable "target_group_protocol" {
  description = "Protocol for the default target group"
  type        = string
  default     = "HTTP"
}

variable "target_type" {
  description = "Type of target that you must specify when registering targets with this target group"
  type        = string
  default     = "instance"
  validation {
    condition     = contains(["instance", "ip", "lambda", "alb"], var.target_type)
    error_message = "Valid values for target_type are 'instance', 'ip', 'lambda', or 'alb'."
  }
}

variable "health_check_interval" {
  description = "Approximate amount of time, in seconds, between health checks"
  type        = number
  default     = 30
}

variable "health_check_path" {
  description = "Destination for the health check request"
  type        = string
  default     = "/"
}

variable "health_check_port" {
  description = "Port to use to connect with the target"
  type        = string
  default     = "traffic-port"
}

variable "health_check_protocol" {
  description = "Protocol to use to connect with the target"
  type        = string
  default     = "HTTP"
}

variable "health_check_timeout" {
  description = "Amount of time, in seconds, during which no response means a failed health check"
  type        = number
  default     = 5
}

variable "health_check_healthy_threshold" {
  description = "Number of consecutive health checks successes required before considering an unhealthy target healthy"
  type        = number
  default     = 3
}

variable "health_check_unhealthy_threshold" {
  description = "Number of consecutive health check failures required before considering the target unhealthy"
  type        = number
  default     = 3
}

variable "health_check_matcher" {
  description = "HTTP codes to use when checking for a successful response from a target"
  type        = string
  default     = "200-299"
}
