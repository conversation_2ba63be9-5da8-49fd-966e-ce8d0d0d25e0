"""
Storage Implementations for Document Ingestion Pipeline

This module provides concrete implementations of storage interfaces
using AWS S3 as the file storage layer.
"""

import os
import re
from typing import Any, Dict, Optional, Tuple
from urllib.parse import unquote_plus

import boto3
from botocore.exceptions import ClientError

from ..domain.entities import S3Location, UserId
from ..domain.interfaces import IFileStorage, ILogger, IMetricsCollector


class S3FileStorage(IFileStorage):
    """AWS S3 implementation of file storage"""

    def __init__(self, logger: ILogger, metrics: IMetricsCollector):
        self._logger = logger
        self._metrics = metrics
        self._client = boto3.client("s3")

    async def download_content(self, location: S3Location) -> bytes:
        """Download file content from S3"""
        try:
            self._logger.info(f"Downloading file: {location.uri}")

            response = self._client.get_object(Bucket=location.bucket, Key=location.key)
            content = response["Body"].read()

            self._metrics.record_success("s3_download", bucket=location.bucket)
            self._logger.debug(f"Downloaded {len(content)} bytes from {location.uri}")

            return content

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            self._metrics.record_error(f"s3_download_error_{error_code}")
            self._logger.error(f"Failed to download from {location.uri}: {e}", error=e)
            raise
        except Exception as e:
            self._metrics.record_error("s3_download_unexpected_error")
            self._logger.error(f"Unexpected error downloading from {location.uri}: {e}", error=e)
            raise

    async def move_file(self, source: S3Location, target_folder: str, user_id: UserId) -> bool:
        """Move file between folders in S3"""
        try:
            # Extract filename from source key
            filename = os.path.basename(source.key)

            # Construct target key
            target_key = f"{user_id}/{target_folder}/{filename}"
            target_location = S3Location(bucket=source.bucket, key=target_key)

            self._logger.info(f"Moving file from {source.uri} to {target_location.uri}")

            # Check if source file exists
            try:
                self._client.head_object(Bucket=source.bucket, Key=source.key)
            except ClientError as e:
                if e.response["Error"]["Code"] == "404":
                    self._logger.warning(f"Source file not found: {source.uri}")
                    return False
                else:
                    raise

            # Copy object to new location
            copy_source = {"Bucket": source.bucket, "Key": source.key}
            self._client.copy_object(CopySource=copy_source, Bucket=source.bucket, Key=target_key)

            # Verify copy was successful
            try:
                self._client.head_object(Bucket=source.bucket, Key=target_key)
            except ClientError as e:
                self._metrics.record_error("s3_copy_verification_failed")
                raise Exception(f"Failed to verify copied file: {e}")

            # Delete original object
            self._client.delete_object(Bucket=source.bucket, Key=source.key)

            # Verify deletion
            try:
                self._client.head_object(Bucket=source.bucket, Key=source.key)
                # If we get here, the file still exists
                self._logger.warning(f"Original file still exists after deletion: {source.uri}")
            except ClientError as e:
                if e.response["Error"]["Code"] == "404":
                    # File successfully deleted
                    pass
                else:
                    self._logger.warning(f"Error verifying file deletion: {e}")

            self._logger.info(f"Successfully moved file from {source.uri} to {target_location.uri}")
            self._metrics.record_success("s3_move", user_id=str(user_id))
            return True

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            self._metrics.record_error(f"s3_move_error_{error_code}")
            self._logger.error(f"Error moving S3 file {source.uri}: {e}", error=e)
            return False
        except Exception as e:
            self._metrics.record_error("s3_move_unexpected_error")
            self._logger.error(f"Unexpected error moving S3 file {source.uri}: {e}", error=e)
            return False

    def extract_user_info(self, key: str) -> tuple[UserId | None, dict[str, str] | None]:
        """Extract user information from S3 key"""
        try:
            # Pattern: {user_id}/uploaded/{filename}.csv
            pattern = r"^([^/]+)/uploaded/(.+\.csv)$"
            match = re.match(pattern, key)

            if match:
                user_id_str = match.group(1)
                filename = match.group(2)

                user_id = UserId(user_id_str)
                file_info = {"filename": filename, "folder": "uploaded"}

                return user_id, file_info

            self._logger.warning(f"S3 key does not match expected pattern: {key}")
            return None, None

        except Exception as e:
            self._logger.error(f"Error extracting user info from S3 key {key}: {e}", error=e)
            return None, None

    def validate_file_format(self, key: str) -> bool:
        """Validate that the file is a CSV"""
        return key.lower().endswith(".csv")


class S3EventExtractor:
    """Utility class for extracting information from S3 events"""

    @staticmethod
    def extract_s3_info(record: dict[str, Any]) -> tuple[str, str]:
        """Extract bucket and key from S3 event record"""
        bucket = record["s3"]["bucket"]["name"]
        key = unquote_plus(record["s3"]["object"]["key"])
        return bucket, key

    @staticmethod
    def is_s3_event(record: dict[str, Any]) -> bool:
        """Check if record is an S3 event"""
        return record.get("eventSource") == "aws:s3"
