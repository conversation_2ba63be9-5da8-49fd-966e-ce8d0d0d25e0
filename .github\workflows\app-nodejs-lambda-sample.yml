name: App - Node.js Lambda Sample CI/CD

on:
  push:
    branches: [main]
    paths:
      - "sample/lambda-nodejs/**"
  pull_request:
    branches: [main]
    paths:
      - "sample/lambda-nodejs/**"
  workflow_dispatch:

# 🔧 PROJECT CONFIGURATION
# For new Lambda functions, modify these values:
# 1. lambda-path: Directory containing your Lambda code
# 2. function-name: Key name in lambda_services.yaml config file
#
# Note: This workflow uses GitOps approach - it updates lambda_services.yaml
# and lets Terraform handle the actual Lambda deployment when merged to main

env:
  # Project-specific settings (modify these for your application)
  LAMBDA_PATH: sample/lambda-nodejs
  FUNCTION_NAME: sample-lambda # Must match key in lambda_services.yaml

jobs:
  # Test and Build
  test-build:
    uses: ./.github/workflows/reusable-nodejs-test-build.yml
    with:
      lambda-path: sample/lambda-nodejs
      node-version: "18"
      run-tests: true
      run-lint: true
      run-build: true

  # Build and Push Docker Image
  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: sample/lambda-nodejs
      function-name: lambda-nodejs-sample
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  # Update UAT Config (Triggers Terraform Deployment)
  deploy-uat:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: write
      pull-requests: write
    with:
      function-name: sample-lambda
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      environment-name: uat

  # Summary Job
  summary:
    needs: [test-build, docker-build, deploy-uat]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Generate build summary
        run: |
          echo "## 🚀 Lambda Function CI/CD Pipeline" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "- **Test & Build**: ${{ needs.test-build.result == 'success' && '✅ Passed' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Docker Build**: ${{ needs.docker-build.result == 'success' && '✅ Passed' || needs.docker-build.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **UAT Config Update**: ${{ needs.deploy-uat.result == 'success' && '✅ Passed' || needs.deploy-uat.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ needs.deploy-uat.result }}" == "success" ]; then
            if [ "${{ needs.deploy-uat.outputs.deployment-status }}" = "deployed" ]; then
              echo "### 🔄 GitOps Deployment Status" >> $GITHUB_STEP_SUMMARY
              echo "- **Config Updated**: ✅ lambda_services.yaml updated with new image" >> $GITHUB_STEP_SUMMARY
              echo "- **Auto-Merge Branch**: \`${{ needs.deploy-uat.outputs.branch-name }}\`" >> $GITHUB_STEP_SUMMARY
              echo "- **Pull Request**: [#${{ needs.deploy-uat.outputs.pr-number }}](${{ needs.deploy-uat.outputs.pr-url }}) ✅ **Auto-merged**" >> $GITHUB_STEP_SUMMARY
              echo "- **Terraform Deploy**: 🔄 Triggered by merge to main" >> $GITHUB_STEP_SUMMARY
            else
              echo "### ⏭️ Deployment Skipped" >> $GITHUB_STEP_SUMMARY
              echo "- **Reason**: Image unchanged - no deployment needed" >> $GITHUB_STEP_SUMMARY
              echo "- **Current Image**: \`${{ needs.docker-build.outputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
              echo "- **Status**: ✅ Function already running latest version" >> $GITHUB_STEP_SUMMARY
            fi
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### � Deployment Details" >> $GITHUB_STEP_SUMMARY
            echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
            echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY
            echo "| **Function** | \`sample-lambda\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Environment** | \`uat\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Image** | \`${{ needs.docker-build.outputs.image-uri }}\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Config File** | \`infra/iac/core/configs/lambda_services.yaml\` |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🔗 Quick Links" >> $GITHUB_STEP_SUMMARY
            if [ "${{ needs.deploy-uat.outputs.deployment-status }}" = "deployed" ]; then
              echo "- 📋 **[View Pull Request #${{ needs.deploy-uat.outputs.pr-number }}](${{ needs.deploy-uat.outputs.pr-url }})** - See what was changed" >> $GITHUB_STEP_SUMMARY
            fi
            echo "- 📝 **[View Config File](https://github.com/${{ github.repository }}/blob/main/infra/iac/core/configs/lambda_services.yaml)** - lambda_services.yaml" >> $GITHUB_STEP_SUMMARY
            echo "- 🔄 **[View Actions](https://github.com/${{ github.repository }}/actions)** - Monitor Terraform deployment" >> $GITHUB_STEP_SUMMARY
          fi
