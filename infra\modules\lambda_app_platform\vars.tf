variable "env" {
  description = "Environment name (e.g., dev, uat, prod)"
  type        = string
}

variable "app_name" {
  description = "Application name"
  type        = string
  default     = "lambda-api"
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "api_stage_name" {
  description = "API Gateway stage name"
  type        = string
  default     = "api"
}

variable "domain_name" {
  description = "Custom domain name for the API Gateway (e.g., api.uat.ezychat.ai)"
  type        = string
  default     = null
}

variable "certificate_arn" {
  description = "ARN of the ACM certificate for the custom domain"
  type        = string
  default     = null
}

variable "route53_zone_id" {
  description = "Route 53 hosted zone ID for the domain"
  type        = string
  default     = null
}

variable "lambda_services_config" {
  description = "Configuration for Lambda services"
  type = map(object({
    # Lambda function configuration
    image_repository_uri = optional(string)
    filename             = optional(string)
    source_code_hash     = optional(string)
    handler              = optional(string)
    runtime              = optional(string)
    timeout              = optional(number)
    memory_size          = optional(number)
    log_retention_days   = optional(number)
    publish              = optional(bool)

    # Container configuration (for container-based Lambdas)
    entry_point = optional(list(string))
    command     = optional(list(string))

    # Environment configuration
    environment_variables = optional(map(string))
    secrets               = optional(map(string))

    # VPC configuration
    vpc_config = optional(object({
      subnet_ids         = list(string)
      security_group_ids = list(string)
    }))

    # IAM configuration
    iam_policy = optional(string)

    # API Gateway configuration - simplified like ECS path_patterns
    api_gateway = optional(object({
      path_patterns = list(string)
    }))

    # Additional tags
    tags = optional(map(string))
  }))
  default = {}
}
