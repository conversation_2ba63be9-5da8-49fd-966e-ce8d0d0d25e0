# Environment Configuration for Document Ingestion Pipeline
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Configuration
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_ANON_KEY=your-anonymous-key-here

# =============================================================================
# OPTIONAL CONFIGURATION (with defaults)
# =============================================================================

# Environment Settings
ENVIRONMENT=dev
SERVICE_VERSION=1.0.0
LOG_LEVEL=INFO

# OpenAI Embedding Configuration
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=512

# Processing Limits
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100

# Retry Configuration
RETRY_ATTEMPTS=3
RETRY_MIN_WAIT=1
RETRY_MAX_WAIT=60

# Batch Processing Configuration
EMBEDDING_BATCH_SIZE=100
SUPABASE_BATCH_SIZE=100

# Vector Search Configuration
SIMILARITY_THRESHOLD=0.7
MAX_SEARCH_RESULTS=10

# AWS Configuration (for Lambda deployment)
AWS_REGION=us-east-1
DOCUMENT_BUCKET_NAME=your-document-bucket-name

# Dependency Injection Configuration
# Enable/disable dependency injection container features
DI_AUTO_WIRE=true
DI_STRICT_MODE=false
