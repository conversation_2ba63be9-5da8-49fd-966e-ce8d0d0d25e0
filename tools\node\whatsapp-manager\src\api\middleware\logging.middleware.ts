import { Request, Response } from 'express';
import morgan from 'morgan';
import { ILoggerService } from '../../shared/logging/interfaces';

/**
 * Create Morgan logging middleware with structured logging
 */
export function createLoggingMiddleware(logger: ILoggerService): ReturnType<typeof morgan> {
  // Custom token for request ID
  morgan.token('requestId', (req: Request) => {
    return (req.headers['x-request-id'] as string) || 'unknown';
  });

  // Custom token for response time in milliseconds
  morgan.token('responseTimeMs', (_req: Request, res: Response) => {
    const responseTime = res.getHeader('X-Response-Time');
    return responseTime ? `${responseTime}ms` : 'unknown';
  });

  return morgan(
    ':method :url :status :res[content-length] - :response-time ms [requestId: :requestId]',
    {
      stream: {
        write: (message: string) => {
          // Parse the log message to extract structured data
          const logData = parseLogMessage(message.trim());
          
          if (logData.status >= 400) {
            logger.warn('HTTP request completed with error', logData);
          } else {
            logger.info('HTTP request completed', logData);
          }
        },
      },
    }
  );
}

interface ParsedLogData {
  method: string;
  url: string;
  status: number;
  contentLength?: string;
  responseTime?: string;
  requestId?: string;
  message?: string;
}

function parseLogMessage(message: string): ParsedLogData {
  // Parse Morgan log format: "GET /health 200 123 - 45.67 ms [requestId: abc-123]"
  const regex = /^(\w+) (.+?) (\d+) (.+?) - (.+?) ms \[requestId: (.+?)\]$/;
  const match = message.match(regex);

  if (match) {
    const result: ParsedLogData = {
      method: match[1] || 'UNKNOWN',
      url: match[2] || 'UNKNOWN',
      status: parseInt(match[3] || '0', 10),
    };

    if (match[4] && match[4] !== '-') {
      result.contentLength = match[4];
    }

    if (match[5]) {
      result.responseTime = match[5];
    }

    if (match[6] && match[6] !== 'unknown') {
      result.requestId = match[6];
    }

    return result;
  }

  // Fallback for unparseable messages
  return {
    method: 'UNKNOWN',
    url: 'UNKNOWN',
    status: 0,
    message,
  };
}
