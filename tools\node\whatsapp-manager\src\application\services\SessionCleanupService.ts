import { inject, injectable } from 'tsyringe';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { SessionEntity } from '../../domain/entities/SessionEntity';
import { BaileysAuthStateAdapter } from '../../infrastructure/whatsapp/BaileysAuthStateAdapter';

/**
 * Cleanup configuration
 */
export interface CleanupConfig {
  expiredSessionsMaxAge: number; // milliseconds
  disconnectedSessionsMaxAge: number; // milliseconds
  errorSessionsMaxAge: number; // milliseconds
  orphanedAuthStatesMaxAge: number; // milliseconds
  cleanupInterval: number; // milliseconds
  batchSize: number;
  dryRun: boolean;
}

/**
 * Cleanup statistics
 */
export interface CleanupStatistics {
  totalSessions: number;
  expiredSessions: number;
  disconnectedSessions: number;
  errorSessions: number;
  orphanedAuthStates: number;
  cleanedSessions: number;
  cleanedAuthStates: number;
  errors: number;
  duration: number;
  timestamp: Date;
}

/**
 * Cleanup result
 */
export interface CleanupResult {
  success: boolean;
  statistics: CleanupStatistics;
  errors: string[];
  message: string;
}

/**
 * Session cleanup service for maintaining system hygiene
 * Handles cleanup of expired sessions, orphaned auth states, and stale data
 */
@injectable()
export class SessionCleanupService {
  private readonly config: CleanupConfig;
  private readonly cleanupInterval: NodeJS.Timeout;
  private isRunning = false;
  private lastCleanup: Date | null = null;
  private cleanupHistory: CleanupStatistics[] = [];

  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('BaileysAuthStateAdapter') private authStateAdapter: BaileysAuthStateAdapter,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private configService: IConfigService
  ) {
    this.config = {
      expiredSessionsMaxAge: this.configService.getOptional('EXPIRED_SESSIONS_MAX_AGE_MS', 7 * 24 * 60 * 60 * 1000), // 7 days
      disconnectedSessionsMaxAge: this.configService.getOptional('DISCONNECTED_SESSIONS_MAX_AGE_MS', 24 * 60 * 60 * 1000), // 1 day
      errorSessionsMaxAge: this.configService.getOptional('ERROR_SESSIONS_MAX_AGE_MS', 6 * 60 * 60 * 1000), // 6 hours
      orphanedAuthStatesMaxAge: this.configService.getOptional('ORPHANED_AUTH_STATES_MAX_AGE_MS', 24 * 60 * 60 * 1000), // 1 day
      cleanupInterval: this.configService.getOptional('SESSION_CLEANUP_INTERVAL_MS', 60 * 60 * 1000), // 1 hour
      batchSize: this.configService.getOptional('CLEANUP_BATCH_SIZE', 100),
      dryRun: this.configService.getOptional('CLEANUP_DRY_RUN', false)
    };

    // Start automatic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performAutomaticCleanup();
    }, this.config.cleanupInterval);

    this.logger.info('SessionCleanupService initialized', {
      config: this.config
    });
  }

  /**
   * Perform comprehensive cleanup
   */
  async performCleanup(dryRun: boolean = false): Promise<CleanupResult> {
    if (this.isRunning) {
      return {
        success: false,
        statistics: this.createEmptyStatistics(),
        errors: ['Cleanup is already running'],
        message: 'Cleanup operation already in progress'
      };
    }

    const startTime = Date.now();
    this.isRunning = true;

    try {
      this.logger.info('Starting session cleanup', { dryRun });

      const statistics: CleanupStatistics = {
        totalSessions: 0,
        expiredSessions: 0,
        disconnectedSessions: 0,
        errorSessions: 0,
        orphanedAuthStates: 0,
        cleanedSessions: 0,
        cleanedAuthStates: 0,
        errors: 0,
        duration: 0,
        timestamp: new Date()
      };

      const errors: string[] = [];

      // Get all sessions
      const allSessions = await this.sessionRepository.findAllSessions();
      statistics.totalSessions = allSessions.length;

      this.logger.debug('Found sessions for cleanup analysis', {
        totalSessions: allSessions.length
      });

      // Categorize sessions for cleanup
      const now = Date.now();
      const sessionsToClean: SessionEntity[] = [];

      for (const session of allSessions) {
        const sessionAge = now - session.updatedAt.getTime();

        // Check for expired sessions
        if (this.isSessionExpired(session)) {
          statistics.expiredSessions++;
          sessionsToClean.push(session);
          continue;
        }

        // Check for old disconnected sessions
        if (session.status === 'disconnected' && sessionAge > this.config.disconnectedSessionsMaxAge) {
          statistics.disconnectedSessions++;
          sessionsToClean.push(session);
          continue;
        }

        // Check for old error sessions
        if (session.status === 'error' && sessionAge > this.config.errorSessionsMaxAge) {
          statistics.errorSessions++;
          sessionsToClean.push(session);
          continue;
        }
      }

      // Clean sessions in batches
      if (!dryRun && sessionsToClean.length > 0) {
        const cleanedCount = await this.cleanSessionsBatch(sessionsToClean, errors);
        statistics.cleanedSessions = cleanedCount;
      }

      // Clean orphaned auth states
      const orphanedAuthStates = await this.findOrphanedAuthStates(allSessions);
      statistics.orphanedAuthStates = orphanedAuthStates;

      if (!dryRun && orphanedAuthStates > 0) {
        const cleanedAuthStates = await this.cleanupOrphanedAuthStates();
        statistics.cleanedAuthStates = cleanedAuthStates;
      }

      statistics.errors = errors.length;
      statistics.duration = Date.now() - startTime;

      // Store cleanup history
      this.cleanupHistory.push(statistics);
      if (this.cleanupHistory.length > 100) {
        this.cleanupHistory.shift(); // Keep only last 100 cleanup runs
      }

      this.lastCleanup = new Date();

      this.logger.info('Session cleanup completed', {
        dryRun,
        statistics,
        errorsCount: errors.length
      });

      return {
        success: errors.length === 0,
        statistics,
        errors,
        message: dryRun 
          ? `Dry run completed. Would clean ${sessionsToClean.length} sessions and ${orphanedAuthStates} auth states`
          : `Cleanup completed. Cleaned ${statistics.cleanedSessions} sessions and ${statistics.cleanedAuthStates} auth states`
      };

    } catch (error) {
      this.logger.error('Session cleanup failed', {
        error: (error as Error).message,
        duration: Date.now() - startTime
      });

      return {
        success: false,
        statistics: this.createEmptyStatistics(),
        errors: [(error as Error).message],
        message: 'Cleanup operation failed'
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get cleanup statistics
   */
  getStatistics(): {
    isRunning: boolean;
    lastCleanup: Date | null;
    config: CleanupConfig;
    history: CleanupStatistics[];
  } {
    return {
      isRunning: this.isRunning,
      lastCleanup: this.lastCleanup,
      config: this.config,
      history: [...this.cleanupHistory]
    };
  }

  /**
   * Force cleanup of specific session
   */
  async cleanupSession(userId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      this.logger.info('Cleaning up specific session', { userId });

      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        return {
          success: false,
          message: 'Session not found'
        };
      }

      // Remove session
      await this.sessionRepository.deleteSession(userId);

      // Clean auth state
      await this.authStateAdapter.clearAuthState(userId);

      this.logger.info('Session cleaned up successfully', { userId });

      return {
        success: true,
        message: 'Session cleaned up successfully'
      };

    } catch (error) {
      this.logger.error('Failed to cleanup session', {
        userId,
        error: (error as Error).message
      });

      return {
        success: false,
        message: `Failed to cleanup session: ${(error as Error).message}`
      };
    }
  }

  /**
   * Check if session is expired
   */
  private isSessionExpired(session: SessionEntity): boolean {
    if (!session.ttl) {
      return false;
    }

    const now = Math.floor(Date.now() / 1000);
    return session.ttl < now;
  }

  /**
   * Clean sessions in batches
   */
  private async cleanSessionsBatch(sessions: SessionEntity[], errors: string[]): Promise<number> {
    let cleanedCount = 0;

    for (let i = 0; i < sessions.length; i += this.config.batchSize) {
      const batch = sessions.slice(i, i + this.config.batchSize);
      
      for (const session of batch) {
        try {
          await this.sessionRepository.deleteSession(session.userId);
          await this.authStateAdapter.clearAuthState(session.userId);
          cleanedCount++;

          this.logger.debug('Session cleaned', {
            userId: session.userId,
            status: session.status,
            age: Date.now() - session.updatedAt.getTime()
          });

        } catch (error) {
          const errorMessage = `Failed to clean session ${session.userId}: ${(error as Error).message}`;
          errors.push(errorMessage);
          this.logger.error('Failed to clean session', {
            userId: session.userId,
            error: (error as Error).message
          });
        }
      }

      // Small delay between batches to avoid overwhelming the system
      if (i + this.config.batchSize < sessions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return cleanedCount;
  }

  /**
   * Find orphaned auth states
   */
  private async findOrphanedAuthStates(_sessions: SessionEntity[]): Promise<number> {
    try {
      // This would typically query the auth state storage directly
      // For now, we'll use the cleanup method from auth state adapter
      return await this.authStateAdapter.cleanupExpiredAuthStates();
    } catch (error) {
      this.logger.error('Failed to find orphaned auth states', {
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Cleanup orphaned auth states
   */
  private async cleanupOrphanedAuthStates(): Promise<number> {
    try {
      return await this.authStateAdapter.cleanupExpiredAuthStates();
    } catch (error) {
      this.logger.error('Failed to cleanup orphaned auth states', {
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Perform automatic cleanup
   */
  private async performAutomaticCleanup(): Promise<void> {
    try {
      if (this.isRunning) {
        this.logger.debug('Skipping automatic cleanup - already running');
        return;
      }

      this.logger.debug('Starting automatic cleanup');
      await this.performCleanup(this.config.dryRun);
    } catch (error) {
      this.logger.error('Automatic cleanup failed', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Create empty statistics object
   */
  private createEmptyStatistics(): CleanupStatistics {
    return {
      totalSessions: 0,
      expiredSessions: 0,
      disconnectedSessions: 0,
      errorSessions: 0,
      orphanedAuthStates: 0,
      cleanedSessions: 0,
      cleanedAuthStates: 0,
      errors: 0,
      duration: 0,
      timestamp: new Date()
    };
  }

  /**
   * Update cleanup configuration
   */
  updateConfig(newConfig: Partial<CleanupConfig>): void {
    Object.assign(this.config, newConfig);
    this.logger.info('Cleanup configuration updated', {
      newConfig: this.config
    });
  }

  /**
   * Stop automatic cleanup
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.logger.info('SessionCleanupService stopped');
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stop();
    this.cleanupHistory.length = 0;
    this.logger.info('SessionCleanupService destroyed');
  }
}
