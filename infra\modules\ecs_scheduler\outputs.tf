output "scale_down_schedule_arn" {
  description = "ARN of the EventBridge schedule for scaling down"
  value       = aws_scheduler_schedule.scale_down.arn
}

output "scale_up_schedule_arn" {
  description = "ARN of the EventBridge schedule for scaling up"
  value       = aws_scheduler_schedule.scale_up.arn
}

output "scheduler_role_arn" {
  description = "ARN of the IAM role for the scheduler"
  value       = aws_iam_role.scheduler_role.arn
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.ecs_scheduler.arn
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.ecs_scheduler.function_name
}
