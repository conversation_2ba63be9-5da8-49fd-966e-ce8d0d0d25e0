import { inject, injectable } from 'tsyringe';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { HealthChecker } from '../../infrastructure/whatsapp/HealthChecker';
import { ConnectionPool } from '../../infrastructure/whatsapp/ConnectionPool';
import { RateLimiter } from '../../infrastructure/whatsapp/RateLimiter';
import { BaileysEventEmitter } from '../../infrastructure/whatsapp/BaileysEventEmitter';

/**
 * Monitoring metrics
 */
export interface MonitoringMetrics {
  timestamp: Date;
  system: {
    uptime: number;
    memoryUsage: {
      heapUsedMB: number;
      heapTotalMB: number;
      rssMB: number;
      heapUsagePercent: number;
    };
    cpuUsage?: number;
    loadAverage?: number[];
  };
  sessions: {
    total: number;
    active: number;
    byStatus: { [status: string]: number };
    averageAge: number;
    connectionSuccessRate: number;
  };
  connections: {
    poolSize: number;
    activeConnections: number;
    utilization: number;
    averageUseCount: number;
  };
  rateLimiting: {
    userLimitsCount: number;
    globalLimitsCount: number;
  };
  events: {
    totalEvents: number;
    eventsByType: { [type: string]: number };
    listenerCount: number;
    errorCount: number;
  };
  performance: {
    averageResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
  };
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    memoryUsagePercent: number;
    cpuUsagePercent: number;
    errorRate: number;
    connectionFailureRate: number;
    responseTimeMs: number;
  };
  cooldownMs: number;
}

/**
 * Alert event
 */
export interface AlertEvent {
  id: string;
  type: 'memory' | 'cpu' | 'error_rate' | 'connection_failure' | 'response_time';
  severity: 'warning' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
  resolved?: Date;
}

/**
 * Monitoring service for system observability and alerting
 * Collects metrics, monitors performance, and triggers alerts
 */
@injectable()
export class MonitoringService {
  private readonly metricsHistory: MonitoringMetrics[] = [];
  private readonly alerts: AlertEvent[] = [];
  private readonly alertConfig: AlertConfig;
  private readonly metricsInterval: NodeJS.Timeout;
  private readonly maxHistorySize: number;
  private readonly performanceCounters = {
    requestCount: 0,
    errorCount: 0,
    totalResponseTime: 0,
    lastResetTime: Date.now()
  };

  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('HealthChecker') private healthChecker: HealthChecker,
    @inject('ConnectionPool') private connectionPool: ConnectionPool,
    @inject('RateLimiter') private rateLimiter: RateLimiter,
    @inject('BaileysEventEmitter') private eventEmitter: BaileysEventEmitter,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    this.maxHistorySize = this.config.getOptional('METRICS_HISTORY_SIZE', 1000);

    this.alertConfig = {
      enabled: this.config.getOptional('MONITORING_ALERTS_ENABLED', true),
      thresholds: {
        memoryUsagePercent: this.config.getOptional('ALERT_MEMORY_THRESHOLD', 85),
        cpuUsagePercent: this.config.getOptional('ALERT_CPU_THRESHOLD', 80),
        errorRate: this.config.getOptional('ALERT_ERROR_RATE_THRESHOLD', 5),
        connectionFailureRate: this.config.getOptional('ALERT_CONNECTION_FAILURE_THRESHOLD', 10),
        responseTimeMs: this.config.getOptional('ALERT_RESPONSE_TIME_THRESHOLD', 5000)
      },
      cooldownMs: this.config.getOptional('ALERT_COOLDOWN_MS', 5 * 60 * 1000) // 5 minutes
    };

    // Start metrics collection
    const metricsInterval = this.config.getOptional('METRICS_COLLECTION_INTERVAL_MS', 60000); // 1 minute
    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, metricsInterval);

    // Set up event listeners for performance tracking
    this.setupEventListeners();

    this.logger.info('MonitoringService initialized', {
      alertConfig: this.alertConfig,
      metricsInterval,
      maxHistorySize: this.maxHistorySize
    });
  }

  /**
   * Get current metrics
   */
  async getCurrentMetrics(): Promise<MonitoringMetrics> {
    try {
      const timestamp = new Date();

      // System metrics
      const memoryUsage = process.memoryUsage();
      const systemMetrics = {
        uptime: process.uptime() * 1000,
        memoryUsage: {
          heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          rssMB: Math.round(memoryUsage.rss / 1024 / 1024),
          heapUsagePercent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
        },
        cpuUsage: await this.getCPUUsage(),
        loadAverage: this.getLoadAverage()
      };

      // Session metrics
      const sessions = await this.sessionRepository.findAllSessions();
      const sessionMetrics = this.calculateSessionMetrics(sessions);

      // Connection metrics
      const connectionStats = this.connectionPool.getStatistics();
      const connectionMetrics = {
        poolSize: connectionStats.totalConnections,
        activeConnections: connectionStats.activeConnections,
        utilization: connectionStats.currentUtilization,
        averageUseCount: connectionStats.averageUseCount
      };

      // Rate limiting metrics
      const rateLimitStats = this.rateLimiter.getStatistics();
      const rateLimitMetrics = {
        userLimitsCount: rateLimitStats.userLimitsCount,
        globalLimitsCount: rateLimitStats.globalLimitsCount
      };

      // Event metrics
      const eventStats = this.eventEmitter.getStatistics();
      const eventMetrics = {
        totalEvents: eventStats.totalEvents,
        eventsByType: eventStats.eventsByType,
        listenerCount: eventStats.listenerCount,
        errorCount: eventStats.errorCount
      };

      // Performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics();

      const metrics: MonitoringMetrics = {
        timestamp,
        system: systemMetrics,
        sessions: sessionMetrics,
        connections: connectionMetrics,
        rateLimiting: rateLimitMetrics,
        events: eventMetrics,
        performance: performanceMetrics
      };

      return metrics;

    } catch (error) {
      this.logger.error('Failed to collect current metrics', {
        error: (error as Error).message
      });

      // Return minimal metrics on error
      return this.createMinimalMetrics();
    }
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(limit?: number): MonitoringMetrics[] {
    const history = [...this.metricsHistory];
    if (limit && limit > 0) {
      return history.slice(-limit);
    }
    return history;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): AlertEvent[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Get all alerts
   */
  getAllAlerts(limit?: number): AlertEvent[] {
    const alerts = [...this.alerts];
    if (limit && limit > 0) {
      return alerts.slice(-limit);
    }
    return alerts;
  }

  /**
   * Record performance data
   */
  recordRequest(responseTime: number, isError: boolean = false): void {
    this.performanceCounters.requestCount++;
    this.performanceCounters.totalResponseTime += responseTime;
    
    if (isError) {
      this.performanceCounters.errorCount++;
    }
  }

  /**
   * Get system summary
   */
  async getSystemSummary(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    activeAlerts: number;
    totalSessions: number;
    activeSessions: number;
    memoryUsagePercent: number;
    uptime: number;
    lastMetricsUpdate: Date | null;
  }> {
    try {
      const systemHealth = await this.healthChecker.getSystemHealth();
      const activeAlerts = this.getActiveAlerts().length;
      const lastMetrics = this.metricsHistory[this.metricsHistory.length - 1];

      return {
        status: systemHealth.status,
        activeAlerts,
        totalSessions: systemHealth.metrics.totalSessions,
        activeSessions: systemHealth.metrics.activeSessions,
        memoryUsagePercent: Math.round((systemHealth.metrics.memoryUsage.heapUsed / systemHealth.metrics.memoryUsage.heapTotal) * 100),
        uptime: systemHealth.uptime,
        lastMetricsUpdate: lastMetrics?.timestamp || null
      };

    } catch (error) {
      this.logger.error('Failed to get system summary', {
        error: (error as Error).message
      });

      return {
        status: 'unhealthy',
        activeAlerts: 0,
        totalSessions: 0,
        activeSessions: 0,
        memoryUsagePercent: 0,
        uptime: 0,
        lastMetricsUpdate: null
      };
    }
  }

  /**
   * Collect metrics periodically
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics = await this.getCurrentMetrics();
      
      // Add to history
      this.metricsHistory.push(metrics);
      
      // Trim history if too large
      if (this.metricsHistory.length > this.maxHistorySize) {
        this.metricsHistory.shift();
      }

      // Check for alerts
      if (this.alertConfig.enabled) {
        this.checkAlerts(metrics);
      }

      this.logger.debug('Metrics collected', {
        timestamp: metrics.timestamp,
        memoryUsage: metrics.system.memoryUsage.heapUsagePercent,
        activeSessions: metrics.sessions.active,
        totalEvents: metrics.events.totalEvents
      });

    } catch (error) {
      this.logger.error('Failed to collect metrics', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Calculate session metrics
   */
  private calculateSessionMetrics(sessions: any[]): MonitoringMetrics['sessions'] {
    const total = sessions.length;
    const active = sessions.filter(s => s.status === 'connected').length;
    
    const byStatus: { [status: string]: number } = {};
    let totalAge = 0;
    let connectedCount = 0;

    for (const session of sessions) {
      const status = session.status || 'unknown';
      byStatus[status] = (byStatus[status] || 0) + 1;
      
      const age = Date.now() - session.createdAt.getTime();
      totalAge += age;
      
      if (session.status === 'connected') {
        connectedCount++;
      }
    }

    const averageAge = total > 0 ? totalAge / total : 0;
    const connectionSuccessRate = total > 0 ? (connectedCount / total) * 100 : 0;

    return {
      total,
      active,
      byStatus,
      averageAge,
      connectionSuccessRate
    };
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(): MonitoringMetrics['performance'] {
    const now = Date.now();
    const timeSinceReset = now - this.performanceCounters.lastResetTime;
    const timeInSeconds = timeSinceReset / 1000;

    const averageResponseTime = this.performanceCounters.requestCount > 0 
      ? this.performanceCounters.totalResponseTime / this.performanceCounters.requestCount 
      : 0;

    const requestsPerSecond = timeInSeconds > 0 
      ? this.performanceCounters.requestCount / timeInSeconds 
      : 0;

    const errorRate = this.performanceCounters.requestCount > 0 
      ? (this.performanceCounters.errorCount / this.performanceCounters.requestCount) * 100 
      : 0;

    // Reset counters every hour
    if (timeSinceReset > 60 * 60 * 1000) {
      this.performanceCounters.requestCount = 0;
      this.performanceCounters.errorCount = 0;
      this.performanceCounters.totalResponseTime = 0;
      this.performanceCounters.lastResetTime = now;
    }

    return {
      averageResponseTime,
      requestsPerSecond,
      errorRate
    };
  }

  /**
   * Check for alert conditions
   */
  private checkAlerts(metrics: MonitoringMetrics): void {
    // const now = new Date(); // Unused for now

    // Memory usage alert
    if (metrics.system.memoryUsage.heapUsagePercent > this.alertConfig.thresholds.memoryUsagePercent) {
      this.createAlert('memory', 'warning', 
        `High memory usage: ${metrics.system.memoryUsage.heapUsagePercent}%`,
        metrics.system.memoryUsage.heapUsagePercent,
        this.alertConfig.thresholds.memoryUsagePercent
      );
    }

    // Error rate alert
    if (metrics.performance.errorRate > this.alertConfig.thresholds.errorRate) {
      this.createAlert('error_rate', 'warning',
        `High error rate: ${metrics.performance.errorRate.toFixed(2)}%`,
        metrics.performance.errorRate,
        this.alertConfig.thresholds.errorRate
      );
    }

    // Response time alert
    if (metrics.performance.averageResponseTime > this.alertConfig.thresholds.responseTimeMs) {
      this.createAlert('response_time', 'warning',
        `High response time: ${metrics.performance.averageResponseTime.toFixed(0)}ms`,
        metrics.performance.averageResponseTime,
        this.alertConfig.thresholds.responseTimeMs
      );
    }

    // Resolve alerts that are no longer triggered
    this.resolveAlerts(metrics);
  }

  /**
   * Create alert
   */
  private createAlert(
    type: AlertEvent['type'],
    severity: AlertEvent['severity'],
    message: string,
    value: number,
    threshold: number
  ): void {
    const now = new Date();
    
    // Check if similar alert exists and is in cooldown
    const existingAlert = this.alerts
      .filter(a => a.type === type && !a.resolved)
      .find(a => now.getTime() - a.timestamp.getTime() < this.alertConfig.cooldownMs);

    if (existingAlert) {
      return; // Skip creating alert due to cooldown
    }

    const alert: AlertEvent = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      value,
      threshold,
      timestamp: now
    };

    this.alerts.push(alert);

    this.logger.warn('Alert triggered', {
      alertId: alert.id,
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      value: alert.value,
      threshold: alert.threshold
    });

    // Trim alerts history
    if (this.alerts.length > 1000) {
      this.alerts.splice(0, 100); // Remove oldest 100 alerts
    }
  }

  /**
   * Resolve alerts
   */
  private resolveAlerts(metrics: MonitoringMetrics): void {
    const now = new Date();
    
    for (const alert of this.alerts.filter(a => !a.resolved)) {
      let shouldResolve = false;

      switch (alert.type) {
        case 'memory':
          shouldResolve = metrics.system.memoryUsage.heapUsagePercent < alert.threshold;
          break;
        case 'error_rate':
          shouldResolve = metrics.performance.errorRate < alert.threshold;
          break;
        case 'response_time':
          shouldResolve = metrics.performance.averageResponseTime < alert.threshold;
          break;
      }

      if (shouldResolve) {
        alert.resolved = now;
        this.logger.info('Alert resolved', {
          alertId: alert.id,
          type: alert.type,
          resolvedAt: alert.resolved
        });
      }
    }
  }

  /**
   * Get CPU usage
   */
  private async getCPUUsage(): Promise<number | undefined> {
    try {
      // This would typically use system monitoring libraries
      // For now, return undefined as it's optional
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Get load average
   */
  private getLoadAverage(): number[] | undefined {
    try {
      // This would typically use os.loadavg() on Unix systems
      // For now, return undefined as it's optional
      return undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Create minimal metrics on error
   */
  private createMinimalMetrics(): MonitoringMetrics {
    const memoryUsage = process.memoryUsage();

    return {
      timestamp: new Date(),
      system: {
        uptime: process.uptime() * 1000,
        memoryUsage: {
          heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          rssMB: Math.round(memoryUsage.rss / 1024 / 1024),
          heapUsagePercent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
        }
      },
      sessions: {
        total: 0,
        active: 0,
        byStatus: {},
        averageAge: 0,
        connectionSuccessRate: 0
      },
      connections: {
        poolSize: 0,
        activeConnections: 0,
        utilization: 0,
        averageUseCount: 0
      },
      rateLimiting: {
        userLimitsCount: 0,
        globalLimitsCount: 0
      },
      events: {
        totalEvents: 0,
        eventsByType: {},
        listenerCount: 0,
        errorCount: 0
      },
      performance: {
        averageResponseTime: 0,
        requestsPerSecond: 0,
        errorRate: 0
      }
    };
  }

  /**
   * Setup event listeners for performance tracking
   */
  private setupEventListeners(): void {
    // Listen for session events to track performance
    this.eventEmitter.addEventListener('session_event', (event) => {
      // Track session-related performance metrics
      this.recordRequest(100, event.type === 'error'); // Placeholder response time
    });

    // Listen for connection events
    this.eventEmitter.addEventListener('connection_update', (event) => {
      // Track connection-related metrics
      if (event.status === 'connected') {
        this.recordRequest(200, false);
      } else if (event.status === 'error') {
        this.recordRequest(500, true);
      }
    });

    // Listen for message events
    this.eventEmitter.addEventListener('message', (_event) => {
      // Track message processing performance
      this.recordRequest(50, false);
    });

    this.logger.debug('Event listeners setup for monitoring');
  }

  /**
   * Update alert configuration
   */
  updateAlertConfig(newConfig: Partial<AlertConfig>): void {
    Object.assign(this.alertConfig, newConfig);
    this.logger.info('Alert configuration updated', {
      newConfig: this.alertConfig
    });
  }

  /**
   * Clear metrics history
   */
  clearMetricsHistory(): void {
    this.metricsHistory.length = 0;
    this.logger.info('Metrics history cleared');
  }

  /**
   * Clear alerts
   */
  clearAlerts(): void {
    this.alerts.length = 0;
    this.logger.info('Alerts cleared');
  }

  /**
   * Export metrics to external system (placeholder)
   */
  async exportMetrics(format: 'json' | 'prometheus' = 'json'): Promise<string> {
    try {
      const metrics = await this.getCurrentMetrics();

      if (format === 'prometheus') {
        return this.formatPrometheusMetrics(metrics);
      }

      return JSON.stringify(metrics, null, 2);
    } catch (error) {
      this.logger.error('Failed to export metrics', {
        format,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Format metrics for Prometheus
   */
  private formatPrometheusMetrics(metrics: MonitoringMetrics): string {
    const lines: string[] = [];

    // System metrics
    lines.push(`# HELP whatsapp_memory_usage_percent Memory usage percentage`);
    lines.push(`# TYPE whatsapp_memory_usage_percent gauge`);
    lines.push(`whatsapp_memory_usage_percent ${metrics.system.memoryUsage.heapUsagePercent}`);

    lines.push(`# HELP whatsapp_sessions_total Total number of sessions`);
    lines.push(`# TYPE whatsapp_sessions_total gauge`);
    lines.push(`whatsapp_sessions_total ${metrics.sessions.total}`);

    lines.push(`# HELP whatsapp_sessions_active Active sessions`);
    lines.push(`# TYPE whatsapp_sessions_active gauge`);
    lines.push(`whatsapp_sessions_active ${metrics.sessions.active}`);

    lines.push(`# HELP whatsapp_events_total Total events processed`);
    lines.push(`# TYPE whatsapp_events_total counter`);
    lines.push(`whatsapp_events_total ${metrics.events.totalEvents}`);

    lines.push(`# HELP whatsapp_response_time_avg Average response time in milliseconds`);
    lines.push(`# TYPE whatsapp_response_time_avg gauge`);
    lines.push(`whatsapp_response_time_avg ${metrics.performance.averageResponseTime}`);

    return lines.join('\n');
  }

  /**
   * Get monitoring statistics
   */
  getStatistics(): {
    metricsHistorySize: number;
    alertsCount: number;
    activeAlertsCount: number;
    performanceCounters: any;
    alertConfig: AlertConfig;
  } {
    return {
      metricsHistorySize: this.metricsHistory.length,
      alertsCount: this.alerts.length,
      activeAlertsCount: this.getActiveAlerts().length,
      performanceCounters: { ...this.performanceCounters },
      alertConfig: { ...this.alertConfig }
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    this.metricsHistory.length = 0;
    this.alerts.length = 0;

    this.logger.info('MonitoringService destroyed');
  }
}
