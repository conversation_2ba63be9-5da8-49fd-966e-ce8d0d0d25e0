# Example: Integration with existing core module
# This shows how to update the core module to use the network_foundation module
# instead of the default VPC

# Create network foundation
module "network" {
  source = "../../"

  env                   = var.env
  vpc_cidr             = var.vpc_cidr
  public_subnet_count  = var.public_subnet_count
  private_subnet_count = var.private_subnet_count
  
  enable_nat_gateway   = var.enable_nat_gateway
  nat_gateway_strategy = var.nat_gateway_strategy
  
  tags = local.this.tags
  
  vpc_tags = {
    Name = "${var.env}-ezychat-vpc"
  }
  
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
    Type = "Public"
  }
  
  private_subnet_tags = {
    "kubernetes.io/role/internal-elb" = "1"
    Type = "Private"
  }
}

# Query the main domain ezychat.ai from master account
data "aws_route53_zone" "main_domain" {
  provider = aws.master
  name     = "ezychat.ai"
}

# Create ACM certificate for ALB (in the current region)
module "alb_certificate" {
  source = "../../../acm_certificate"

  domain_name     = "api.${var.env}.ezychat.ai"
  route53_zone_id = data.aws_route53_zone.main_domain.zone_id

  tags = merge(local.this.tags, {
    Name = "api.${var.env}.ezychat.ai-alb-certificate"
  })

  providers = {
    aws.certificate_region = aws
    aws.route53_region     = aws.master
  }
}

# Load services configuration from YAML
locals {
  services_config = yamldecode(
    fileexists("${path.root}/configs/ecs_services.yaml") ? 
    file("${path.root}/configs/ecs_services.yaml") : 
    file("${path.module}/configs/ecs_services.yaml")
  )
}

# Create the ECS App Platform using the network foundation
module "api_platform" {
  source   = "../../../ecs_app_platform"
  env      = var.env
  app_name = "api"

  # Use network foundation outputs instead of default VPC
  vpc_id             = module.network.vpc_id
  alb_subnet_ids     = module.network.public_subnet_ids
  service_subnet_ids = var.use_private_subnets ? module.network.private_subnet_ids : module.network.public_subnet_ids

  # Use services configuration from YAML
  services_config = local.services_config

  # HTTPS Configuration
  create_https_listener = true
  certificate_arn       = module.alb_certificate.certificate_arn
  ssl_policy            = "ELBSecurityPolicy-TLS13-1-2-2021-06"

  # HTTP to HTTPS redirect
  create_http_listener = true
  http_listener_type   = "redirect"

  # Default settings for all services
  default_assign_public_ip = var.use_private_subnets ? false : true

  task_role_policy_arns = [aws_iam_policy.ecs_tasks_secrets_read.arn]

  tags = merge(local.this.tags, {
    Project = "ECS App Platform"
  })

  depends_on = [module.alb_certificate]
}

# Additional variables needed for network configuration
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_count" {
  description = "Number of public subnets to create"
  type        = number
  default     = 2
}

variable "private_subnet_count" {
  description = "Number of private subnets to create"
  type        = number
  default     = 2
}

variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "nat_gateway_strategy" {
  description = "NAT Gateway deployment strategy: 'single' or 'per_az'"
  type        = string
  default     = "single"
}

variable "use_private_subnets" {
  description = "Whether to deploy ECS services in private subnets"
  type        = bool
  default     = true
}

# Network outputs
output "vpc_id" {
  value       = module.network.vpc_id
  description = "ID of the VPC"
}

output "public_subnet_ids" {
  value       = module.network.public_subnet_ids
  description = "List of public subnet IDs"
}

output "private_subnet_ids" {
  value       = module.network.private_subnet_ids
  description = "List of private subnet IDs"
}

output "nat_gateway_public_ips" {
  value       = module.network.nat_gateway_public_ips
  description = "Public IPs of NAT Gateways"
}
