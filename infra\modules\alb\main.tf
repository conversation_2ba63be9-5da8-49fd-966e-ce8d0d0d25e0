terraform {
  required_version = ">= 1.0.0"
}

locals {
  name = "${var.env}-${var.app_name}-alb"
}

# Create the ALB
resource "aws_lb" "this" {
  name               = local.name
  internal           = var.internal
  load_balancer_type = "application"
  security_groups    = var.security_group_ids != null ? var.security_group_ids : [aws_security_group.this[0].id]
  subnets            = var.subnet_ids != null ? var.subnet_ids : data.aws_subnets.default.ids

  enable_deletion_protection = var.enable_deletion_protection
  idle_timeout               = var.idle_timeout
  drop_invalid_header_fields = var.drop_invalid_header_fields
  enable_http2               = var.enable_http2

  dynamic "access_logs" {
    for_each = var.access_logs_bucket != null ? [1] : []
    content {
      bucket  = var.access_logs_bucket
      prefix  = var.access_logs_prefix != null ? var.access_logs_prefix : local.name
      enabled = true
    }
  }

  tags = merge(
    var.tags,
    {
      Name = local.name
    }
  )
}

# Create a default security group if none provided
resource "aws_security_group" "this" {
  count       = var.security_group_ids == null ? 1 : 0
  name        = "${local.name}-sg"
  description = "Security group for ${local.name} load balancer"
  vpc_id      = var.vpc_id != null ? var.vpc_id : data.aws_vpc.default.id

  # Allow HTTP traffic
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Allow HTTP traffic"
  }

  # Allow HTTPS traffic
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Allow HTTPS traffic"
  }

  # Allow outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name}-sg"
    }
  )
}

# Create HTTP listener if enabled
resource "aws_lb_listener" "http" {
  count             = var.create_http_listener ? 1 : 0
  load_balancer_arn = aws_lb.this.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = var.http_listener_type

    dynamic "redirect" {
      for_each = var.http_listener_type == "redirect" ? [1] : []
      content {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }

    dynamic "fixed_response" {
      for_each = var.http_listener_type == "fixed-response" ? [1] : []
      content {
        content_type = "text/plain"
        message_body = var.fixed_response_content
        status_code  = "200"
      }
    }

    # If type is forward, target group must be provided
    dynamic "forward" {
      for_each = var.http_listener_type == "forward" && var.default_target_group_arn != null ? [1] : []
      content {
        target_group {
          arn = var.default_target_group_arn
        }
      }
    }
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name}-http-listener"
    }
  )
}

# Create HTTPS listener if enabled and certificate provided
resource "aws_lb_listener" "https" {
  count             = var.create_https_listener ? 1 : 0
  load_balancer_arn = aws_lb.this.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = var.ssl_policy
  certificate_arn   = var.certificate_arn

  # Default action for the HTTPS listener
  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "No routes matched"
      status_code  = "404"
    }
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name}-https-listener"
    }
  )
}

# Add additional certificates to the HTTPS listener if provided
resource "aws_lb_listener_certificate" "additional_certs" {
  count           = var.create_https_listener && length(var.additional_certificate_arns) > 0 ? length(var.additional_certificate_arns) : 0
  listener_arn    = aws_lb_listener.https[0].arn
  certificate_arn = var.additional_certificate_arns[count.index]
}

# Create a default target group if none provided
resource "aws_lb_target_group" "default" {
  count       = var.default_target_group_arn == null && var.create_default_target_group ? 1 : 0
  name        = "${local.name}-default-tg"
  port        = var.target_group_port
  protocol    = var.target_group_protocol
  vpc_id      = var.vpc_id != null ? var.vpc_id : data.aws_vpc.default.id
  target_type = var.target_type

  health_check {
    enabled             = true
    interval            = var.health_check_interval
    path                = var.health_check_path
    port                = var.health_check_port
    protocol            = var.health_check_protocol
    timeout             = var.health_check_timeout
    healthy_threshold   = var.health_check_healthy_threshold
    unhealthy_threshold = var.health_check_unhealthy_threshold
    matcher             = var.health_check_matcher
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name}-default-tg"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}
