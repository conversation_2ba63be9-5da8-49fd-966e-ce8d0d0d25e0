# ECS Services Configuration for api.dev.anchorsprint.com
whatsapp-manager:
  container_image: "611032973328.dkr.ecr.ap-southeast-5.amazonaws.com/whatsapp-manager:latest"
  container_port: 3000
  path_patterns:
    - "/api/*"
    - "/health"
  health_check_path: "/health"
  task_cpu: 256
  task_memory: 512
  container_memory: 512
  assign_public_ip: true
  environment_variables:
    HOST: "0.0.0.0"
    PORT: "3000"
    NODE_ENV: "uat"
    AWS_REGION: "ap-southeast-5"
    LOG_LEVEL: "info"
    LOG_FORMAT: "json"
    CORS_ORIGINS: "*"
    DYNAMODB_TABLE_NAME: "WhatsAppSessions-uat"
  # Secrets from AWS Secrets Manager
  secrets:
    "JWT_SECRET": "arn:aws:secretsmanager:ap-southeast-5:611032973328:secret:uat/whatsapp-manager/jwt_token"
  desired_count: 1
  tags:
    Service: "WhatsApp Manager"
    Description: "WhatsApp API Middleware Foundation"
    schedule: "true"
ecs-python-sample:
  container_image: 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/ecs-python-sample:29
  container_port: 5000
  path_patterns:
    - "/python-test"
    - "/python-test/*"
  health_check_path: "/health"
  task_cpu: 256
  task_memory: 512
  container_memory: 512
  assign_public_ip: true
  environment_variables:
    SERVICE_NAME: "ecs-python"
    ENVIRONMENT: "uat"
    HOST: "0.0.0.0"
    PORT: "5000"
    PYTHONUNBUFFERED: "1"
  desired_count: 1
  tags:
    Service: "ECS Python Sample"
    Description: "Python Flask Sample Application for ECS"
    schedule: "true"
