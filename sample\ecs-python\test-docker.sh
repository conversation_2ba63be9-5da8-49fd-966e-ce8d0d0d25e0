#!/bin/bash

# EzyChat Python Flask Sample - Docker Test Script
set -e

IMAGE_NAME="ezychat-ecs-python"
CONTAINER_NAME="ezychat-python-test"
PORT="5000"

echo "🐍 EzyChat Python Flask Sample - Docker Test"
echo "=============================================="

# Clean up any existing container
echo "🧹 Cleaning up existing container..."
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME .

# Run the container
echo "🏃 Starting container on port $PORT..."
docker run -d \
  --name $CONTAINER_NAME \
  -p $PORT:5000 \
  -e SERVICE_NAME=ecs-python \
  -e ENVIRONMENT=development \
  -e HOST=0.0.0.0 \
  -e PORT=5000 \
  $IMAGE_NAME

# Wait for container to be ready with proper health check
echo "⏳ Waiting for container to be ready..."
sleep 3

# Check if container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
  echo "❌ Container failed to start!"
  docker logs $CONTAINER_NAME
  exit 1
fi

# Wait for Flask to be fully ready (check for gunicorn startup in logs)
echo "⏳ Waiting for Flask application to be fully initialized..."
for i in {1..30}; do
  if docker logs $CONTAINER_NAME 2>&1 | grep -E "(Listening at|Booting worker)" > /dev/null; then
    echo "✅ Flask application is ready"
    break
  elif [ $i -eq 30 ]; then
    echo "❌ Flask application failed to start properly"
    docker logs $CONTAINER_NAME
    exit 1
  else
    sleep 1
  fi
done

# Additional wait to ensure port binding is complete
sleep 2

# Function to test endpoint with retry
test_endpoint() {
  local endpoint=$1
  local name=$2
  local expected_content=$3
  echo -n "Testing $name... "
  
  for i in {1..10}; do
    if response=$(curl -s -f http://localhost:$PORT$endpoint 2>/dev/null); then
      # If expected content is provided, check for it
      if [ -n "$expected_content" ] && ! echo "$response" | grep -q "$expected_content"; then
        if [ $i -eq 10 ]; then
          echo "❌ FAILED (wrong content)"
          echo "Expected: $expected_content"
          echo "Got: $response"
          return 1
        fi
      else
        echo "✅ OK"
        echo "   Response: $(echo "$response" | head -c 100)..."
        return 0
      fi
    elif [ $i -eq 10 ]; then
      echo "❌ FAILED"
      echo "Debug info for $endpoint:"
      curl -v http://localhost:$PORT$endpoint || true
      docker logs $CONTAINER_NAME | tail -5
      return 1
    fi
    sleep 1
  done
}

# Test the endpoints
echo "🧪 Testing endpoints..."

# Test health endpoint
test_endpoint "/health" "/health" "healthy" || exit 1

# Test python-test endpoint (main requirement)
test_endpoint "/python-test" "/python-test" "Hello World" || exit 1

# Test root endpoint
test_endpoint "/" "/" "EzyChat Python Flask Sample" || exit 1

# Test status endpoint
test_endpoint "/status" "/status" "running" || exit 1

echo ""
echo "🎉 All tests passed!"
echo ""
echo "📊 Container info:"
echo "  Image: $IMAGE_NAME"
echo "  Container: $CONTAINER_NAME"
echo "  URL: http://localhost:$PORT"
echo ""
echo "🔗 Test endpoints:"
echo "  Health: http://localhost:$PORT/health"
echo "  Python Test: http://localhost:$PORT/python-test"
echo "  Main: http://localhost:$PORT/"
echo "  Status: http://localhost:$PORT/status"
echo ""

# Show sample responses
echo "📋 Sample responses:"
echo ""
echo "🔹 Health endpoint:"
curl -s http://localhost:$PORT/health | python3 -m json.tool || echo "Failed to get health response"
echo ""
echo "🔹 Python Test endpoint:"
curl -s http://localhost:$PORT/python-test | python3 -m json.tool || echo "Failed to get python-test response"
echo ""

# Keep container running for manual testing
read -p "Press Enter to stop and remove the container..."

# Clean up
echo "🧹 Stopping and removing container..."
docker rm -f $CONTAINER_NAME

echo "✅ Test completed successfully!"