variable "env" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Map of tags to add to resources"
  type        = map(string)
  default     = {}
}

# Network Configuration Variables
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_count" {
  description = "Number of public subnets to create"
  type        = number
  default     = 2

  validation {
    condition     = var.public_subnet_count >= 1 && var.public_subnet_count <= 6
    error_message = "Public subnet count must be between 1 and 6."
  }
}

variable "private_subnet_count" {
  description = "Number of private subnets to create"
  type        = number
  default     = 2

  validation {
    condition     = var.private_subnet_count >= 0 && var.private_subnet_count <= 6
    error_message = "Private subnet count must be between 0 and 6."
  }
}

variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "nat_gateway_strategy" {
  description = "NAT Gateway deployment strategy: 'single' for cost-optimized, 'per_az' for high availability"
  type        = string
  default     = "single"

  validation {
    condition     = contains(["single", "per_az"], var.nat_gateway_strategy)
    error_message = "NAT Gateway strategy must be either 'single' or 'per_az'."
  }
}

variable "use_private_subnets" {
  description = "Whether to deploy ECS services in private subnets (recommended for production)"
  type        = bool
  default     = false
}

# Domain Configuration Variables
variable "domain_name" {
  description = "The root domain name (e.g., ezychat.ai, mycompany.com)"
  type        = string
  default     = "ezychat.ai"
}

variable "api_subdomain" {
  description = "The API subdomain prefix (e.g., api)"
  type        = string
  default     = "api"
}

variable "app_subdomain" {
  description = "The App subdomain prefix (e.g., api)"
  type        = string
  default     = "app"
}
