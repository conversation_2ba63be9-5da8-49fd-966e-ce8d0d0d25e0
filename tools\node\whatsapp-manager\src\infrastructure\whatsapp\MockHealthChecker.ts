import { injectable } from 'tsyringe';

/**
 * Mock implementation of HealthChecker for testing
 * Always returns healthy status to avoid real service dependencies
 */
@injectable()
export class MockHealthChecker {
  async checkDatabase(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        connection: 'mock',
        responseTime: 1,
        totalSessions: 0
      }
    };
  }

  async checkMemory(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    const memUsage = process.memoryUsage();
    return {
      status: 'healthy',
      details: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        heapUsedMB: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memUsage.heapTotal / 1024 / 1024)
      }
    };
  }

  async checkSystem(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };
  }

  async checkServices(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        whatsapp: 'healthy',
        database: 'healthy',
        cache: 'healthy'
      }
    };
  }

  async performHealthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    checks: {
      database: { status: 'healthy' | 'unhealthy'; details?: any };
      memory: { status: 'healthy' | 'unhealthy'; details?: any };
      system: { status: 'healthy' | 'unhealthy'; details?: any };
      services: { status: 'healthy' | 'unhealthy'; details?: any };
    };
    timestamp: string;
  }> {
    const [database, memory, system, services] = await Promise.all([
      this.checkDatabase(),
      this.checkMemory(),
      this.checkSystem(),
      this.checkServices()
    ]);

    const allHealthy = [database, memory, system, services].every(check => check.status === 'healthy');

    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      checks: {
        database,
        memory,
        system,
        services
      },
      timestamp: new Date().toISOString()
    };
  }

  async getReadinessProbe(): Promise<{ status: 'ready' | 'not-ready'; details?: any }> {
    return {
      status: 'ready',
      details: {
        database: 'connected',
        services: 'initialized'
      }
    };
  }

  async getLivenessProbe(): Promise<{ status: 'alive' | 'dead'; details?: any }> {
    return {
      status: 'alive',
      details: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      }
    };
  }

  async getMetrics(): Promise<any> {
    return {
      activeSessions: 0,
      totalRequests: 0,
      errorRate: 0,
      averageResponseTime: 1,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  // Additional methods that the real HealthChecker has
  async getSystemHealth(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: { usage: 0.1 },
        disk: { usage: 0.5 }
      }
    };
  }

  async checkComponentHealth(componentName: string): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        component: componentName,
        lastCheck: new Date().toISOString(),
        responseTime: 1
      }
    };
  }

  // Cleanup method
  async destroy(): Promise<void> {
    // Mock cleanup - nothing to do
  }
}
