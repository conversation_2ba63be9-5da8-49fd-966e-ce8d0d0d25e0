import { Router } from 'express';
import { DIContainer } from '../../di/container';
import { HealthController } from '../controllers/health.controller';
import { rateLimitMiddleware } from '../middleware/rate-limit.middleware';

/**
 * Health check routes for production monitoring
 */
export function createHealthRoutes(): Router {
  const router = Router();
  const healthController = DIContainer.resolve<HealthController>('HealthController');

  /**
   * GET /api/health
   * Simple health check for load balancers
   */
  router.get('/',
    healthController.getHealth.bind(healthController)
  );

  /**
   * GET /api/health/detailed
   * Comprehensive health check with component details
   */
  router.get('/detailed',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute
      message: 'Too many detailed health check requests'
    }),
    healthController.getDetailedHealth.bind(healthController)
  );

  /**
   * GET /api/health/readiness
   * Kubernetes readiness probe
   */
  router.get('/readiness',
    healthController.getReadiness.bind(healthController)
  );

  /**
   * GET /api/health/liveness
   * Kubernetes liveness probe
   */
  router.get('/liveness',
    healthController.getLiveness.bind(healthController)
  );

  /**
   * GET /api/health/metrics
   * System metrics endpoint
   */
  router.get('/metrics',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60, // 60 requests per minute
      message: 'Too many metrics requests'
    }),
    healthController.getMetrics.bind(healthController)
  );

  /**
   * GET /api/health/monitoring
   * Monitoring data and alerts
   */
  router.get('/monitoring',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute
      message: 'Too many monitoring requests'
    }),
    healthController.getMonitoring.bind(healthController)
  );

  /**
   * GET /api/health/monitoring/alerts
   * Get system alerts
   */
  router.get('/monitoring/alerts',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60, // 60 requests per minute
      message: 'Too many alert requests'
    }),
    healthController.getAlerts.bind(healthController)
  );

  /**
   * GET /api/health/cleanup/statistics
   * Get cleanup service statistics
   */
  router.get('/cleanup/statistics',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute
      message: 'Too many cleanup statistics requests'
    }),
    healthController.getCleanupStatistics.bind(healthController)
  );

  /**
   * POST /api/health/cleanup
   * Trigger system cleanup (admin endpoint)
   */
  router.post('/cleanup',
    rateLimitMiddleware({
      windowMs: 10 * 60 * 1000, // 10 minutes
      maxRequests: 5, // 5 cleanup requests per 10 minutes
      message: 'Cleanup operations are heavily rate limited'
    }),
    healthController.triggerCleanup.bind(healthController)
  );

  /**
   * GET /api/health/component/:componentName
   * Check specific component health
   */
  router.get('/component/:componentName',
    rateLimitMiddleware({
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30, // 30 requests per minute
      message: 'Too many component health check requests'
    }),
    healthController.checkComponent.bind(healthController)
  );

  return router;
}
