import 'reflect-metadata';

// E2E test setup
beforeAll(async () => {
  // Set test environment variables for e2e tests
  process.env['NODE_ENV'] = 'test';
  process.env['PORT'] = '3001'; // Use different port for e2e tests
  process.env['JWT_SECRET'] = 'test-secret-key-123-minimum-32-characters-required';
  process.env['DYNAMODB_TABLE_NAME'] = 'WhatsAppSessions-e2e-test';
  process.env['AWS_REGION'] = 'ap-southeast-1';
  process.env['LOG_LEVEL'] = 'error'; // Reduce log noise in tests
  process.env['LOG_FORMAT'] = 'json';
  process.env['CORS_ORIGINS'] = '*';
  process.env['RATE_LIMIT_WINDOW_MS'] = '60000';
  process.env['RATE_LIMIT_MAX_REQUESTS'] = '1000';
});

afterAll(async () => {
  // Cleanup after all e2e tests
});

// Increase timeout for e2e tests
jest.setTimeout(30000);
