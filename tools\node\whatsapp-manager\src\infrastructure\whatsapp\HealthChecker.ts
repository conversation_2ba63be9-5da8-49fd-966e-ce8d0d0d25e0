import { inject, injectable } from 'tsyringe';

import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { ISessionRepository } from '../../domain/repositories/ISessionRepository';

/**
 * Health check status
 */
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy';

/**
 * Health check result
 */
export interface HealthCheckResult {
  status: HealthStatus;
  timestamp: Date;
  responseTime: number;
  details?: any;
  error?: string;
}

/**
 * Component health check
 */
export interface ComponentHealth {
  name: string;
  status: HealthStatus;
  lastCheck: Date;
  responseTime: number;
  details: any;
  error?: string;
}

/**
 * Overall system health
 */
export interface SystemHealth {
  status: HealthStatus;
  timestamp: Date;
  uptime: number;
  version: string;
  components: ComponentHealth[];
  metrics: {
    activeSessions: number;
    totalSessions: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage?: number;
  };
}

/**
 * Health checker for WhatsApp Manager system
 * Monitors various components and provides health status
 */
@injectable()
export class HealthChecker {
  private readonly startTime: Date;
  private readonly checkInterval: number;
  private readonly componentChecks = new Map<string, ComponentHealth>();
  private readonly healthCheckInterval: NodeJS.Timeout;
  private lastSystemHealth: SystemHealth | null = null;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService,
    @inject('ISessionRepository') private sessionRepository: ISessionRepository
  ) {
    this.startTime = new Date();
    this.checkInterval = this.config.getOptional('HEALTH_CHECK_INTERVAL_MS', 30000);

    // Start periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, this.checkInterval);

    // Perform initial health check
    this.performHealthChecks();
  }

  /**
   * Get current system health
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const startTime = Date.now();

    try {
      // Get component health
      const components = Array.from(this.componentChecks.values());

      // Get metrics
      const metrics = await this.getSystemMetrics();

      // Determine overall status
      const status = this.determineOverallStatus(components);

      const systemHealth: SystemHealth = {
        status,
        timestamp: new Date(),
        uptime: Date.now() - this.startTime.getTime(),
        version: this.config.getOptional('npm_package_version', '1.0.0'),
        components,
        metrics
      };

      this.lastSystemHealth = systemHealth;

      this.logger.debug('System health check completed', {
        status,
        componentCount: components.length,
        responseTime: Date.now() - startTime
      });

      return systemHealth;
    } catch (error) {
      this.logger.error('Failed to get system health', {
        error: (error as Error).message
      });

      return {
        status: 'unhealthy',
        timestamp: new Date(),
        uptime: Date.now() - this.startTime.getTime(),
        version: this.config.getOptional('npm_package_version', '1.0.0'),
        components: [],
        metrics: {
          activeSessions: 0,
          totalSessions: 0,
          memoryUsage: process.memoryUsage()
        }
      };
    }
  }

  /**
   * Check specific component health
   */
  async checkComponentHealth(componentName: string): Promise<ComponentHealth> {
    const startTime = Date.now();

    try {
      let result: HealthCheckResult;

      switch (componentName) {
        case 'database':
          result = await this.checkDatabaseHealth();
          break;
        case 'memory':
          result = await this.checkMemoryHealth();
          break;
        case 'sessions':
          result = await this.checkSessionsHealth();
          break;
        case 'connections':
          result = await this.checkConnectionsHealth();
          break;
        default:
          throw new Error(`Unknown component: ${componentName}`);
      }

      const componentHealth: ComponentHealth = {
        name: componentName,
        status: result.status,
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        details: result.details || {},
        error: result.error
      };

      this.componentChecks.set(componentName, componentHealth);

      return componentHealth;
    } catch (error) {
      const componentHealth: ComponentHealth = {
        name: componentName,
        status: 'unhealthy',
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        details: {},
        error: (error as Error).message
      };

      this.componentChecks.set(componentName, componentHealth);

      return componentHealth;
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      // Try to perform a simple database operation
      // const testSession = await this.sessionRepository.findByUserId('health-check-test'); // Unused for now
      
      return {
        status: 'healthy',
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        details: {
          connected: true,
          testQuery: 'success'
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        error: (error as Error).message,
        details: {
          connected: false
        }
      };
    }
  }

  /**
   * Check memory health
   */
  private async checkMemoryHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const memoryUsage = process.memoryUsage();
    
    // Convert to MB
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
    const rssMB = memoryUsage.rss / 1024 / 1024;

    // Define thresholds
    const heapUsagePercent = (heapUsedMB / heapTotalMB) * 100;
    const rssThresholdMB = this.config.getOptional('MEMORY_THRESHOLD_MB', 1024);

    let status: HealthStatus = 'healthy';
    
    if (heapUsagePercent > 90 || rssMB > rssThresholdMB) {
      status = 'unhealthy';
    } else if (heapUsagePercent > 75 || rssMB > rssThresholdMB * 0.8) {
      status = 'degraded';
    }

    return {
      status,
      timestamp: new Date(),
      responseTime: Date.now() - startTime,
      details: {
        heapUsedMB: Math.round(heapUsedMB),
        heapTotalMB: Math.round(heapTotalMB),
        heapUsagePercent: Math.round(heapUsagePercent),
        rssMB: Math.round(rssMB),
        externalMB: Math.round(memoryUsage.external / 1024 / 1024),
        arrayBuffersMB: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
      }
    };
  }

  /**
   * Check sessions health
   */
  private async checkSessionsHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      const sessions = await this.sessionRepository.findAllSessions();
      const activeSessions = sessions.filter(s => s.status === 'connected').length;
      const totalSessions = sessions.length;

      let status: HealthStatus = 'healthy';
      
      // Check if too many sessions are in error state
      const errorSessions = sessions.filter(s => s.status === 'error').length;
      const errorRate = totalSessions > 0 ? (errorSessions / totalSessions) * 100 : 0;

      if (errorRate > 50) {
        status = 'unhealthy';
      } else if (errorRate > 25) {
        status = 'degraded';
      }

      return {
        status,
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        details: {
          totalSessions,
          activeSessions,
          errorSessions,
          errorRate: Math.round(errorRate),
          sessionsByStatus: this.groupSessionsByStatus(sessions)
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        error: (error as Error).message
      };
    }
  }

  /**
   * Check connections health (placeholder for connection manager integration)
   */
  private async checkConnectionsHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    // This would integrate with BaileysConnectionManager when available
    return {
      status: 'healthy',
      timestamp: new Date(),
      responseTime: Date.now() - startTime,
      details: {
        note: 'Connection health check not yet implemented'
      }
    };
  }

  /**
   * Get system metrics
   */
  private async getSystemMetrics(): Promise<SystemHealth['metrics']> {
    try {
      const sessions = await this.sessionRepository.findAllSessions();
      const activeSessions = sessions.filter(s => s.status === 'connected').length;

      return {
        activeSessions,
        totalSessions: sessions.length,
        memoryUsage: process.memoryUsage()
      };
    } catch (error) {
      this.logger.error('Failed to get system metrics', {
        error: (error as Error).message
      });

      return {
        activeSessions: 0,
        totalSessions: 0,
        memoryUsage: process.memoryUsage()
      };
    }
  }

  /**
   * Determine overall system status based on component health
   */
  private determineOverallStatus(components: ComponentHealth[]): HealthStatus {
    if (components.length === 0) {
      return 'degraded';
    }

    const unhealthyCount = components.filter(c => c.status === 'unhealthy').length;
    const degradedCount = components.filter(c => c.status === 'degraded').length;

    if (unhealthyCount > 0) {
      return 'unhealthy';
    }

    if (degradedCount > 0) {
      return 'degraded';
    }

    return 'healthy';
  }

  /**
   * Group sessions by status
   */
  private groupSessionsByStatus(sessions: any[]): { [status: string]: number } {
    const grouped: { [status: string]: number } = {};
    
    for (const session of sessions) {
      const status = session.status || 'unknown';
      grouped[status] = (grouped[status] || 0) + 1;
    }

    return grouped;
  }

  /**
   * Perform all health checks
   */
  private async performHealthChecks(): Promise<void> {
    try {
      const components = ['database', 'memory', 'sessions', 'connections'];
      
      await Promise.allSettled(
        components.map(component => this.checkComponentHealth(component))
      );

      this.logger.debug('Periodic health checks completed');
    } catch (error) {
      this.logger.error('Failed to perform health checks', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Get last cached system health
   */
  getLastSystemHealth(): SystemHealth | null {
    return this.lastSystemHealth;
  }

  /**
   * Check if system is healthy
   */
  isHealthy(): boolean {
    return this.lastSystemHealth?.status === 'healthy';
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.componentChecks.clear();
    this.logger.info('HealthChecker destroyed');
  }
}
