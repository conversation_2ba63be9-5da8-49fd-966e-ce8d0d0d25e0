# AWS ECS Application Platform Module

This module creates a complete ECS application platform with multiple services and path-based routing. It sets up an ECS cluster, Application Load Balancer, and multiple ECS services with different path patterns.

## Features

- Creates an ECS cluster
- Sets up an Application Load Balancer (ALB)
- Configures multiple ECS services with different container images
- Implements path-based routing to direct traffic to the appropriate service
- Supports HTTPS with SSL certificates and HTTP-to-HTTPS redirection
- Supports multi-domain HTTPS with additional certificates
- Supports custom health checks, CPU/memory settings, and environment variables for each service
- Comprehensive logging and monitoring
- Flexible deployment options

## Usage

### Minimal Service Configuration

```hcl
module "minimal_api_platform" {
  source   = "../../modules/ecs_app_platform"
  env      = "dev"
  app_name = "api"

  # SSL certificate for HTTPS
  certificate_arn = "arn:aws:acm:us-east-1:************:certificate/example"

  # Configure a service with minimal settings
  services_config = {
    # Simple nginx service
    test = {
      container_image = "nginx:latest"
      path_patterns   = ["/*"]
      health_check_path = "/health"
      # Uses default minimum CPU (256) and memory (512) for Fargate
      command = [
        "/bin/sh",
        "-c",
        "echo 'server { listen 80; location / { root /usr/share/nginx/html; } location /health { return 200 \"OK\"; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
      ]
    }
  }

  tags = {
    Environment = "Development"
  }
}
```

### Basic Usage with Multiple Services

```hcl
module "api_platform" {
  source   = "../../modules/ecs_app_platform"
  env      = "prod"
  app_name = "api"

  # SSL certificate for HTTPS
  certificate_arn = "arn:aws:acm:us-east-1:************:certificate/example"

  # Configure multiple services with different paths
  services_config = {
    # User API Service
    users = {
      container_image = "my-user-api:latest"
      container_port  = 8080
      path_patterns   = ["/api/users*"]
      health_check_path = "/api/users/health"
      task_cpu        = 512
      task_memory     = 1024
    },

    # Products API Service
    products = {
      container_image = "my-product-api:latest"
      container_port  = 8080
      path_patterns   = ["/api/products*"]
      health_check_path = "/api/products/health"
      task_cpu        = 1024
      task_memory     = 2048
      environment_variables = {
        DB_HOST = "products-db.example.com"
        LOG_LEVEL = "info"
      }
    },

    # Frontend Web Service
    web = {
      container_image = "my-web-frontend:latest"
      container_port  = 80
      path_patterns   = ["/*"]
      listener_rule_priority = 200  # Lower priority (higher number) than API services
      health_check_path = "/health"
    }
  }

  tags = {
    Environment = "Production"
    ManagedBy   = "Terraform"
  }
}
```

## ECS Services Configuration

The `services_config` parameter accepts a map of service configurations. Each service is defined with various configuration options for the container, task, routing, health checks, and more.

For detailed information on how to configure the ECS services, please refer to the [ECS Services Configuration Guide](../../iac/aws-ezychat-uat-tf/configs/README.md).

## Configuration Options

### HTTPS Configuration

- `create_https_listener` - Whether to create an HTTPS listener (default: true)
- `certificate_arn` - ARN of the primary SSL certificate for HTTPS
- `additional_certificate_arns` - List of additional certificate ARNs for multi-domain support
- `ssl_policy` - SSL policy for the HTTPS listener (default: "ELBSecurityPolicy-TLS13-1-2-2021-06")
- `use_https_for_target_groups` - Whether to use HTTPS for target groups (default: false)
- `create_http_listener` - Whether to create an HTTP listener (default: true)
- `http_listener_type` - Type of HTTP listener: "redirect", "forward", or "fixed-response" (default: "redirect")

## Requirements

| Name      | Version  |
| --------- | -------- |
| terraform | >= 1.0.0 |
| aws       | >= 5.0.0 |

## Inputs

See the `vars.tf` file for a complete list of input variables.

## Outputs

| Name                    | Description                             |
| ----------------------- | --------------------------------------- |
| alb_id                  | The ID of the ALB                       |
| alb_arn                 | The ARN of the ALB                      |
| alb_dns_name            | The DNS name of the ALB                 |
| alb_zone_id             | The canonical hosted zone ID of the ALB |
| http_listener_arn       | The ARN of the HTTP listener            |
| https_listener_arn      | The ARN of the HTTPS listener           |
| alb_security_group_id   | The ID of the ALB security group        |
| cluster_id              | The ID of the ECS cluster               |
| cluster_arn             | The ARN of the ECS cluster              |
| cluster_name            | The name of the ECS cluster             |
| services                | Map of service details                  |
| task_definitions        | Map of task definition ARNs             |
| target_groups           | Map of target group ARNs                |
| ecs_security_group_id   | The ID of the ECS tasks security group  |
| task_execution_role_arn | The ARN of the task execution role      |
| task_role_arn           | The ARN of the task role                |
| log_groups              | Map of CloudWatch log group names       |
| listener_rules          | Map of listener rule ARNs               |
