# Lambda App Platform for ${local.api_domain_name}
# This creates a single API Gateway that routes traffic to multiple Lambda functions

# Load lambda services configuration from YAML
locals {
  lambda_services_config = yamldecode(file("${path.module}/configs/lambda_services.yaml"))
}

# Create the Lambda App Platform
module "lambda_platform" {
  source = "../../modules/lambda_app_platform"

  env      = var.env
  app_name = "lambda-api"

  # Use lambda services configuration from YAML
  lambda_services_config = local.lambda_services_config

  # Domain Configuration
  # Production: api.ezychat.ai
  # UAT: api.uat.ezychat.ai
  domain_name     = local.api_domain_name
  certificate_arn = module.alb_certificate.certificate_arn
  route53_zone_id = data.aws_route53_zone.main_domain.zone_id

  # API Gateway Configuration
  api_stage_name = "api"

  tags = merge(local.this.tags, {
    Name     = "${local.api_domain_name}-lambda-platform"
    Platform = "Lambda"
    Domain   = local.api_domain_name
  })

  providers = {
    aws.route53_region = aws.master
  }
}

# Output Lambda Platform information
output "lambda_platform" {
  description = "Lambda App Platform outputs"
  value = {
    api_gateway_url       = module.lambda_platform.api_gateway_url
    custom_domain_name    = module.lambda_platform.custom_domain_name
    lambda_function_arns  = module.lambda_platform.lambda_function_arns
    lambda_function_names = module.lambda_platform.lambda_function_names
    api_endpoints         = module.lambda_platform.api_endpoints
    api_routes            = module.lambda_platform.api_routes
  }
}
