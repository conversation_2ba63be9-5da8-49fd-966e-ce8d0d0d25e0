# Python Shared Libraries

Shared Python utilities, schemas, and common functionality used across all Python services in the ezychat platform.

## Purpose

This library provides reusable components that standardize common operations across Python services:
- Data models and schemas
- Error handling and logging
- Database utilities
- API response formatting
- Authentication helpers
- Configuration management

## Modules

### `schemas/`
Pydantic models for data validation and serialization:
- `message.py` - Message models (text, media, webhook payloads)
- `conversation.py` - Conversation and context models
- `user.py` - User and contact models
- `response.py` - Standard API response models

### `utils/`
Utility functions and helpers:
- `logger.py` - Standardized logging configuration
- `auth.py` - JWT token validation and user authentication
- `database.py` - Database connection and query helpers
- `cache.py` - Redis caching utilities
- `file_handler.py` - File upload/download utilities

### `exceptions/`
Custom exception classes:
- `base.py` - Base exception classes
- `api.py` - API-specific exceptions
- `validation.py` - Data validation exceptions

### `middleware/`
Common middleware components:
- `error_handler.py` - Global error handling
- `rate_limiter.py` - Rate limiting middleware
- `cors.py` - CORS configuration

## Example Usage

```python
# Import shared schemas
from ezychat_libs.schemas.message import MessageCreate, MessageResponse
from ezychat_libs.schemas.response import APIResponse

# Import utilities
from ezychat_libs.utils.logger import get_logger
from ezychat_libs.utils.auth import verify_jwt_token

# Setup logging
logger = get_logger(__name__)

# Use shared models
async def process_message(message_data: MessageCreate) -> APIResponse[MessageResponse]:
    try:
        # Process message logic here
        result = await process_logic(message_data)
        
        return APIResponse(
            success=True,
            data=MessageResponse(**result),
            message="Message processed successfully"
        )
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        return APIResponse(
            success=False,
            error=str(e),
            message="Failed to process message"
        )
```

## Installation

```bash
# Install in development mode for local development
pip install -e libs/python/

# Or install specific version in production
pip install ezychat-libs==1.0.0
```

## Development

```bash
# Setup development environment
cd libs/python
python -m venv venv
source venv/bin/activate
pip install -e .[dev]

# Run tests
pytest tests/

# Run linting
flake8 src/
black src/
isort src/

# Type checking
mypy src/
```

## Configuration

The library uses environment variables for configuration:

- `LOG_LEVEL` - Logging level (DEBUG, INFO, WARNING, ERROR)
- `JWT_SECRET_KEY` - Secret key for JWT token validation
- `DATABASE_URL` - Database connection string
- `REDIS_URL` - Redis connection string

## Publishing

```bash
# Build package
python -m build

# Publish to PyPI (production)
twine upload dist/*

# Publish to test PyPI
twine upload --repository-url https://test.pypi.org/legacy/ dist/*
```

## Versioning

This library follows semantic versioning (SemVer):
- MAJOR version for incompatible API changes
- MINOR version for backwards-compatible functionality additions
- PATCH version for backwards-compatible bug fixes