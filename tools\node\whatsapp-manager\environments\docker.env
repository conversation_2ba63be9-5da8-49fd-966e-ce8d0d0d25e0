# Docker Environment Configuration
# Used by docker-compose for container environment variables

NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# AWS Configuration
AWS_REGION=ap-southeast-1
# Local development credentials (required for DynamoDB Local)
AWS_ACCESS_KEY_ID=fakeMyKeyId
AWS_SECRET_ACCESS_KEY=fakeSecretAccessKey

# DynamoDB Local (Docker service name)
DYNAMODB_ENDPOINT=http://dynamodb-local:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev
AUTH_TOKEN_TABLE_NAME=AuthTokens-dev

# JWT Configuration for Docker development
JWT_SECRET=dev-super-secret-key-123-change-in-production-minimum-32-chars
JWT_ISSUER=ezychat-whatsapp-manager
JWT_AUDIENCE=ezychat-client

# QR Token Configuration
QR_TOKEN_EXPIRY_SEC=300
QR_TOKEN_MAX_ACTIVE=5
QR_AUTH_BASE_URL=http://localhost:3000

# CORS - Allow all origins in Docker development
CORS_ORIGINS=*

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty

# WhatsApp Configuration
MAX_CONCURRENT_SESSIONS=100
RECONNECT_MAX_ATTEMPTS=5
CONNECTION_TIMEOUT_MS=60000
CONNECTION_MAX_IDLE_TIME_MS=1800000
CONNECTION_MAX_AGE_MS=86400000

# Rate Limiting Configuration
RATE_LIMIT_MESSAGES_PER_MIN=60
RATE_LIMIT_GLOBAL_MESSAGES_PER_MIN=1000
RATE_LIMIT_QR_GENERATION_PER_5MIN=5
RATE_LIMIT_SESSION_CREATION_PER_HOUR=10

# Monitoring & Health Configuration
MONITORING_ALERTS_ENABLED=true
HEALTH_CHECK_INTERVAL_MS=30000
METRICS_COLLECTION_INTERVAL_MS=60000
ALERT_MEMORY_THRESHOLD=85
ALERT_CPU_THRESHOLD=80
ALERT_ERROR_RATE_THRESHOLD=5

# Cleanup Configuration
SESSION_CLEANUP_INTERVAL_MS=3600000
EXPIRED_SESSIONS_MAX_AGE_MS=604800000
DISCONNECTED_SESSIONS_MAX_AGE_MS=86400000
ERROR_SESSIONS_MAX_AGE_MS=21600000

# Webhook Configuration
WEBHOOK_MAX_QUEUE_SIZE=10000
WEBHOOK_DEFAULT_TIMEOUT_MS=30000
WEBHOOK_PROCESSING_INTERVAL_MS=5000
WEBHOOK_CONCURRENCY=5

# Message Processing Configuration
MESSAGE_CACHE_SIZE=10000

# Encryption Configuration
ENCRYPTION_KEY=dev-encryption-key-32-chars-min-change-in-production

# Development features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
