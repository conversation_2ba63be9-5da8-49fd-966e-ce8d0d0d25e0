#!/bin/bash

# Lambda Node.js Sample - ZIP Deployment Script
# Deploys Lambda function using ZIP package to ap-southeast-5 region

set -e

# Variables
FUNCTION_NAME="lambda-nodejs-sample"
AWS_REGION="ap-southeast-5"
ROLE_NAME="lambda-execution-role"

echo "🚀 Lambda Node.js Sample - ZIP Deployment"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "Error: AWS CLI not configured or credentials not set"
    exit 1
fi

echo "✓ AWS credentials configured"

# Create deployment package
echo "Creating deployment package..."
rm -f lambda-deployment.zip
zip -r lambda-deployment.zip index.js package.json

echo "✓ Deployment package created: lambda-deployment.zip"

# Check if IAM role exists, create if not
echo "Checking IAM role: $ROLE_NAME"
if ! aws iam get-role --role-name $ROLE_NAME > /dev/null 2>&1; then
    echo "Creating IAM role: $ROLE_NAME"
    
    # Create trust policy
    cat > trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    aws iam create-role \
        --role-name $ROLE_NAME \
        --assume-role-policy-document file://trust-policy.json \
        --region $AWS_REGION

    # Attach basic execution policy
    aws iam attach-role-policy \
        --role-name $ROLE_NAME \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole \
        --region $AWS_REGION

    # Add ECR permissions for container image deployment
    cat > ecr-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "ecr:BatchCheckLayerAvailability"
      ],
      "Resource": "*"
    }
  ]
}
EOF

    aws iam put-role-policy \
        --role-name $ROLE_NAME \
        --policy-name "lambda-ecr-access-policy" \
        --policy-document file://ecr-policy.json

    rm -f ecr-policy.json

    echo "✓ IAM role created with ECR permissions: $ROLE_NAME"
    
    # Wait for role to be available
    echo "Waiting for IAM role to be available..."
    sleep 10
else
    echo "✓ IAM role exists: $ROLE_NAME"
fi

CURRENT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
ROLE_ARN="arn:aws:iam::$CURRENT_ACCOUNT:role/$ROLE_NAME"

# Check if Lambda function exists
echo "Checking Lambda function: $FUNCTION_NAME"
if aws lambda get-function --function-name $FUNCTION_NAME --region $AWS_REGION > /dev/null 2>&1; then
    echo "Updating existing Lambda function..."
    aws lambda update-function-code \
        --function-name $FUNCTION_NAME \
        --zip-file fileb://lambda-deployment.zip \
        --region $AWS_REGION
    echo "✓ Lambda function updated: $FUNCTION_NAME"
else
    echo "Creating new Lambda function..."
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --runtime nodejs18.x \
        --role $ROLE_ARN \
        --handler index.handler \
        --zip-file fileb://lambda-deployment.zip \
        --timeout 30 \
        --memory-size 512 \
        --region $AWS_REGION
    echo "✓ Lambda function created: $FUNCTION_NAME"
fi

# Test the function
echo "Testing Lambda function..."
echo '{"path":"/sample","httpMethod":"GET"}' > test-payload.json
aws lambda invoke \
    --function-name $FUNCTION_NAME \
    --payload file://test-payload.json \
    --region $AWS_REGION \
    response.json

echo "✓ Lambda function invoked successfully"
echo "Response:"
cat response.json
echo ""

# Test health endpoint
echo "Testing health endpoint..."
echo '{"path":"/health","httpMethod":"GET"}' > health-payload.json
aws lambda invoke \
    --function-name $FUNCTION_NAME \
    --payload file://health-payload.json \
    --region $AWS_REGION \
    health-response.json

echo "✓ Health endpoint tested"
echo "Health Response:"
cat health-response.json
echo ""

# Clean up temporary files
rm -f trust-policy.json lambda-deployment.zip test-payload.json health-payload.json

echo "🎉 Deployment Complete!"
echo "Function Name: $FUNCTION_NAME"
echo "Region: $AWS_REGION"
echo "Role ARN: $ROLE_ARN"
echo ""
echo "Test the function:"
echo "aws lambda invoke --function-name $FUNCTION_NAME --payload '{}' --region $AWS_REGION response.json"
echo "cat response.json"
