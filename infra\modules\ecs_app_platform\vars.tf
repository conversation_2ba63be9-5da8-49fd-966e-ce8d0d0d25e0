variable "env" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "app_name" {
  description = "Application name"
  type        = string
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

# Services Configuration
variable "services_config" {
  description = "Configuration for each service in the ECS application"
  type = map(object({
    # Container configuration
    container_image              = string
    container_port               = optional(number)
    container_cpu                = optional(number)
    container_memory             = optional(number)
    container_memory_reservation = optional(number)
    environment_variables        = optional(map(string), {})
    secrets                      = optional(map(string), {})
    command                      = optional(list(string))

    # Task configuration
    task_cpu    = optional(number)
    task_memory = optional(number)

    # Service configuration
    desired_count    = optional(number)
    assign_public_ip = optional(bool)

    # Routing configuration
    path_patterns          = list(string)
    host_headers           = optional(list(string))
    listener_rule_priority = optional(number)

    # Health check configuration
    health_check_path                 = optional(string)
    health_check_interval             = optional(number)
    health_check_timeout              = optional(number)
    health_check_healthy_threshold    = optional(number)
    health_check_unhealthy_threshold  = optional(number)
    health_check_matcher              = optional(string)
    health_check_grace_period_seconds = optional(number)

    # Deployment configuration
    enable_circuit_breaker          = optional(bool)
    enable_circuit_breaker_rollback = optional(bool)

    # Logging configuration
    log_retention_days = optional(number)

    # Additional tags
    tags = optional(map(string), {})
  }))
}

# VPC and Network Configuration
variable "vpc_id" {
  description = "VPC ID where the resources will be deployed. If not specified, the default VPC will be used."
  type        = string
  default     = null
}

variable "alb_subnet_ids" {
  description = "A list of subnet IDs for the ALB. If not specified, the default VPC's subnets will be used."
  type        = list(string)
  default     = null
}

variable "service_subnet_ids" {
  description = "A list of subnet IDs for the ECS services. If not specified, the default VPC's subnets will be used."
  type        = list(string)
  default     = null
}

variable "alb_security_group_ids" {
  description = "A list of security group IDs to assign to the ALB. If not specified, a new security group will be created."
  type        = list(string)
  default     = null
}

variable "default_assign_public_ip" {
  description = "Default setting for whether to assign a public IP to the ECS tasks"
  type        = bool
  default     = false
}

# ALB Configuration
variable "internal_alb" {
  description = "Whether the ALB is internal"
  type        = bool
  default     = false
}

variable "enable_deletion_protection" {
  description = "Whether to enable deletion protection for the ALB"
  type        = bool
  default     = false
}

variable "idle_timeout" {
  description = "The time in seconds that the connection is allowed to be idle"
  type        = number
  default     = 60
}

variable "drop_invalid_header_fields" {
  description = "Whether to drop invalid header fields"
  type        = bool
  default     = true
}

variable "enable_http2" {
  description = "Whether to enable HTTP/2"
  type        = bool
  default     = true
}

variable "create_http_listener" {
  description = "Whether to create an HTTP listener"
  type        = bool
  default     = true
}

variable "http_listener_type" {
  description = "The type of HTTP listener (forward, redirect, fixed-response)"
  type        = string
  default     = "redirect"
}

variable "fixed_response_content" {
  description = "The content for fixed-response action"
  type        = string
  default     = "OK"
}

variable "create_https_listener" {
  description = "Whether to create an HTTPS listener"
  type        = bool
  default     = true
}

variable "certificate_arn" {
  description = "The ARN of the primary SSL certificate for HTTPS"
  type        = string
  default     = null
}

variable "additional_certificate_arns" {
  description = "List of additional certificate ARNs for HTTPS (for multi-domain support)"
  type        = list(string)
  default     = []
}

variable "ssl_policy" {
  description = "The SSL policy for the HTTPS listener"
  type        = string
  default     = "ELBSecurityPolicy-TLS13-1-2-2021-06"
}

variable "use_https_for_target_groups" {
  description = "Whether to use HTTPS protocol for target groups when HTTPS is enabled"
  type        = bool
  default     = false
}

variable "create_default_target_group" {
  description = "Whether to create a default target group for the ALB"
  type        = bool
  default     = false
}

variable "access_logs_bucket" {
  description = "The S3 bucket for ALB access logs"
  type        = string
  default     = null
}

variable "access_logs_prefix" {
  description = "The S3 bucket prefix for ALB access logs"
  type        = string
  default     = null
}

# ECS Cluster Configuration
variable "enable_container_insights" {
  description = "Whether to enable CloudWatch Container Insights for the ECS cluster"
  type        = bool
  default     = true
}

# Default Task and Container Configuration
variable "default_task_cpu" {
  description = "Default CPU units for ECS tasks (minimum value for Fargate is 256)"
  type        = number
  default     = 256 # Minimum value for Fargate
}

variable "default_task_memory" {
  description = "Default memory for ECS tasks in MiB (minimum value for Fargate is 512)"
  type        = number
  default     = 512 # Minimum value for Fargate
}

variable "default_container_port" {
  description = "Default container port"
  type        = number
  default     = 80
}

variable "default_desired_count" {
  description = "Default number of task instances to run"
  type        = number
  default     = 1
}

# Default Health Check Configuration
variable "default_health_check_path" {
  description = "Default health check path"
  type        = string
  default     = "/"
}

variable "default_health_check_interval" {
  description = "Default health check interval (seconds)"
  type        = number
  default     = 30
}

variable "default_health_check_timeout" {
  description = "Default health check timeout (seconds)"
  type        = number
  default     = 5
}

variable "default_health_check_healthy_threshold" {
  description = "Default number of consecutive health check successes required"
  type        = number
  default     = 3
}

variable "default_health_check_unhealthy_threshold" {
  description = "Default number of consecutive health check failures required"
  type        = number
  default     = 3
}

variable "default_health_check_matcher" {
  description = "Default HTTP codes to use when checking for a successful response"
  type        = string
  default     = "200-299"
}

variable "default_health_check_grace_period_seconds" {
  description = "Default health check grace period (seconds)"
  type        = number
  default     = 60
}

# Default Deployment Configuration
variable "default_enable_circuit_breaker" {
  description = "Default setting for enabling deployment circuit breaker"
  type        = bool
  default     = true
}

variable "default_enable_circuit_breaker_rollback" {
  description = "Default setting for enabling deployment circuit breaker rollback"
  type        = bool
  default     = true
}

# Default Logging Configuration
variable "default_log_retention_days" {
  description = "Default number of days to retain logs"
  type        = number
  default     = 30
}

# IAM Configuration
variable "task_role_policy_arns" {
  description = "List of IAM policy ARNs to attach to the task role"
  type        = list(string)
  default     = []
}
