"""
Application Use Cases for Document Ingestion Pipeline

This module contains the application layer use cases that orchestrate
the business logic for document processing operations.
"""

import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from ..domain.entities import (
    Document,
    DocumentId,
    ProcessingResult,
    ProcessingStage,
    ProductData,
    ProductEmbedding,
    S3Location,
    UserId,
)
from ..domain.interfaces import (
    IConfiguration,
    ICSVProcessor,
    IDocumentRepository,
    IEventPublisher,
    IFileStorage,
    ILogger,
    IMetricsCollector,
    IProductEmbeddingRepository,
)
from ..domain.services import (
    DocumentValidationService,
    EmbeddingGenerationService,
    MultiTenantSecurityService,
)


@dataclass
class ProcessDocumentCommand:
    """Command for processing a document"""

    bucket: str
    key: str


@dataclass
class ProcessDocumentResult:
    """Result of document processing"""

    success: bool
    document_id: str | None = None
    user_id: str | None = None
    processed_rows: int = 0
    error_message: str | None = None
    processing_time_ms: float = 0


class ProcessDocumentUseCase:
    """Use case for processing uploaded documents"""

    def __init__(
        self,
        document_repo: IDocumentRepository,
        embedding_repo: IProductEmbeddingRepository,
        file_storage: IFileStorage,
        csv_processor: ICSVProcessor,
        embedding_service: EmbeddingGenerationService,
        validation_service: DocumentValidationService,
        security_service: MultiTenantSecurityService,
        event_publisher: IEventPublisher,
        logger: ILogger,
        metrics: IMetricsCollector,
        config: IConfiguration,
    ):
        self._document_repo = document_repo
        self._embedding_repo = embedding_repo
        self._file_storage = file_storage
        self._csv_processor = csv_processor
        self._embedding_service = embedding_service
        self._validation_service = validation_service
        self._security_service = security_service
        self._event_publisher = event_publisher
        self._logger = logger
        self._metrics = metrics
        self._config = config

    async def execute(self, command: ProcessDocumentCommand) -> ProcessDocumentResult:
        """Execute document processing use case"""
        start_time = time.time()

        try:
            # Extract user information from S3 key
            user_info = self._file_storage.extract_user_info(command.key)
            user_id, file_info = user_info

            if not user_id or not file_info:
                raise ValueError(f"Invalid S3 key format: {command.key}")

            # Validate file format
            if not self._file_storage.validate_file_format(command.key):
                raise ValueError(f"Unsupported file format: {command.key}")

            # Security validation
            if not self._security_service.validate_tenant_access(str(user_id), command.key):
                raise ValueError(f"Tenant access violation for key: {command.key}")

            # Create document entity
            document_id = DocumentId.generate()
            s3_location = S3Location(bucket=command.bucket, key=command.key)

            document = Document(
                id=document_id,
                user_id=user_id,
                s3_location=s3_location,
                original_filename=file_info["filename"],
            )

            # Save initial document record
            await self._document_repo.save(document)

            # Start processing
            document.start_processing()
            await self._document_repo.save(document)
            await self._event_publisher.publish_document_processing_started(document)

            self._logger.info(f"Started processing document {document_id} for user {user_id}")

            # Process the document
            result = await self._process_document(document)

            processing_time_ms = (time.time() - start_time) * 1000

            if result.success:
                self._metrics.record_success(
                    "document_processing", user_id=str(user_id), document_id=str(document_id)
                )
                await self._event_publisher.publish_document_processing_completed(result)
            else:
                self._metrics.record_error(
                    "document_processing_failed", user_id=str(user_id), document_id=str(document_id)
                )
                await self._event_publisher.publish_document_processing_failed(result)

            self._metrics.record_processing_time(
                "document_processing", processing_time_ms, user_id=str(user_id)
            )

            return ProcessDocumentResult(
                success=result.success,
                document_id=str(document_id),
                user_id=str(user_id),
                processed_rows=result.processed_rows,
                error_message=result.error_message,
                processing_time_ms=processing_time_ms,
            )

        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            error_message = str(e)

            self._logger.error(f"Document processing failed: {error_message}", error=e)
            self._metrics.record_error("document_processing_error")
            self._metrics.record_processing_time("document_processing", processing_time_ms)

            return ProcessDocumentResult(
                success=False, error_message=error_message, processing_time_ms=processing_time_ms
            )

    async def _process_document(self, document: Document) -> ProcessingResult:
        """Internal method to process a document"""
        try:
            # Download CSV content
            document.update_stage(ProcessingStage.PARSING)
            await self._document_repo.save(document)

            content = await self._file_storage.download_content(document.s3_location)

            # Validate file size
            max_size = self._config.get_max_file_size()
            if not self._validation_service.validate_file_size(len(content), max_size):
                raise ValueError(f"File size exceeds limit: {len(content)} > {max_size}")

            # Parse CSV
            column_names, products = await self._csv_processor.parse_csv(content)

            # Validate CSV data
            if not self._validation_service.validate_column_names(column_names):
                raise ValueError("Invalid column names in CSV")

            max_rows = self._config.get_max_rows()
            if not self._validation_service.validate_row_count(len(products), max_rows):
                raise ValueError(f"Too many rows: {len(products)} > {max_rows}")

            # Clean and validate data
            products = self._csv_processor.clean_csv_data(products)
            self._csv_processor.validate_csv_data(products)

            # Update document with CSV metadata
            document.update_csv_metadata(column_names, len(products))
            await self._document_repo.save(document)

            # Generate embeddings
            document.update_stage(ProcessingStage.GENERATING_EMBEDDINGS)
            await self._document_repo.save(document)

            embeddings_data = await self._embedding_service.generate_product_embeddings(products)

            # Store embeddings
            document.update_stage(ProcessingStage.STORING)
            await self._document_repo.save(document)

            embeddings = []
            for item in embeddings_data:
                product_data = ProductData(item["product_data"], item["row_index"])
                embedding = ProductEmbedding(
                    document_id=document.id,
                    user_id=document.user_id,
                    row_index=item["row_index"],
                    product_data=product_data,
                    searchable_text=item["searchable_text"],
                    embedding_vector=item["embedding_vector"],
                )
                embeddings.append(embedding)

            await self._embedding_repo.save_batch(embeddings)

            # Move file to processed folder
            await self._file_storage.move_file(document.s3_location, "processed", document.user_id)

            # Complete processing
            document.complete_processing(len(products))
            await self._document_repo.save(document)

            return ProcessingResult.success_result(document.id, document.user_id, len(products), 0)

        except Exception as e:
            # Handle failure
            error_message = str(e)
            document.fail_processing(error_message)
            await self._document_repo.save(document)

            # Move file to failed folder
            try:
                await self._file_storage.move_file(document.s3_location, "failed", document.user_id)
            except Exception as move_error:
                self._logger.error(f"Failed to move file to failed folder: {move_error}")

            return ProcessingResult.failure_result(document.id, document.user_id, error_message, 0)
