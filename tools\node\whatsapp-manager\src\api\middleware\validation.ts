import { Request, Response, NextFunction } from 'express';
import { ValidationError, validate } from 'express-validation';

/**
 * Validation middleware for request validation using express-validation
 */
export function validateRequest(schema: any) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      validate(schema, {}, {})(req, res, next);
    } catch (error) {
      if (error instanceof ValidationError) {
        const validationErrors = error.details.body || error.details.params || error.details.query || [];
        
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Request validation failed',
            details: validationErrors.map((err: any) => ({
              field: err.path?.join('.') || 'unknown',
              message: err.message,
              value: err.context?.value
            }))
          }
        });
        return;
      }
      
      next(error);
    }
  };
}
