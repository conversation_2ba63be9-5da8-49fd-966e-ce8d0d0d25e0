import { inject, injectable } from 'tsyringe';

import { WhatsAppMessage, MessageType, MediaInfo, LocationInfo, ContactInfo } from '../../domain/entities/WhatsAppMessage';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';

/**
 * Message processing options
 */
export interface MessageProcessingOptions {
  enableDeduplication?: boolean;
  maxMessageAge?: number; // in milliseconds
  enableContentFiltering?: boolean;
  enableMediaProcessing?: boolean;
  maxMediaSize?: number; // in bytes
}

/**
 * Message filter interface
 */
export interface MessageFilter {
  userId?: string;
  messageType?: MessageType;
  fromContact?: string;
  isGroup?: boolean;
  hasMedia?: boolean;
  minAge?: number;
  maxAge?: number;
}

/**
 * Message processor for handling incoming WhatsApp messages
 * Provides validation, deduplication, filtering, and transformation
 */
@injectable()
export class MessageProcessor {
  private readonly processedMessages = new Set<string>();
  private readonly maxCacheSize: number;
  private readonly defaultOptions: MessageProcessingOptions;
  private readonly cleanupInterval: NodeJS.Timeout;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    this.maxCacheSize = this.config.getOptional('MESSAGE_CACHE_SIZE', 10000);
    this.defaultOptions = {
      enableDeduplication: true,
      maxMessageAge: 24 * 60 * 60 * 1000, // 24 hours
      enableContentFiltering: true,
      enableMediaProcessing: true,
      maxMediaSize: 50 * 1024 * 1024 // 50MB
    };

    // Start cleanup interval for processed messages cache
    this.cleanupInterval = setInterval(() => {
      this.cleanupProcessedMessages();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Process incoming messages from Baileys
   */
  async processMessages(
    userId: string,
    sessionId: string,
    messages: any[],
    options?: MessageProcessingOptions
  ): Promise<WhatsAppMessage[]> {
    const finalOptions = { ...this.defaultOptions, ...options };
    const processedMessages: WhatsAppMessage[] = [];

    try {
      this.logger.debug('Processing messages', {
        userId,
        messageCount: messages.length,
        options: finalOptions
      });

      for (const rawMessage of messages) {
        try {
          const processedMessage = await this.processMessage(
            userId,
            sessionId,
            rawMessage,
            finalOptions
          );

          if (processedMessage) {
            processedMessages.push(processedMessage);
          }
        } catch (error) {
          this.logger.error('Failed to process individual message', {
            userId,
            messageId: rawMessage.key?.id,
            error: (error as Error).message
          });
        }
      }

      this.logger.info('Messages processed', {
        userId,
        totalMessages: messages.length,
        processedCount: processedMessages.length
      });

      return processedMessages;
    } catch (error) {
      this.logger.error('Failed to process messages', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Process a single message
   */
  private async processMessage(
    userId: string,
    sessionId: string,
    rawMessage: any,
    options: MessageProcessingOptions
  ): Promise<WhatsAppMessage | null> {
    try {
      // Basic validation
      if (!rawMessage.key?.id || !rawMessage.key?.remoteJid) {
        this.logger.debug('Skipping message with missing key information', { userId });
        return null;
      }

      const messageId = rawMessage.key.id;
      const from = rawMessage.key.remoteJid;
      const to = userId; // For incoming messages, 'to' is the session user

      // Check message age
      const messageTimestamp = rawMessage.messageTimestamp 
        ? new Date(Number(rawMessage.messageTimestamp) * 1000)
        : new Date();

      if (options.maxMessageAge && this.isMessageTooOld(messageTimestamp, options.maxMessageAge)) {
        this.logger.debug('Skipping old message', {
          userId,
          messageId,
          messageAge: Date.now() - messageTimestamp.getTime()
        });
        return null;
      }

      // Deduplication check
      if (options.enableDeduplication && this.isDuplicateMessage(messageId)) {
        this.logger.debug('Skipping duplicate message', { userId, messageId });
        return null;
      }

      // Extract message content and type
      const { content, type, mediaInfo, locationInfo, contactInfo } = await this.extractMessageContent(
        rawMessage,
        options
      );

      // Content filtering
      if (options.enableContentFiltering && this.shouldFilterContent(content, type)) {
        this.logger.debug('Message filtered by content filter', {
          userId,
          messageId,
          type
        });
        return null;
      }

      // Create WhatsApp message entity
      const whatsappMessage = WhatsAppMessage.createInbound(
        sessionId,
        userId,
        from,
        to,
        type,
        content,
        messageId,
        rawMessage.key.quotedMessageId,
        mediaInfo,
        locationInfo,
        contactInfo,
        {
          rawMessage: this.sanitizeRawMessage(rawMessage),
          processedAt: new Date().toISOString(),
          fromMe: rawMessage.key.fromMe || false,
          participant: rawMessage.key.participant,
          pushName: rawMessage.pushName
        }
      );

      // Mark as processed
      if (options.enableDeduplication) {
        this.markMessageAsProcessed(messageId);
      }

      this.logger.debug('Message processed successfully', {
        userId,
        messageId,
        type,
        from,
        hasMedia: !!mediaInfo
      });

      return whatsappMessage;
    } catch (error) {
      this.logger.error('Failed to process message', {
        userId,
        messageId: rawMessage.key?.id,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Extract content and metadata from raw message
   */
  private async extractMessageContent(
    rawMessage: any,
    _options: MessageProcessingOptions
  ): Promise<{
    content: string;
    type: MessageType;
    mediaInfo?: MediaInfo;
    locationInfo?: LocationInfo;
    contactInfo?: ContactInfo;
  }> {
    const message = rawMessage.message;
    if (!message) {
      return { content: '', type: 'text' };
    }

    // Text message
    if (message.conversation) {
      return {
        content: message.conversation,
        type: 'text'
      };
    }

    // Extended text message
    if (message.extendedTextMessage) {
      return {
        content: message.extendedTextMessage.text || '',
        type: 'text'
      };
    }

    // Image message
    if (message.imageMessage) {
      const mediaInfo: MediaInfo = {
        mimeType: message.imageMessage.mimetype || 'image/jpeg',
        fileName: message.imageMessage.fileName,
        fileSize: message.imageMessage.fileLength ? Number(message.imageMessage.fileLength) : undefined,
        caption: message.imageMessage.caption
      };

      return {
        content: message.imageMessage.caption || '',
        type: 'image',
        mediaInfo
      };
    }

    // Audio message
    if (message.audioMessage) {
      const mediaInfo: MediaInfo = {
        mimeType: message.audioMessage.mimetype || 'audio/ogg',
        fileName: message.audioMessage.fileName,
        fileSize: message.audioMessage.fileLength ? Number(message.audioMessage.fileLength) : undefined
      };

      return {
        content: '',
        type: 'audio',
        mediaInfo
      };
    }

    // Video message
    if (message.videoMessage) {
      const mediaInfo: MediaInfo = {
        mimeType: message.videoMessage.mimetype || 'video/mp4',
        fileName: message.videoMessage.fileName,
        fileSize: message.videoMessage.fileLength ? Number(message.videoMessage.fileLength) : undefined,
        caption: message.videoMessage.caption
      };

      return {
        content: message.videoMessage.caption || '',
        type: 'video',
        mediaInfo
      };
    }

    // Document message
    if (message.documentMessage) {
      const mediaInfo: MediaInfo = {
        mimeType: message.documentMessage.mimetype || 'application/octet-stream',
        fileName: message.documentMessage.fileName,
        fileSize: message.documentMessage.fileLength ? Number(message.documentMessage.fileLength) : undefined
      };

      return {
        content: message.documentMessage.fileName || '',
        type: 'document',
        mediaInfo
      };
    }

    // Location message
    if (message.locationMessage) {
      const locationInfo: LocationInfo = {
        latitude: message.locationMessage.degreesLatitude || 0,
        longitude: message.locationMessage.degreesLongitude || 0,
        name: message.locationMessage.name,
        address: message.locationMessage.address
      };

      return {
        content: message.locationMessage.name || 'Location',
        type: 'location',
        locationInfo
      };
    }

    // Contact message
    if (message.contactMessage) {
      const contactInfo: ContactInfo = {
        displayName: message.contactMessage.displayName || '',
        phoneNumber: message.contactMessage.vcard?.match(/TEL.*?:(.*)/)?.[1]
      };

      return {
        content: message.contactMessage.displayName || 'Contact',
        type: 'contact',
        contactInfo
      };
    }

    // Sticker message
    if (message.stickerMessage) {
      const mediaInfo: MediaInfo = {
        mimeType: message.stickerMessage.mimetype || 'image/webp',
        fileSize: message.stickerMessage.fileLength ? Number(message.stickerMessage.fileLength) : undefined
      };

      return {
        content: '',
        type: 'sticker',
        mediaInfo
      };
    }

    // Reaction message
    if (message.reactionMessage) {
      return {
        content: message.reactionMessage.text || '',
        type: 'reaction'
      };
    }

    // Default fallback
    return {
      content: JSON.stringify(message),
      type: 'text'
    };
  }

  /**
   * Check if message is too old
   */
  private isMessageTooOld(messageTimestamp: Date, maxAge: number): boolean {
    const age = Date.now() - messageTimestamp.getTime();
    return age > maxAge;
  }

  /**
   * Check if message is duplicate
   */
  private isDuplicateMessage(messageId: string): boolean {
    return this.processedMessages.has(messageId);
  }

  /**
   * Mark message as processed
   */
  private markMessageAsProcessed(messageId: string): void {
    this.processedMessages.add(messageId);

    // Prevent cache from growing too large
    if (this.processedMessages.size > this.maxCacheSize) {
      const toDelete = Array.from(this.processedMessages).slice(0, this.maxCacheSize / 2);
      toDelete.forEach(id => this.processedMessages.delete(id));
    }
  }

  /**
   * Content filtering
   */
  private shouldFilterContent(content: string, type: MessageType): boolean {
    // Basic content filtering rules
    if (!content && type === 'text') {
      return true; // Filter empty text messages
    }

    // Filter messages with suspicious content
    const suspiciousPatterns = [
      /javascript:/i,
      /data:.*base64/i,
      /<script/i,
      /on\w+=/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Sanitize raw message for storage
   */
  private sanitizeRawMessage(rawMessage: any): any {
    // Remove sensitive or large data from raw message
    const sanitized = {
      key: rawMessage.key,
      messageTimestamp: rawMessage.messageTimestamp,
      pushName: rawMessage.pushName,
      broadcast: rawMessage.broadcast,
      multicast: rawMessage.multicast,
      urlText: rawMessage.urlText,
      urlNumber: rawMessage.urlNumber,
      messageStubType: rawMessage.messageStubType,
      clearMedia: rawMessage.clearMedia,
      messageStubParameters: rawMessage.messageStubParameters,
      duration: rawMessage.duration,
      labels: rawMessage.labels,
      paymentInfo: rawMessage.paymentInfo,
      finalLiveLocation: rawMessage.finalLiveLocation,
      quotedPaymentInfo: rawMessage.quotedPaymentInfo,
      ephemeralStartTimestamp: rawMessage.ephemeralStartTimestamp,
      ephemeralDuration: rawMessage.ephemeralDuration,
      ephemeralOffToOn: rawMessage.ephemeralOffToOn,
      disappearingMode: rawMessage.disappearingMode
    };

    return sanitized;
  }

  /**
   * Filter messages based on criteria
   */
  filterMessages(messages: WhatsAppMessage[], filter: MessageFilter): WhatsAppMessage[] {
    return messages.filter(message => {
      if (filter.userId && message.userId !== filter.userId) {
        return false;
      }

      if (filter.messageType && message.type !== filter.messageType) {
        return false;
      }

      if (filter.fromContact && message.from !== filter.fromContact) {
        return false;
      }

      if (filter.isGroup !== undefined) {
        const isGroup = message.from.includes('@g.us');
        if (filter.isGroup !== isGroup) {
          return false;
        }
      }

      if (filter.hasMedia !== undefined) {
        if (filter.hasMedia !== message.hasMedia()) {
          return false;
        }
      }

      if (filter.minAge !== undefined) {
        const age = message.getAge();
        if (age < filter.minAge) {
          return false;
        }
      }

      if (filter.maxAge !== undefined) {
        const age = message.getAge();
        if (age > filter.maxAge) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Get processing statistics
   */
  getStatistics(): {
    processedMessagesCount: number;
    cacheSize: number;
    maxCacheSize: number;
  } {
    return {
      processedMessagesCount: this.processedMessages.size,
      cacheSize: this.processedMessages.size,
      maxCacheSize: this.maxCacheSize
    };
  }

  /**
   * Cleanup processed messages cache
   */
  private cleanupProcessedMessages(): void {
    if (this.processedMessages.size > this.maxCacheSize * 0.8) {
      const toDelete = Array.from(this.processedMessages).slice(0, this.maxCacheSize / 4);
      toDelete.forEach(id => this.processedMessages.delete(id));

      this.logger.debug('Cleaned up processed messages cache', {
        deletedCount: toDelete.length,
        remainingCount: this.processedMessages.size
      });
    }
  }

  /**
   * Clear all processed messages cache
   */
  clearCache(): void {
    this.processedMessages.clear();
    this.logger.info('Processed messages cache cleared');
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.processedMessages.clear();
    this.logger.info('MessageProcessor destroyed');
  }
}
