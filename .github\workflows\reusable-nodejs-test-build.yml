name: Reusable - Node.js Test and Build

on:
  workflow_call:
    inputs:
      lambda-path:
        required: true
        type: string
        description: 'Path to Lambda function directory'
      node-version:
        required: false
        type: string
        description: 'Node.js version to use'
        default: '18'
      cache-dependency-path:
        required: false
        type: string
        description: 'Path to package-lock.json for caching'
        default: '**/package-lock.json'
      run-tests:
        required: false
        type: boolean
        description: 'Whether to run tests'
        default: true
      run-lint:
        required: false
        type: boolean
        description: 'Whether to run linting'
        default: true
      run-build:
        required: false
        type: boolean
        description: 'Whether to run build'
        default: true
      test-command:
        required: false
        type: string
        description: 'Custom test command'
        default: 'npm test'
      lint-command:
        required: false
        type: string
        description: 'Custom lint command'
        default: 'npx eslint . --ext .js'
      build-command:
        required: false
        type: string
        description: 'Custom build command'
        default: 'npm run build'
    outputs:
      test-result:
        description: 'Test execution result'
        value: ${{ jobs.test-build.outputs.test-result }}
      lint-result:
        description: 'Lint execution result'
        value: ${{ jobs.test-build.outputs.lint-result }}
      build-result:
        description: 'Build execution result'
        value: ${{ jobs.test-build.outputs.build-result }}

jobs:
  test-build:
    runs-on: ubuntu-latest
    outputs:
      test-result: ${{ steps.test-status.outputs.result }}
      lint-result: ${{ steps.lint-status.outputs.result }}
      build-result: ${{ steps.build-status.outputs.result }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}

      - name: Install dependencies
        run: |
          cd ${{ inputs.lambda-path }}
          echo "📦 Installing dependencies..."
          npm install

      - name: Run tests
        if: inputs.run-tests
        id: run-tests
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🧪 Running tests..."
          if [ -f "package.json" ] && npm run | grep -q "test"; then
            ${{ inputs.test-command }}
            echo "✅ Tests passed"
          else
            echo "⚠️ No test script found in package.json, skipping tests"
          fi

      - name: Set test status
        id: test-status
        run: |
          if [ "${{ inputs.run-tests }}" == "true" ]; then
            if [ "${{ steps.run-tests.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Run linting
        if: inputs.run-lint
        id: run-lint
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🔍 Running linting..."
          if [ -f ".eslintrc.json" ] || [ -f ".eslintrc.js" ] || [ -f ".eslintrc.yml" ]; then
            ${{ inputs.lint-command }}
            echo "✅ Linting passed"
          else
            echo "⚠️ No ESLint configuration found, skipping linting"
          fi

      - name: Set lint status
        id: lint-status
        run: |
          if [ "${{ inputs.run-lint }}" == "true" ]; then
            if [ "${{ steps.run-lint.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Run build
        if: inputs.run-build
        id: run-build
        run: |
          cd ${{ inputs.lambda-path }}
          echo "🏗️ Running build..."
          if [ -f "package.json" ] && npm run | grep -q "build"; then
            ${{ inputs.build-command }}
            echo "✅ Build completed"
          else
            echo "⚠️ No build script found in package.json, skipping build"
          fi

      - name: Set build status
        id: build-status
        run: |
          if [ "${{ inputs.run-build }}" == "true" ]; then
            if [ "${{ steps.run-build.outcome }}" == "success" ]; then
              echo "result=success" >> $GITHUB_OUTPUT
            else
              echo "result=failure" >> $GITHUB_OUTPUT
            fi
          else
            echo "result=skipped" >> $GITHUB_OUTPUT
          fi

      - name: Summary
        run: |
          echo "## 🧪 Node.js Test & Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Node.js Version**: ${{ inputs.node-version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Lambda Path**: ${{ inputs.lambda-path }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tests**: ${{ steps.test-status.outputs.result == 'success' && '✅ Passed' || steps.test-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Linting**: ${{ steps.lint-status.outputs.result == 'success' && '✅ Passed' || steps.lint-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Build**: ${{ steps.build-status.outputs.result == 'success' && '✅ Passed' || steps.build-status.outputs.result == 'failure' && '❌ Failed' || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
