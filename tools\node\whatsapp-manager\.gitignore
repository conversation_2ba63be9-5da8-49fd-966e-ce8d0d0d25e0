# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.*.local
!.env.example
environments/.env.development.local
environments/.env.test.local
environments/.env.production.local

# Logs
logs/
*.log
pino-*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Test results
test-results/
junit.xml

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# AWS
.aws/

# Secrets
secrets/
*.pem
*.key
!docker/ssl/*.key

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.parcel-cache/
