import { Request, Response, NextFunction } from 'express';
import { inject, injectable } from 'tsyringe';
import { ILoggerService } from '../../shared/logging/interfaces';
import { StartSessionUseCase, StartSessionRequest } from '../../application/use-cases/StartSessionUseCase';
import { GetSessionStatusUseCase, GetSessionStatusRequest } from '../../application/use-cases/GetSessionStatusUseCase';
import { TerminateSessionUseCase, TerminateSessionRequest } from '../../application/use-cases/TerminateSessionUseCase';
import { ListSessionsUseCase, ListSessionsRequest } from '../../application/use-cases/ListSessionsUseCase';
import { RefreshQRCodeUseCase, RefreshQRCodeRequest } from '../../application/use-cases/RefreshQRCodeUseCase';
import { ProcessMessageUseCase, SendMessageRequest } from '../../application/use-cases/ProcessMessageUseCase';

/**
 * Session controller for handling WhatsApp session API endpoints
 */
@injectable()
export class SessionController {
  constructor(
    @inject('StartSessionUseCase') private startSessionUseCase: StartSessionUseCase,
    @inject('GetSessionStatusUseCase') private getSessionStatusUseCase: GetSessionStatusUseCase,
    @inject('TerminateSessionUseCase') private terminateSessionUseCase: TerminateSessionUseCase,
    @inject('ListSessionsUseCase') private listSessionsUseCase: ListSessionsUseCase,
    @inject('RefreshQRCodeUseCase') private refreshQRCodeUseCase: RefreshQRCodeUseCase,
    @inject('ProcessMessageUseCase') private processMessageUseCase: ProcessMessageUseCase,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * POST /api/sessions/:userId
   * Start a new WhatsApp session
   */
  async startSession(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const { deviceName, browserName, forceNew } = req.body;

      this.logger.info('Starting session request', {
        userId,
        deviceName,
        browserName,
        forceNew,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      const request: StartSessionRequest = {
        userId,
        deviceName,
        browserName,
        forceNew: forceNew === true
      };

      const result = await this.startSessionUseCase.execute(request);

      res.status(result.isExistingSession ? 200 : 201).json({
        success: true,
        data: {
          sessionId: result.sessionId,
          userId,
          status: result.status,
          qrCode: result.qrCode,
          expiresAt: result.expiresAt,
          isExistingSession: result.isExistingSession,
          phoneNumber: result.phoneNumber
        },
        message: result.isExistingSession ? 'Session reconnected' : 'Session created successfully'
      });

    } catch (error) {
      this.logger.error('Failed to start session', {
        userId: req.params['userId'],
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      next(error);
    }
  }

  /**
   * POST /api/sessions/:userId/reconnect
   * Reconnect an existing session
   */
  async reconnectSession(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const { reason } = req.body;

      this.logger.info('Reconnecting session request', {
        userId,
        reason,
        ip: req.ip
      });

      const request: StartSessionRequest = {
        userId,
        forceNew: false
      };

      const result = await this.startSessionUseCase.execute(request);

      res.status(200).json({
        success: true,
        data: {
          sessionId: result.sessionId,
          userId,
          status: result.status,
          qrCode: result.qrCode,
          expiresAt: result.expiresAt,
          phoneNumber: result.phoneNumber
        },
        message: 'Session reconnection initiated'
      });

    } catch (error) {
      this.logger.error('Failed to reconnect session', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions/:userId
   * Get session status
   */
  async getSessionStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const { includeHealth } = req.query;

      this.logger.debug('Getting session status', { userId, includeHealth });

      const request: GetSessionStatusRequest = { userId };

      let result;
      if (includeHealth === 'true') {
        result = await this.getSessionStatusUseCase.executeWithHealthCheck(request);
      } else {
        result = await this.getSessionStatusUseCase.execute(request);
      }

      res.status(200).json({
        success: true,
        data: result,
        message: 'Session status retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to get session status', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * DELETE /api/sessions/:userId
   * Terminate session
   */
  async terminateSession(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const { reason, forceDelete } = req.body;

      this.logger.info('Terminating session request', {
        userId,
        reason,
        forceDelete,
        ip: req.ip
      });

      const request: TerminateSessionRequest = {
        userId,
        reason,
        forceDelete: forceDelete === true
      };

      const result = await this.terminateSessionUseCase.execute(request);

      res.status(200).json({
        success: true,
        data: {
          userId: result.userId,
          sessionId: result.sessionId,
          terminated: result.terminated,
          reason: result.reason,
          terminatedAt: result.terminatedAt
        },
        message: 'Session terminated successfully'
      });

    } catch (error) {
      this.logger.error('Failed to terminate session', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions
   * List all sessions
   */
  async listSessions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        status,
        limit,
        offset,
        includeInactive,
        sortBy,
        sortOrder
      } = req.query;

      this.logger.debug('Listing sessions request', {
        status,
        limit,
        offset,
        includeInactive,
        sortBy,
        sortOrder
      });

      const request: ListSessionsRequest = {
        status: status as any,
        limit: limit ? parseInt(limit as string) : undefined,
        offset: offset ? parseInt(offset as string) : undefined,
        includeInactive: includeInactive === 'true',
        sortBy: sortBy as any,
        sortOrder: sortOrder as any
      };

      const result = await this.listSessionsUseCase.execute(request);

      res.status(200).json({
        success: true,
        data: {
          sessions: result.sessions,
          pagination: result.pagination,
          summary: {
            totalCount: result.totalCount,
            activeCount: result.activeCount,
            connectedCount: result.connectedCount,
            qrPendingCount: result.qrPendingCount,
            disconnectedCount: result.disconnectedCount,
            errorCount: result.errorCount
          }
        },
        message: 'Sessions retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to list sessions', {
        error: (error as Error).message,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions/statistics
   * Get session statistics
   */
  async getSessionStatistics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      this.logger.debug('Getting session statistics');

      const result = await this.listSessionsUseCase.getSessionStatistics();

      res.status(200).json({
        success: true,
        data: result,
        message: 'Session statistics retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to get session statistics', {
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions/active
   * Get active sessions only
   */
  async getActiveSessions(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      this.logger.debug('Getting active sessions');

      const result = await this.listSessionsUseCase.getActiveSessions();

      res.status(200).json({
        success: true,
        data: {
          sessions: result.sessions,
          pagination: result.pagination,
          summary: {
            totalCount: result.totalCount,
            activeCount: result.activeCount,
            connectedCount: result.connectedCount
          }
        },
        message: 'Active sessions retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to get active sessions', {
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * POST /api/sessions/cleanup/expired
   * Cleanup expired sessions
   */
  async cleanupExpiredSessions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      this.logger.info('Cleaning up expired sessions', { ip: req.ip });

      const result = await this.terminateSessionUseCase.terminateExpiredSessions();

      res.status(200).json({
        success: true,
        data: {
          terminated: result.terminated,
          failed: result.failed,
          errors: result.errors
        },
        message: `Cleanup completed: ${result.terminated} sessions terminated, ${result.failed} failed`
      });

    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions', {
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * POST /api/sessions/terminate-all
   * Terminate all sessions (admin endpoint)
   */
  async terminateAllSessions(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { reason } = req.body;

      this.logger.warn('Terminating all sessions', {
        reason,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      const result = await this.terminateSessionUseCase.terminateAllSessions(reason);

      res.status(200).json({
        success: true,
        data: {
          terminated: result.terminated,
          failed: result.failed,
          errors: result.errors
        },
        message: `All sessions terminated: ${result.terminated} successful, ${result.failed} failed`
      });

    } catch (error) {
      this.logger.error('Failed to terminate all sessions', {
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * POST /api/sessions/:userId/qr/refresh
   * Refresh QR code for pending session
   */
  async refreshQRCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }

      const { force } = req.body;

      this.logger.info('Refreshing QR code', {
        userId,
        force,
        ip: req.ip
      });

      const request: RefreshQRCodeRequest = {
        userId,
        force: force === true
      };

      const result = await this.refreshQRCodeUseCase.execute(request);

      if (!result.success) {
        res.status(400).json({
          success: false,
          error: result.message,
          data: {
            isNewQR: result.isNewQR
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          qrCode: result.qrCode,
          expiresAt: result.expiresAt,
          timeToExpiry: result.timeToExpiry,
          isNewQR: result.isNewQR
        },
        message: result.message
      });

    } catch (error) {
      this.logger.error('Failed to refresh QR code', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions/:userId/qr/status
   * Get QR code status
   */
  async getQRStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }

      this.logger.debug('Getting QR status', { userId });

      const result = await this.refreshQRCodeUseCase.getQRStatus(userId);

      res.status(200).json({
        success: true,
        data: result,
        message: 'QR status retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to get QR status', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * POST /api/sessions/:userId/messages/send
   * Send a message through the session
   */
  async sendMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }

      const { to, content, type, mediaUrl, quotedMessageId, metadata } = req.body;

      this.logger.info('Sending message', {
        userId,
        to: to?.split('@')[0] + '@***', // Mask phone number
        type,
        hasMedia: !!mediaUrl,
        isReply: !!quotedMessageId,
        ip: req.ip
      });

      const request: SendMessageRequest = {
        userId,
        to,
        content,
        type,
        mediaUrl,
        quotedMessageId,
        metadata
      };

      const result = await this.processMessageUseCase.sendMessage(request);

      if (!result.success) {
        res.status(400).json({
          success: false,
          error: result.message,
          rateLimitInfo: result.rateLimitInfo
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          messageId: result.messageId
        },
        message: result.message,
        rateLimitInfo: result.rateLimitInfo
      });

    } catch (error) {
      this.logger.error('Failed to send message', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }

  /**
   * GET /api/sessions/:userId/messages
   * Get messages for session
   */
  async getMessages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.params['userId'];
      if (!userId) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }

      const { limit = 50, offset = 0, type, from, hasMedia } = req.query;

      this.logger.debug('Getting messages', {
        userId,
        limit,
        offset,
        type,
        from,
        hasMedia
      });

      const filter = {
        userId,
        messageType: type as any,
        fromContact: from as string,
        hasMedia: hasMedia === 'true' ? true : hasMedia === 'false' ? false : undefined
      };

      const result = await this.processMessageUseCase.getMessages(
        userId,
        filter,
        parseInt(limit as string),
        parseInt(offset as string)
      );

      res.status(200).json({
        success: true,
        data: {
          messages: result.messages,
          total: result.total,
          hasMore: result.hasMore,
          pagination: {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string)
          }
        },
        message: 'Messages retrieved successfully'
      });

    } catch (error) {
      this.logger.error('Failed to get messages', {
        userId: req.params['userId'],
        error: (error as Error).message
      });
      next(error);
    }
  }
}
