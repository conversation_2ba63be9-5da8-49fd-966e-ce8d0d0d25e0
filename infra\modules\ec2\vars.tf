variable "app_id" {
  description = "Application ID"
  type        = string
  default     = "bastion"
}

variable "volume_size" {
  description = "Volume size of root device"
  type        = number
  default     = 30
}

variable "volume_type" {
  description = "Volume type use in EC2,gp,io1,io2"
  type        = string
  default     = "gp2"
}

variable "instance_type" {
  description = "Instance type"
  type        = string
  default     = "t2.micro"
}

variable "tags" {
  description = "Custom tags"
  type        = map(string)
  default     = {}
}

variable "associate_public_ip_address" {
  description = "Associate public IP address"
  type        = bool
  default     = false
}
