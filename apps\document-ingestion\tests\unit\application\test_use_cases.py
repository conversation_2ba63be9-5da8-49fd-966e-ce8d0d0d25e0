"""
Unit tests for application use cases

These tests verify the orchestration logic of use cases
using mocked dependencies to ensure isolation.
"""

from datetime import datetime
from unittest.mock import AsyncMock, Mock

import pytest

from src.application.use_cases import (
    ProcessDocumentCommand,
    ProcessDocumentResult,
    ProcessDocumentUseCase,
)
from src.domain.entities import (
    Document,
    DocumentId,
    DocumentStatus,
    ProcessingResult,
    ProcessingStage,
    ProductData,
    S3Location,
    UserId,
)
from src.domain.interfaces import (
    IConfiguration,
    ICSVProcessor,
    IDocumentRepository,
    IEventPublisher,
    IFileStorage,
    ILogger,
    IMetricsCollector,
    IProductEmbeddingRepository,
)
from src.domain.services import (
    DocumentValidationService,
    EmbeddingGenerationService,
    MultiTenantSecurityService,
)


class TestProcessDocumentUseCase:
    """Test ProcessDocumentUseCase"""

    @pytest.fixture
    def use_case(self, mocked_container):
        """Create use case with mocked dependencies"""
        return mocked_container.process_document_use_case()

    @pytest.fixture
    def valid_command(self):
        """Create a valid process document command"""
        return ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")

    @pytest.fixture
    def setup_mocks_for_success(
        self,
        mock_file_storage,
        mock_csv_processor,
        mock_document_repository,
        mock_embedding_repository,
        mock_embedding_generation_service,
        mock_event_publisher,
        mock_configuration,
        sample_csv_content,
    ):
        """Setup mocks for successful processing"""
        # File storage mocks
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_file_storage.download_content.return_value = sample_csv_content
        mock_file_storage.move_file.return_value = True

        # CSV processor mocks
        mock_csv_processor.parse_csv.return_value = (
            ["name", "description", "price"],
            [
                ProductData({"name": "Product 1", "price": "29.99"}, 0),
                ProductData({"name": "Product 2", "price": "39.99"}, 1),
            ],
        )
        mock_csv_processor.clean_csv_data.side_effect = lambda x: x
        mock_csv_processor.validate_csv_data.return_value = True

        # Configuration mocks
        mock_configuration.get_max_file_size.return_value = 10000000
        mock_configuration.get_max_rows.return_value = 10000

        # Repository mocks
        mock_document_repository.save = AsyncMock()
        mock_embedding_repository.save_batch = AsyncMock()

        # Event publisher mocks
        mock_event_publisher.publish_document_processing_started = AsyncMock()
        mock_event_publisher.publish_document_processing_completed = AsyncMock()

        # Embedding generation service mock
        mock_embedding_generation_service.generate_product_embeddings.return_value = [
            {
                "row_index": 0,
                "product_data": {"name": "Product 1", "price": "29.99"},
                "searchable_text": "Name: Product 1",
                "embedding_vector": [0.1, 0.2, 0.3],
            },
            {
                "row_index": 1,
                "product_data": {"name": "Product 2", "price": "39.99"},
                "searchable_text": "Name: Product 2",
                "embedding_vector": [0.4, 0.5, 0.6],
            },
        ]

    @pytest.mark.asyncio
    async def test_execute_success(self, use_case, valid_command, setup_mocks_for_success):
        """Test successful document processing"""
        result = await use_case.execute(valid_command)

        assert result.success is True
        assert result.user_id == "user123"
        assert result.processed_rows == 2
        assert result.error_message is None
        assert result.processing_time_ms >= 0

    @pytest.mark.asyncio
    async def test_execute_invalid_s3_key(self, use_case, mock_file_storage):
        """Test processing with invalid S3 key"""
        mock_file_storage.extract_user_info.return_value = (None, None)

        command = ProcessDocumentCommand(bucket="test-bucket", key="invalid-key")
        result = await use_case.execute(command)

        assert result.success is False
        assert "Invalid S3 key format" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_unsupported_file_format(self, use_case, mock_file_storage):
        """Test processing with unsupported file format"""
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "file.txt"},
        )
        mock_file_storage.validate_file_format.return_value = False

        command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/file.txt")
        result = await use_case.execute(command)

        assert result.success is False
        assert "Unsupported file format" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_tenant_access_violation(
        self, use_case, mock_file_storage, mock_security_service
    ):
        """Test processing with tenant access violation"""
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_security_service.validate_tenant_access.return_value = False

        command = ProcessDocumentCommand(bucket="test-bucket", key="user456/uploaded/products.csv")
        result = await use_case.execute(command)

        assert result.success is False
        assert "Tenant access violation" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_file_too_large(
        self, use_case, mock_file_storage, mock_validation_service, mock_configuration
    ):
        """Test processing with file too large"""
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_file_storage.download_content.return_value = b"large content"
        mock_configuration.get_max_file_size.return_value = 5
        mock_validation_service.validate_file_size.return_value = False

        command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
        result = await use_case.execute(command)

        assert result.success is False
        assert "File size exceeds limit" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_csv_parsing_error(
        self, use_case, mock_file_storage, mock_csv_processor, mock_configuration
    ):
        """Test processing with CSV parsing error"""
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_file_storage.download_content.return_value = b"invalid csv"
        mock_configuration.get_max_file_size.return_value = 10000
        mock_csv_processor.parse_csv.side_effect = ValueError("Invalid CSV format")

        command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
        result = await use_case.execute(command)

        assert result.success is False
        assert "Invalid CSV format" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_too_many_rows(
        self,
        use_case,
        mock_file_storage,
        mock_csv_processor,
        mock_validation_service,
        mock_configuration,
    ):
        """Test processing with too many rows"""
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_file_storage.download_content.return_value = b"csv content"
        mock_configuration.get_max_file_size.return_value = 10000
        mock_configuration.get_max_rows.return_value = 1

        mock_csv_processor.parse_csv.return_value = (
            ["name"],
            [ProductData({"name": "Product 1"}, 0), ProductData({"name": "Product 2"}, 1)],
        )
        mock_validation_service.validate_file_size.return_value = True
        mock_validation_service.validate_column_names.return_value = True
        mock_validation_service.validate_row_count.return_value = False

        command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
        result = await use_case.execute(command)

        assert result.success is False
        assert "Too many rows" in result.error_message

    @pytest.mark.asyncio
    async def test_execute_embedding_generation_error(
        self,
        use_case,
        mock_file_storage,
        mock_csv_processor,
        mock_embedding_generation_service,
        mock_configuration,
        sample_csv_content,
    ):
        """Test processing with embedding generation error"""
        # Setup successful initial steps
        mock_file_storage.extract_user_info.return_value = (
            UserId("user123"),
            {"filename": "products.csv"},
        )
        mock_file_storage.validate_file_format.return_value = True
        mock_file_storage.download_content.return_value = sample_csv_content
        mock_configuration.get_max_file_size.return_value = 10000
        mock_configuration.get_max_rows.return_value = 10000

        mock_csv_processor.parse_csv.return_value = (
            ["name"],
            [ProductData({"name": "Product 1"}, 0)],
        )
        mock_csv_processor.clean_csv_data.side_effect = lambda x: x
        mock_csv_processor.validate_csv_data.return_value = True

        # Make embedding generation fail
        mock_embedding_generation_service.generate_product_embeddings.side_effect = Exception(
            "OpenAI API error"
        )

        command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
        result = await use_case.execute(command)

        assert result.success is False
        assert "OpenAI API error" in result.error_message


class TestProcessDocumentCommand:
    """Test ProcessDocumentCommand"""

    def test_create_command(self):
        """Test creating a process document command"""
        command = ProcessDocumentCommand(bucket="test-bucket", key="test-key")

        assert command.bucket == "test-bucket"
        assert command.key == "test-key"


class TestProcessDocumentResult:
    """Test ProcessDocumentResult"""

    def test_create_success_result(self):
        """Test creating a success result"""
        result = ProcessDocumentResult(
            success=True,
            document_id="doc123",
            user_id="user123",
            processed_rows=100,
            processing_time_ms=5000.0,
        )

        assert result.success is True
        assert result.document_id == "doc123"
        assert result.user_id == "user123"
        assert result.processed_rows == 100
        assert result.processing_time_ms == 5000.0
        assert result.error_message is None

    def test_create_failure_result(self):
        """Test creating a failure result"""
        result = ProcessDocumentResult(
            success=False, error_message="Processing failed", processing_time_ms=1000.0
        )

        assert result.success is False
        assert result.error_message == "Processing failed"
        assert result.processing_time_ms == 1000.0
        assert result.document_id is None
        assert result.user_id is None
        assert result.processed_rows == 0
