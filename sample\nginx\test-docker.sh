#!/bin/bash

# EzyChat Nginx Sample - Docker Test Script
set -e

IMAGE_NAME="ezychat-nginx-sample"
CONTAINER_NAME="ezychat-nginx-test"
PORT="8080"

echo "🚀 EzyChat Nginx Sample - Docker Test"
echo "======================================"

# Clean up any existing container
echo "🧹 Cleaning up existing container..."
docker rm -f $CONTAINER_NAME 2>/dev/null || true

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME .

# Run the container
echo "🏃 Starting container on port $PORT..."
docker run -d \
  --name $CONTAINER_NAME \
  -p $PORT:80 \
  -e SERVICE_NAME=nginx-sample \
  -e LOG_LEVEL=info \
  $IMAGE_NAME

# Wait for container to be ready with proper health check
echo "⏳ Waiting for container to be ready..."
sleep 2

# Check if container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
  echo "❌ Container failed to start!"
  docker logs $CONTAINER_NAME
  exit 1
fi

# Wait for nginx to be fully ready (check for "ready for start up" in logs)
echo "⏳ Waiting for nginx to be fully initialized..."
for i in {1..30}; do
  if docker logs $CONTAINER_NAME 2>&1 | grep -q "ready for start up"; then
    echo "✅ Nginx is ready"
    break
  elif [ $i -eq 30 ]; then
    echo "❌ Nginx failed to start properly"
    docker logs $CONTAINER_NAME
    exit 1
  else
    sleep 1
  fi
done

# Additional wait to ensure port binding is complete
sleep 2

# Test the endpoints
echo "🧪 Testing endpoints..."

# Function to test endpoint with retry
test_endpoint() {
  local endpoint=$1
  local name=$2
  echo -n "Testing $name... "
  
  for i in {1..10}; do
    if curl -s -f http://localhost:$PORT$endpoint > /dev/null 2>&1; then
      echo "✅ OK"
      return 0
    elif [ $i -eq 10 ]; then
      echo "❌ FAILED"
      echo "Debug info for $endpoint:"
      curl -v http://localhost:$PORT$endpoint || true
      docker logs $CONTAINER_NAME | tail -5
      return 1
    else
      sleep 1
    fi
  done
}

# Test all endpoints
test_endpoint "/health" "/health" || exit 1
test_endpoint "/status" "/status" || exit 1
test_endpoint "/" "/" || exit 1
test_endpoint "/api/" "/api/" || exit 1

echo ""
echo "🎉 All tests passed!"
echo ""
echo "📊 Container info:"
echo "  Image: $IMAGE_NAME"
echo "  Container: $CONTAINER_NAME"
echo "  URL: http://localhost:$PORT"
echo ""
echo "🔗 Test endpoints:"
echo "  Health: http://localhost:$PORT/health"
echo "  Status: http://localhost:$PORT/status"
echo "  Main: http://localhost:$PORT/"
echo "  API: http://localhost:$PORT/api/"
echo ""

# Keep container running for manual testing
read -p "Press Enter to stop and remove the container..."

# Clean up
echo "🧹 Stopping and removing container..."
docker rm -f $CONTAINER_NAME

echo "✅ Test completed successfully!"