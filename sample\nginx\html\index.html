<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EzyChat - Nginx Sample Service</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .endpoints {
            text-align: left;
            margin-top: 30px;
        }
        
        .endpoint {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .endpoint a {
            color: #fff;
            text-decoration: none;
            opacity: 0.8;
        }
        
        .endpoint a:hover {
            opacity: 1;
            text-decoration: underline;
        }
        
        .footer {
            margin-top: 40px;
            opacity: 0.7;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <h1>EzyChat</h1>
        <p class="subtitle">Nginx Sample Service - Test Environment</p>
        
        <div class="status-card">
            <h3>Service Status</h3>
            <div class="status-item">
                <span>Status:</span>
                <span style="color: #4ade80;">✓ Running</span>
            </div>
            <div class="status-item">
                <span>Service:</span>
                <span>nginx-sample</span>
            </div>
            <div class="status-item">
                <span>Version:</span>
                <span>1.0.0</span>
            </div>
            <div class="status-item">
                <span>Environment:</span>
                <span>Development</span>
            </div>
        </div>
        
        <div class="endpoints">
            <h3 style="margin-bottom: 15px;">Available Endpoints</h3>
            <div class="endpoint">
                <strong>GET</strong> <a href="/health">/health</a> - Health check endpoint
            </div>
            <div class="endpoint">
                <strong>GET</strong> <a href="/status">/status</a> - Detailed status information
            </div>
            <div class="endpoint">
                <strong>GET</strong> <a href="/api/">/api/*</a> - API placeholder endpoints
            </div>
        </div>
        
        <div class="footer">
            <p>This is a sample nginx service for EzyChat infrastructure testing.</p>
            <p>Based on the ECS services configuration from <code>ecs_services.yaml</code></p>
        </div>
    </div>
    
    <script>
        // Add current timestamp to status
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date().toISOString();
            const timestampElement = document.createElement('div');
            timestampElement.className = 'status-item';
            timestampElement.innerHTML = `<span>Last Updated:</span><span>${now}</span>`;
            document.querySelector('.status-card').appendChild(timestampElement);
        });
        
        // Simple health check indicator
        fetch('/health')
            .then(response => response.ok ? 'healthy' : 'unhealthy')
            .catch(() => 'unknown')
            .then(status => {
                console.log('Health check:', status);
            });
    </script>
</body>
</html>