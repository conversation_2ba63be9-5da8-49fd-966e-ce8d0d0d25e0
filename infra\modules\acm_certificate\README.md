# AWS ACM Certificate Module

This module creates an AWS ACM certificate with DNS validation. It handles the creation of the certificate, the DNS validation records, and the certificate validation.

## Features

- Creates an ACM certificate
- Creates DNS validation records in Route 53
- Validates the certificate
- Supports subject alternative names
- Supports custom tags

## Usage

```hcl
# Create a certificate in us-east-1 (required for CloudFront)
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "certificate" {
  source = "../../modules/acm_certificate"
  
  domain_name = "example.com"
  subject_alternative_names = ["www.example.com"]
  route53_zone_id = aws_route53_zone.example.zone_id
  
  tags = {
    Environment = "Production"
  }
  
  providers = {
    aws.certificate_region = aws.us_east_1
  }
}

# Use the certificate ARN
output "certificate_arn" {
  value = module.certificate.certificate_arn
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | >= 5.0.0 |

## Providers

| Name | Version |
|------|---------|
| aws.certificate_region | >= 5.0.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| domain_name | The domain name for the certificate | `string` | n/a | yes |
| subject_alternative_names | A list of subject alternative names to include in the certificate | `list(string)` | `[]` | no |
| route53_zone_id | The ID of the Route 53 hosted zone to create validation records in | `string` | n/a | yes |
| tags | A map of tags to add to the certificate | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| certificate_arn | The ARN of the certificate |
| certificate_domain_name | The domain name of the certificate |
| certificate_status | The status of the certificate |
| validation_record_fqdns | The FQDNs of the validation records |
