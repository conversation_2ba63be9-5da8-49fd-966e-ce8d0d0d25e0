import { inject, injectable } from 'tsyringe';
import QRCode from 'qrcode';
import { randomBytes, createHash } from 'crypto';

import { QRCode as QRCodeEntity } from '../../domain/entities/QRCode';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';

/**
 * QR Code generation and management service
 * Handles secure QR code creation, validation, and expiry
 */
@injectable()
export class QRCodeManager {
  private readonly qrCodes = new Map<string, QRCodeEntity>();
  private readonly defaultExpirySeconds: number;
  private readonly maxQRSize: number;
  private readonly cleanupInterval: NodeJS.Timeout;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    this.defaultExpirySeconds = this.config.getOptional('QR_TOKEN_EXPIRY_SEC', 300); // 5 minutes default
    this.maxQRSize = this.config.getOptional('MAX_QR_SIZE', 512); // Max QR image size
    
    // Start cleanup interval for expired QR codes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredQRCodes();
    }, 60000); // Cleanup every minute
  }

  /**
   * Generate a new QR code for WhatsApp authentication
   */
  async generateQRCode(
    userId: string,
    qrData: string,
    expirySeconds?: number,
    options?: {
      errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
      type?: 'image/png' | 'image/jpeg';
      quality?: number;
      margin?: number;
      color?: {
        dark?: string;
        light?: string;
      };
    }
  ): Promise<QRCodeEntity> {
    try {
      // Validate QR data
      this.validateQRData(qrData);

      // Generate secure QR code image
      const qrOptions = {
        errorCorrectionLevel: options?.errorCorrectionLevel || 'M',
        type: options?.type || 'image/png',
        quality: options?.quality || 0.92,
        margin: options?.margin || 1,
        color: {
          dark: options?.color?.dark || '#000000',
          light: options?.color?.light || '#FFFFFF'
        },
        width: this.maxQRSize
      };

      const qrImageData = await QRCode.toDataURL(qrData, qrOptions);

      // Create QR code entity
      const qrCodeEntity = QRCodeEntity.create(
        userId,
        qrData,
        qrImageData,
        expirySeconds || this.defaultExpirySeconds,
        {
          generatedAt: new Date().toISOString(),
          options: qrOptions,
          hash: this.generateQRHash(qrData)
        }
      );

      // Store QR code
      this.qrCodes.set(qrCodeEntity.id, qrCodeEntity);

      // Remove any existing QR codes for this user
      this.removeUserQRCodes(userId, qrCodeEntity.id);

      this.logger.info('QR code generated', {
        userId,
        qrId: qrCodeEntity.id,
        expiresAt: qrCodeEntity.expiresAt,
        timeToExpiry: qrCodeEntity.getTimeToExpiry()
      });

      return qrCodeEntity;
    } catch (error) {
      this.logger.error('Failed to generate QR code', {
        userId,
        error: (error as Error).message
      });
      throw new Error(`QR code generation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get QR code by ID
   */
  getQRCode(qrId: string): QRCodeEntity | null {
    const qrCode = this.qrCodes.get(qrId);
    
    if (!qrCode) {
      return null;
    }

    // Check if expired
    if (qrCode.isExpired()) {
      this.qrCodes.delete(qrId);
      return null;
    }

    return qrCode;
  }

  /**
   * Get active QR code for user
   */
  getUserQRCode(userId: string): QRCodeEntity | null {
    for (const qrCode of this.qrCodes.values()) {
      if (qrCode.userId === userId && qrCode.isValidForScanning()) {
        return qrCode;
      }
    }
    return null;
  }

  /**
   * Mark QR code as scanned
   */
  markQRCodeAsScanned(qrId: string): QRCodeEntity | null {
    const qrCode = this.qrCodes.get(qrId);
    
    if (!qrCode || !qrCode.isValidForScanning()) {
      return null;
    }

    const scannedQRCode = qrCode.markAsScanned();
    this.qrCodes.set(qrId, scannedQRCode);

    this.logger.info('QR code marked as scanned', {
      userId: qrCode.userId,
      qrId,
      scannedAt: scannedQRCode.scannedAt
    });

    return scannedQRCode;
  }

  /**
   * Refresh QR code (generate new one for same user)
   */
  async refreshQRCode(userId: string, newQRData: string): Promise<QRCodeEntity> {
    try {
      // Remove existing QR codes for user
      this.removeUserQRCodes(userId);

      // Generate new QR code
      const newQRCode = await this.generateQRCode(userId, newQRData);

      this.logger.info('QR code refreshed', {
        userId,
        newQrId: newQRCode.id,
        expiresAt: newQRCode.expiresAt
      });

      return newQRCode;
    } catch (error) {
      this.logger.error('Failed to refresh QR code', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Validate QR code data format and security
   */
  private validateQRData(qrData: string): void {
    if (!qrData || qrData.trim().length === 0) {
      throw new Error('QR data cannot be empty');
    }

    if (qrData.length > 4096) {
      throw new Error('QR data too long');
    }

    // Real WhatsApp QR data validation
    // Baileys QR data is typically a long base64-encoded string or comma-separated values
    if (qrData.length < 20) {
      throw new Error('Invalid WhatsApp QR data format');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /javascript:/i,
      /vbscript:/i,
      /<script/i,
      /on\w+=/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(qrData)) {
        throw new Error('QR data contains suspicious content');
      }
    }
  }

  /**
   * Generate secure hash for QR data
   */
  private generateQRHash(qrData: string): string {
    const salt = randomBytes(16);
    const hash = createHash('sha256');
    hash.update(qrData + salt.toString('hex'));
    return hash.digest('hex');
  }

  /**
   * Remove QR codes for a user (except specified ID)
   */
  private removeUserQRCodes(userId: string, exceptId?: string): void {
    const toRemove: string[] = [];
    
    for (const [id, qrCode] of this.qrCodes.entries()) {
      if (qrCode.userId === userId && id !== exceptId) {
        toRemove.push(id);
      }
    }

    for (const id of toRemove) {
      this.qrCodes.delete(id);
    }

    if (toRemove.length > 0) {
      this.logger.debug('Removed old QR codes for user', {
        userId,
        removedCount: toRemove.length
      });
    }
  }

  /**
   * Cleanup expired QR codes
   */
  private cleanupExpiredQRCodes(): void {
    const toRemove: string[] = [];
    
    for (const [id, qrCode] of this.qrCodes.entries()) {
      if (qrCode.isExpired()) {
        toRemove.push(id);
      }
    }

    for (const id of toRemove) {
      this.qrCodes.delete(id);
    }

    if (toRemove.length > 0) {
      this.logger.debug('Cleaned up expired QR codes', {
        removedCount: toRemove.length
      });
    }
  }

  /**
   * Get QR code statistics
   */
  getStatistics(): {
    totalQRCodes: number;
    activeQRCodes: number;
    expiredQRCodes: number;
    scannedQRCodes: number;
  } {
    let active = 0;
    let expired = 0;
    let scanned = 0;

    for (const qrCode of this.qrCodes.values()) {
      if (qrCode.status === 'scanned') {
        scanned++;
      } else if (qrCode.isExpired()) {
        expired++;
      } else if (qrCode.isValidForScanning()) {
        active++;
      }
    }

    return {
      totalQRCodes: this.qrCodes.size,
      activeQRCodes: active,
      expiredQRCodes: expired,
      scannedQRCodes: scanned
    };
  }

  /**
   * Cleanup resources
   */
  /**
   * Alias for generateQRCode method (for backward compatibility)
   */
  async generateQR(
    userId: string,
    qrData: string,
    expirySeconds?: number
  ): Promise<QRCodeEntity> {
    return this.generateQRCode(userId, qrData, expirySeconds);
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.qrCodes.clear();
    this.logger.info('QRCodeManager destroyed');
  }
}
