# AWS SSM Parameter Store Migration

This document describes the migration from AWS Secrets Manager to AWS Systems Manager (SSM) Parameter Store for the Document Ingestion Pipeline.

## Overview

The Document Ingestion Pipeline has been migrated from using AWS Secrets Manager to AWS SSM Parameter Store for retrieving sensitive configuration values. This change provides better cost efficiency, hierarchical parameter organization, and improved integration with AWS services.

## Changes Made

### 1. Core Implementation Changes

- **Replaced**: `src/infrastructure/secrets_manager.py`
- **With**: `src/infrastructure/parameter_store.py`
- **Updated**: `src/infrastructure/configuration.py` to import from the new module

### 2. Parameter Naming Convention

The new implementation uses hierarchical parameter names following AWS best practices:

| Legacy Secret Name | New SSM Parameter Name |
|-------------------|------------------------|
| `lambda/openai-api-key` | `/ezychat/document-ingestion/openai/api-key` |
| `lambda/supabase-url` | `/ezychat/document-ingestion/supabase/url` |
| `lambda/supabase-service-role-key` | `/ezychat/document-ingestion/supabase/service-role-key` |
| `lambda/supabase-anon-key` | `/ezychat/document-ingestion/supabase/anon-key` |

### 3. Backward Compatibility

The new implementation maintains full backward compatibility:

- All existing function signatures remain unchanged
- Legacy secret names are automatically converted to SSM parameter names
- Environment variable fallback behavior is preserved
- Global `secrets_manager` instance is aliased to the new `parameter_store_manager`

## Technical Details

### ParameterStoreManager Class

The new `ParameterStoreManager` class provides:

- **SSM Integration**: Uses `boto3.client('ssm')` instead of `boto3.client('secretsmanager')`
- **Parameter Retrieval**: Uses `get_parameter()` with `WithDecryption=True` for SecureString parameters
- **Caching**: Maintains the same caching mechanism for performance
- **Error Handling**: Handles `ParameterNotFound` instead of `ResourceNotFoundException`
- **Environment Detection**: Same AWS environment detection logic
- **LocalStack Support**: Full compatibility with LocalStack for local development

### Key Methods

- `get_parameter()`: New primary method for parameter retrieval
- `get_parameter_string()`: String parameter retrieval with empty string fallback
- `get_parameter_json()`: JSON parameter parsing for complex parameters
- `get_secret()`, `get_secret_string()`, `get_secret_json()`: Backward compatibility methods

### Environment Variable Fallback

The fallback to environment variables remains unchanged:

- `OPENAI_API_KEY`
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `SUPABASE_ANON_KEY`

## AWS Configuration Requirements

### IAM Permissions

Update IAM policies to include SSM Parameter Store permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:GetParametersByPath"
      ],
      "Resource": [
        "arn:aws:ssm:*:*:parameter/ezychat/document-ingestion/*"
      ]
    }
  ]
}
```

### Parameter Creation

Create parameters in AWS SSM Parameter Store:

```bash
# OpenAI API Key (SecureString)
aws ssm put-parameter \
  --name "/ezychat/document-ingestion/openai/api-key" \
  --value "your-openai-api-key" \
  --type "SecureString" \
  --description "OpenAI API key for document ingestion"

# Supabase URL (String)
aws ssm put-parameter \
  --name "/ezychat/document-ingestion/supabase/url" \
  --value "https://your-project.supabase.co" \
  --type "String" \
  --description "Supabase project URL"

# Supabase Service Role Key (SecureString)
aws ssm put-parameter \
  --name "/ezychat/document-ingestion/supabase/service-role-key" \
  --value "your-service-role-key" \
  --type "SecureString" \
  --description "Supabase service role key"

# Supabase Anonymous Key (String)
aws ssm put-parameter \
  --name "/ezychat/document-ingestion/supabase/anon-key" \
  --value "your-anon-key" \
  --type "String" \
  --description "Supabase anonymous key"
```

## Testing

### Unit Tests

Comprehensive unit tests have been added in `tests/unit/infrastructure/test_parameter_store.py`:

- **41 test cases** covering all functionality
- **Mocked boto3 SSM client** to avoid actual AWS calls
- **Environment variable fallback testing**
- **Error handling scenarios**
- **Backward compatibility verification**
- **LocalStack configuration testing**

### Running Tests

```bash
# Run parameter store tests
python -m pytest tests/unit/infrastructure/test_parameter_store.py -v

# Run all infrastructure tests
python -m pytest tests/unit/infrastructure/ -v
```

## Migration Checklist

- [x] Create new `parameter_store.py` module
- [x] Update `configuration.py` imports
- [x] Implement parameter name conversion mapping
- [x] Maintain backward compatibility
- [x] Add comprehensive unit tests
- [x] Update documentation
- [ ] Create SSM parameters in AWS
- [ ] Update IAM policies
- [ ] Deploy and test in staging environment
- [ ] Remove old Secrets Manager secrets (after verification)

## Benefits

1. **Cost Efficiency**: SSM Parameter Store is more cost-effective than Secrets Manager for simple key-value pairs
2. **Hierarchical Organization**: Better parameter organization with path-based structure
3. **Native AWS Integration**: Better integration with other AWS services
4. **Versioning**: Built-in parameter versioning capabilities
5. **Tagging**: Enhanced tagging and organization features

## Rollback Plan

If rollback is needed:

1. Revert the import in `configuration.py` back to `secrets_manager`
2. Restore the original `secrets_manager.py` file from version control
3. Ensure AWS Secrets Manager secrets are still available
4. Update IAM policies to restore Secrets Manager permissions

The backward compatibility ensures that no code changes are required for rollback.
