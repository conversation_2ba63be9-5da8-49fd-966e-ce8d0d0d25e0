# Dify Webhook Handler

This service handles webhook events from <PERSON><PERSON> and orchestrates responses through the agent workflow.

## Overview

The dify-webhook service processes incoming webhook requests from <PERSON>fy, validates the payload, and triggers appropriate agent workflows based on the event type.

## Webhook Formats

### Incoming Webhook Structure
```json
{
  "event_type": "message.received",
  "conversation_id": "string",
  "user_id": "string",
  "message": {
    "content": "string",
    "type": "text|image|file",
    "timestamp": "ISO8601"
  },
  "metadata": {
    "channel": "whatsapp|web|api",
    "session_id": "string"
  }
}
```

## Routes

- `POST /webhook/dify` - Main webhook endpoint for Dify events
- `GET /health` - Health check endpoint
- `POST /webhook/test` - Test endpoint for development

## Processing Logic

1. **Validation**: Verify webhook signature and payload structure
2. **Routing**: Determine which agent tools to invoke based on event type
3. **Orchestration**: Call appropriate tools in sequence (e.g., agent-orchestrator → whatsapp-manager)
4. **Response**: Return appropriate HTTP status and acknowledgment

## Environment Variables

- `DIFY_WEBHOOK_SECRET` - Secret for webhook signature verification
- `AGENT_ORCHESTRATOR_URL` - URL for the Python agent orchestrator
- `WHATSAPP_MANAGER_URL` - URL for the WhatsApp manager service
- `PORT` - Port to run the webhook service (default: 3000)

## Development

```bash
npm install
npm run dev
```

## Deployment

This service is deployed as a containerized application in ECS.