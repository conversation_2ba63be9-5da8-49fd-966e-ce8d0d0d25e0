# Node.js Shared Libraries

Shared Node.js utilities, types, and common functionality used across all Node.js services in the ezychat platform.

## Purpose

This library provides reusable components that standardize common operations across Node.js services:
- TypeScript interfaces and types
- Error handling and logging utilities
- Database connection helpers
- API response formatting
- Authentication middleware
- Configuration management

## Modules

### `types/`
TypeScript definitions and interfaces:
- `message.ts` - Message types (text, media, webhook payloads)
- `conversation.ts` - Conversation and context types
- `user.ts` - User and contact types
- `api.ts` - Standard API response types
- `webhook.ts` - Webhook payload types

### `utils/`
Utility functions and helpers:
- `logger.ts` - Winston-based logging configuration
- `auth.ts` - JWT token validation and user authentication
- `database.ts` - Database connection and query helpers
- `cache.ts` - Redis caching utilities
- `validation.ts` - Input validation using Joi/Zod
- `response.ts` - Standard API response formatters

### `middleware/`
Express middleware components:
- `errorHandler.ts` - Global error handling middleware
- `rateLimiter.ts` - Rate limiting middleware
- `auth.ts` - Authentication middleware
- `cors.ts` - CORS configuration
- `validation.ts` - Request validation middleware

### `errors/`
Custom error classes:
- `BaseError.ts` - Base error class
- `APIError.ts` - API-specific errors
- `ValidationError.ts` - Data validation errors
- `AuthError.ts` - Authentication errors

## Example Usage

```typescript
// Import shared types
import { MessageCreate, MessageResponse, APIResponse } from '@ezychat/libs';
import { logger, createResponse } from '@ezychat/libs/utils';
import { validateRequest } from '@ezychat/libs/middleware';

// Setup Express app with shared middleware
import express from 'express';
import { errorHandler, rateLimiter } from '@ezychat/libs/middleware';

const app = express();

app.use(rateLimiter);
app.use(express.json());

// Use shared validation and response formatting
app.post('/messages', 
  validateRequest(MessageCreateSchema),
  async (req: Request, res: Response) => {
    try {
      const messageData: MessageCreate = req.body;
      
      // Process message logic here
      const result = await processMessage(messageData);
      
      res.json(createResponse<MessageResponse>({
        success: true,
        data: result,
        message: 'Message processed successfully'
      }));
    } catch (error) {
      logger.error('Error processing message:', error);
      throw error; // Will be handled by error middleware
    }
  }
);

app.use(errorHandler);
```

## Installation

```bash
# Install in development mode for local development
npm install -E file:./libs/node

# Or install from npm registry
npm install @ezychat/libs
```

## Development

```bash
# Setup development environment
cd libs/node
npm install

# Build TypeScript
npm run build

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Type checking
npm run type-check

# Watch mode for development
npm run dev
```

## TypeScript Configuration

The library includes a base TypeScript configuration that other services can extend:

```json
// In your service's tsconfig.json
{
  "extends": "@ezychat/libs/tsconfig.base.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "references": [
    { "path": "../../libs/node" }
  ]
}
```

## Environment Configuration

The library uses environment variables for configuration:

- `LOG_LEVEL` - Logging level (debug, info, warn, error)
- `JWT_SECRET` - Secret key for JWT token validation
- `DATABASE_URL` - Database connection string
- `REDIS_URL` - Redis connection string
- `NODE_ENV` - Environment (development, staging, production)

## Testing Utilities

The library includes testing utilities:

```typescript
import { createMockRequest, createMockResponse } from '@ezychat/libs/testing';

describe('Message Handler', () => {
  it('should process message correctly', async () => {
    const req = createMockRequest({
      body: { message: 'Hello World' }
    });
    const res = createMockResponse();
    
    await messageHandler(req, res);
    
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: expect.any(Object)
    });
  });
});
```

## Publishing

```bash
# Build and test
npm run build
npm test

# Version bump (patch/minor/major)
npm version patch

# Publish to npm
npm publish

# Publish with tag
npm publish --tag beta
```

## Versioning

This library follows semantic versioning (SemVer):
- MAJOR version for incompatible API changes
- MINOR version for backwards-compatible functionality additions
- PATCH version for backwards-compatible bug fixes

## Dependencies

Core dependencies:
- Express.js for web framework utilities
- Winston for logging
- Joi/Zod for validation
- Redis for caching
- JWT for authentication