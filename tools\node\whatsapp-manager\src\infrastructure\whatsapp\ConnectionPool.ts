import { inject, injectable } from 'tsyringe';

import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { SessionStatus } from '../../domain/entities/SessionEntity';

/**
 * Connection pool entry
 */
export interface PooledConnection {
  userId: string;
  status: SessionStatus;
  createdAt: Date;
  lastUsed: Date;
  useCount: number;
  priority: number;
  metadata: Record<string, any>;
}

/**
 * Pool statistics
 */
export interface PoolStatistics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  maxPoolSize: number;
  currentUtilization: number;
  averageUseCount: number;
  oldestConnection?: Date;
  newestConnection?: Date;
}

/**
 * Pool configuration
 */
export interface PoolConfig {
  maxSize: number;
  maxIdleTime: number; // milliseconds
  maxConnectionAge: number; // milliseconds
  cleanupInterval: number; // milliseconds
  priorityThreshold: number;
}

/**
 * Connection pool for managing WhatsApp connections
 * Implements resource pooling with lifecycle management and optimization
 */
@injectable()
export class ConnectionPool {
  private readonly connections = new Map<string, PooledConnection>();
  private readonly config: PoolConfig;
  private readonly cleanupInterval: NodeJS.Timeout;
  private readonly waitingQueue: Array<{
    userId: string;
    resolve: (connection: PooledConnection) => void;
    reject: (error: Error) => void;
    timestamp: Date;
  }> = [];

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private configService: IConfigService
  ) {
    this.config = {
      maxSize: this.configService.getOptional('MAX_CONCURRENT_SESSIONS', 100),
      maxIdleTime: this.configService.getOptional('CONNECTION_MAX_IDLE_TIME_MS', 30 * 60 * 1000), // 30 minutes
      maxConnectionAge: this.configService.getOptional('CONNECTION_MAX_AGE_MS', 24 * 60 * 60 * 1000), // 24 hours
      cleanupInterval: this.configService.getOptional('CONNECTION_CLEANUP_INTERVAL_MS', 5 * 60 * 1000), // 5 minutes
      priorityThreshold: this.configService.getOptional('CONNECTION_PRIORITY_THRESHOLD', 10)
    };

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);

    this.logger.info('ConnectionPool initialized', {
      config: this.config
    });
  }

  /**
   * Acquire a connection from the pool
   */
  async acquireConnection(userId: string, priority: number = 0): Promise<PooledConnection> {
    try {
      // Check if connection already exists
      const existing = this.connections.get(userId);
      if (existing) {
        existing.lastUsed = new Date();
        existing.useCount++;
        
        this.logger.debug('Connection reused from pool', {
          userId,
          useCount: existing.useCount,
          status: existing.status
        });
        
        return existing;
      }

      // Check pool capacity
      if (this.connections.size >= this.config.maxSize) {
        // Try to free up space
        const freed = await this.freeUpSpace();
        
        if (!freed && this.connections.size >= this.config.maxSize) {
          // Add to waiting queue
          return this.addToWaitingQueue(userId);
        }
      }

      // Create new connection entry
      const connection: PooledConnection = {
        userId,
        status: 'initializing',
        createdAt: new Date(),
        lastUsed: new Date(),
        useCount: 1,
        priority,
        metadata: {}
      };

      this.connections.set(userId, connection);

      this.logger.info('New connection added to pool', {
        userId,
        priority,
        poolSize: this.connections.size,
        maxSize: this.config.maxSize
      });

      return connection;
    } catch (error) {
      this.logger.error('Failed to acquire connection', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Release a connection back to the pool
   */
  releaseConnection(userId: string): void {
    const connection = this.connections.get(userId);
    if (!connection) {
      this.logger.warn('Attempted to release non-existent connection', { userId });
      return;
    }

    connection.lastUsed = new Date();

    this.logger.debug('Connection released to pool', {
      userId,
      useCount: connection.useCount,
      status: connection.status
    });

    // Process waiting queue
    this.processWaitingQueue();
  }

  /**
   * Remove connection from pool
   */
  removeConnection(userId: string): boolean {
    const connection = this.connections.get(userId);
    if (!connection) {
      return false;
    }

    this.connections.delete(userId);

    this.logger.info('Connection removed from pool', {
      userId,
      useCount: connection.useCount,
      age: Date.now() - connection.createdAt.getTime(),
      poolSize: this.connections.size
    });

    // Process waiting queue
    this.processWaitingQueue();

    return true;
  }

  /**
   * Update connection status
   */
  updateConnectionStatus(userId: string, status: SessionStatus, metadata?: Record<string, any>): void {
    const connection = this.connections.get(userId);
    if (!connection) {
      this.logger.warn('Attempted to update status of non-existent connection', { userId, status });
      return;
    }

    const oldStatus = connection.status;
    connection.status = status;
    connection.lastUsed = new Date();
    
    if (metadata) {
      connection.metadata = { ...connection.metadata, ...metadata };
    }

    this.logger.debug('Connection status updated', {
      userId,
      oldStatus,
      newStatus: status,
      metadata: Object.keys(metadata || {})
    });
  }

  /**
   * Get connection from pool
   */
  getConnection(userId: string): PooledConnection | null {
    return this.connections.get(userId) || null;
  }

  /**
   * Get all connections
   */
  getAllConnections(): PooledConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * Get connections by status
   */
  getConnectionsByStatus(status: SessionStatus): PooledConnection[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.status === status);
  }

  /**
   * Get pool statistics
   */
  getStatistics(): PoolStatistics {
    const connections = Array.from(this.connections.values());
    const activeConnections = connections.filter(c => 
      c.status === 'connected' || c.status === 'connecting'
    ).length;
    const idleConnections = connections.filter(c => 
      c.status === 'disconnected' || c.status === 'qr_pending'
    ).length;

    const totalUseCount = connections.reduce((sum, c) => sum + c.useCount, 0);
    const averageUseCount = connections.length > 0 ? totalUseCount / connections.length : 0;

    const createdTimes = connections.map(c => c.createdAt);
    const oldestConnection = createdTimes.length > 0 ? new Date(Math.min(...createdTimes.map(d => d.getTime()))) : undefined;
    const newestConnection = createdTimes.length > 0 ? new Date(Math.max(...createdTimes.map(d => d.getTime()))) : undefined;

    return {
      totalConnections: this.connections.size,
      activeConnections,
      idleConnections,
      maxPoolSize: this.config.maxSize,
      currentUtilization: (this.connections.size / this.config.maxSize) * 100,
      averageUseCount,
      oldestConnection,
      newestConnection
    };
  }

  /**
   * Add to waiting queue when pool is full
   */
  private addToWaitingQueue(userId: string): Promise<PooledConnection> {
    return new Promise((resolve, reject) => {
      const queueEntry = {
        userId,
        resolve,
        reject,
        timestamp: new Date()
      };

      this.waitingQueue.push(queueEntry);

      this.logger.debug('Added to waiting queue', {
        userId,
        queueLength: this.waitingQueue.length
      });

      // Set timeout for queue entry
      setTimeout(() => {
        const index = this.waitingQueue.indexOf(queueEntry);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
          reject(new Error('Connection request timed out in queue'));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Process waiting queue
   */
  private processWaitingQueue(): void {
    if (this.waitingQueue.length === 0 || this.connections.size >= this.config.maxSize) {
      return;
    }

    const queueEntry = this.waitingQueue.shift();
    if (!queueEntry) {
      return;
    }

    // Try to acquire connection for queued request
    this.acquireConnection(queueEntry.userId)
      .then(connection => queueEntry.resolve(connection))
      .catch(error => queueEntry.reject(error));
  }

  /**
   * Free up space in the pool
   */
  private async freeUpSpace(): Promise<boolean> {
    const connections = Array.from(this.connections.values());
    
    // Find candidates for removal (idle, old, or low priority)
    const candidates = connections
      .filter(conn => {
        const age = Date.now() - conn.lastUsed.getTime();
        const isIdle = age > this.config.maxIdleTime;
        const isOld = Date.now() - conn.createdAt.getTime() > this.config.maxConnectionAge;
        const isLowPriority = conn.priority < this.config.priorityThreshold;
        const isDisconnected = conn.status === 'disconnected' || conn.status === 'error';
        
        return isIdle || isOld || isLowPriority || isDisconnected;
      })
      .sort((a, b) => {
        // Sort by priority (lower first), then by last used time (older first)
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        return a.lastUsed.getTime() - b.lastUsed.getTime();
      });

    if (candidates.length > 0) {
      const toRemove = candidates[0];
      this.removeConnection(toRemove.userId);
      
      this.logger.info('Freed up space in connection pool', {
        removedUserId: toRemove.userId,
        reason: 'space_needed',
        age: Date.now() - toRemove.createdAt.getTime(),
        idleTime: Date.now() - toRemove.lastUsed.getTime()
      });
      
      return true;
    }

    return false;
  }

  /**
   * Perform periodic cleanup
   */
  private performCleanup(): void {
    const now = Date.now();
    const connectionsToRemove: string[] = [];

    for (const [userId, connection] of this.connections.entries()) {
      const age = now - connection.createdAt.getTime();
      const idleTime = now - connection.lastUsed.getTime();

      // Remove old or idle connections
      if (age > this.config.maxConnectionAge || idleTime > this.config.maxIdleTime) {
        connectionsToRemove.push(userId);
      }
    }

    for (const userId of connectionsToRemove) {
      this.removeConnection(userId);
    }

    if (connectionsToRemove.length > 0) {
      this.logger.info('Connection pool cleanup completed', {
        removedConnections: connectionsToRemove.length,
        remainingConnections: this.connections.size,
        queueLength: this.waitingQueue.length
      });
    }

    // Clean up expired queue entries
    const expiredQueueEntries = this.waitingQueue.filter(entry => 
      now - entry.timestamp.getTime() > 60000 // 1 minute
    );

    for (const entry of expiredQueueEntries) {
      const index = this.waitingQueue.indexOf(entry);
      if (index !== -1) {
        this.waitingQueue.splice(index, 1);
        entry.reject(new Error('Queue entry expired'));
      }
    }
  }

  /**
   * Check if pool has capacity
   */
  hasCapacity(): boolean {
    return this.connections.size < this.config.maxSize;
  }

  /**
   * Get pool utilization percentage
   */
  getUtilization(): number {
    return (this.connections.size / this.config.maxSize) * 100;
  }

  /**
   * Clear all connections
   */
  clear(): void {
    const connectionCount = this.connections.size;
    this.connections.clear();
    
    // Reject all waiting queue entries
    for (const entry of this.waitingQueue) {
      entry.reject(new Error('Connection pool cleared'));
    }
    this.waitingQueue.length = 0;

    this.logger.info('Connection pool cleared', {
      removedConnections: connectionCount
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.clear();
    this.logger.info('ConnectionPool destroyed');
  }
}
