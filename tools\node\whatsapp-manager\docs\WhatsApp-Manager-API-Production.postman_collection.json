{"info": {"name": "WhatsApp Manager API - Production Ready", "description": "Complete API collection for WhatsApp Manager with production features including QR management, message operations, health monitoring, and webhook integration.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "apiPrefix", "value": "/api", "type": "string"}, {"key": "userId", "value": "test-user-123", "type": "string"}, {"key": "phoneNumber", "value": "<EMAIL>", "type": "string"}], "item": [{"name": "Health & Monitoring", "item": [{"name": "Simple Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health"]}}, "response": []}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/detailed?components=database,memory,sessions", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "detailed"], "query": [{"key": "components", "value": "database,memory,sessions"}]}}, "response": []}, {"name": "Kubernetes Readiness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/readiness", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "readiness"]}}, "response": []}, {"name": "Kubernetes Liveness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/liveness", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "liveness"]}}, "response": []}, {"name": "System Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/metrics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "metrics"]}}, "response": []}, {"name": "Monitoring Dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/monitoring?limit=100", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "monitoring"], "query": [{"key": "limit", "value": "100"}]}}, "response": []}, {"name": "System Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/monitoring/alerts?active=true&limit=50", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "monitoring", "alerts"], "query": [{"key": "active", "value": "true"}, {"key": "limit", "value": "50"}]}}, "response": []}, {"name": "Prometheus Metrics Export", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/monitoring/export?format=prometheus", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "monitoring", "export"], "query": [{"key": "format", "value": "prometheus"}]}}, "response": []}, {"name": "Trigger System Cleanup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dryRun\": false\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/cleanup", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "cleanup"]}}, "response": []}, {"name": "Cleanup Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/cleanup/statistics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "cleanup", "statistics"]}}, "response": []}, {"name": "Check Component Health - Database", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/component/database", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "component", "database"]}}, "response": []}, {"name": "Check Component Health - Memory", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/component/memory", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "component", "memory"]}}, "response": []}]}, {"name": "Session Management", "item": [{"name": "Start Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phoneNumber\": \"{{phoneNumber}}\",\n  \"deviceName\": \"WhatsApp Manager Production\",\n  \"browserName\": \"Chrome\"\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Get Session Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Terminate Session", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Manual termination\"\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "List All Sessions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions?status=connected&limit=50&offset=0", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions"], "query": [{"key": "status", "value": "connected"}, {"key": "limit", "value": "50"}, {"key": "offset", "value": "0"}]}}, "response": []}]}, {"name": "QR Code Management", "item": [{"name": "Refresh QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"force\": true\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/refresh", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "refresh"]}}, "response": []}, {"name": "Get QR Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/status", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "status"]}}, "response": []}]}, {"name": "Message Operations", "item": [{"name": "Send Text Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"Hello from WhatsApp Manager Production!\",\n  \"type\": \"text\"\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]}}, "response": []}, {"name": "Send Media Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"Check out this image!\",\n  \"type\": \"image\",\n  \"mediaUrl\": \"https://example.com/image.jpg\",\n  \"metadata\": {\n    \"caption\": \"Sample image\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]}}, "response": []}, {"name": "Get Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages?limit=50&offset=0&type=text&hasMedia=false", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages"], "query": [{"key": "limit", "value": "50"}, {"key": "offset", "value": "0"}, {"key": "type", "value": "text"}, {"key": "hasMedia", "value": "false"}]}}, "response": []}]}]}