# Lambda Node.js Sample Service

A production-ready AWS Lambda function template built with Node.js that demonstrates modern serverless deployment patterns using centralized ECR repositories and cross-account access.

## What This Service Does

This service provides a **containerized Lambda function template** that showcases:

- **Centralized Container Registry**: Images are stored in a shared ECR repository (account ************) and deployed across multiple AWS accounts
- **Cross-Account Access**: Demonstrates secure cross-account ECR access patterns for enterprise environments
- **Modern Lambda Deployment**: Uses container images instead of ZIP packages for better dependency management and faster cold starts
- **Production Patterns**: Includes health checks, proper error handling, and structured logging

## Key Features

- 🚀 **HTTP-like routing** with `/sample` and `/health` endpoints
- 🐳 **Container-first deployment** with Docker and ECR
- 🔒 **Cross-account ECR access** with automatic policy management
- 📦 **Dual deployment modes**: ZIP package (development) and container image (production)
- 🏥 **Health monitoring** with built-in health check endpoint
- 🛠️ **Developer-friendly** with comprehensive testing and diagnostic tools
- ⚡ **AWS Lambda Node.js 18** runtime optimized for performance

## Quick Start

### Prerequisites

- **AWS CLI** configured with appropriate permissions for both accounts
- **Docker** installed and running (for container deployment)
- **Make** utility for build automation
- **Access to centralized ECR** (account ************) for image storage

### 🚀 Deploy with Container Image (Recommended)

The production-ready deployment method:

```bash
# Build, push to centralized ECR, and deploy Lambda function
make deploy
```

**What happens:**

1. 🏗️ Builds Docker image with Node.js 18 Lambda runtime
2. 🔐 Authenticates with centralized ECR (************)
3. 📤 Pushes image to centralized repository
4. ⚡ Creates Lambda function using the ECR image
5. 🧪 Runs automated tests to verify deployment

### 📦 Deploy with ZIP Package (Development)

For quick development iterations:

```bash
# Deploy Lambda function using ZIP package (no Docker required)
make deploy-zip
```

**What happens:**

1. 📦 Creates ZIP package with source code and dependencies
2. 🔑 Creates IAM execution role with necessary permissions
3. ⚡ Deploys Lambda function to ap-southeast-5 region
4. ✅ Automatically tests the deployed function

## Available Commands

### 🚀 Deployment Commands

- `make deploy` - **Full container deployment** (build → push to ECR → deploy Lambda)
- `make deploy-zip` - **ZIP package deployment** (no Docker required, faster for development)

### 🔧 Development Commands

- `make build` - Build Docker image locally
- `make push` - Build and push image to centralized ECR
- `make run-local` - Run container locally for testing (port 9000)

### 🧪 Testing & Diagnostics

- `make test-ecr` - **Automated ECR test** (creates temp Lambda → tests → cleans up)
- `./diagnose-ecr-access.sh` - Diagnose ECR cross-account access issues
- `./test-ecr-lambda.sh` - Manual ECR Lambda function test

### 🛠️ Utility Commands

- `make help` - Show all available commands with descriptions
- `make clean` - Clean up local Docker images and temporary files

## 🌐 API Endpoints

This Lambda function provides HTTP-like routing that works with API Gateway, ALB, or direct invocation.

### 🎯 Primary Endpoint: `/sample`

**Purpose**: Main application endpoint returning "Hello World!" with request metadata
**Method**: GET (default)

**Example Response:**

```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*"
  },
  "body": {
    "message": "Hello World!",
    "timestamp": "2025-08-03T10:30:00.000Z",
    "path": "/sample",
    "method": "GET",
    "requestId": "abc-123-def-456",
    "version": "1.0.0"
  }
}
```

### 🏥 Health Check: `/health`

**Purpose**: Service health monitoring and uptime checks
**Method**: GET

**Example Response:**

```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "status": "healthy",
    "timestamp": "2025-08-03T10:30:00.000Z",
    "version": "1.0.0"
  }
}
```

### 🚫 Unknown Routes

Any other path returns a 404 with available routes listed.

## 🧪 Testing

### 🤖 Automated ECR Testing (Recommended)

Test the complete ECR deployment pipeline:

```bash
# Full automated test: build → push → create Lambda → test → cleanup
make test-ecr
```

**Test Flow:**

1. ✅ Verifies ECR image exists in centralized repository
2. 🔧 Creates temporary Lambda function from ECR image
3. 🧪 Tests `/sample` endpoint (Hello World response)
4. 🏥 Tests `/health` endpoint (health check)
5. 🚫 Tests unknown route handling (404 response)
6. 🧹 Automatically cleans up test resources

### 🔍 Manual Testing

Test a deployed function directly:

```bash
# Test the main endpoint
aws lambda invoke \
  --function-name lambda-nodejs-sample \
  --payload '{"path":"/sample","httpMethod":"GET"}' \
  --region ap-southeast-5 \
  response.json && cat response.json

# Test health check
aws lambda invoke \
  --function-name lambda-nodejs-sample \
  --payload '{"path":"/health","httpMethod":"GET"}' \
  --region ap-southeast-5 \
  health-response.json && cat health-response.json
```

### 🩺 Troubleshooting

If you encounter ECR access issues:

```bash
# Diagnose cross-account ECR access
./diagnose-ecr-access.sh
```

## ⚙️ Configuration

| Setting             | Value                  | Description                       |
| ------------------- | ---------------------- | --------------------------------- |
| **AWS Region**      | `ap-southeast-5`       | Malaysia region for all resources |
| **Centralized ECR** | `************`         | Shared container registry account |
| **Lambda Account**  | `************`         | Target deployment account         |
| **Function Name**   | `lambda-nodejs-sample` | Default Lambda function name      |
| **Runtime**         | `Node.js 18`           | AWS Lambda runtime version        |
| **Architecture**    | `x86_64`               | Processor architecture            |

## 🏗️ Architecture

### Cross-Account ECR Pattern

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│    Developer        │    │  Centralized ECR    │    │   Lambda Function   │
│  (************)     │───▶│  (************)     │───▶│  (************)     │
│                     │    │                     │    │                     │
│ • Build images      │    │ • Store images      │    │ • Run functions     │
│ • Push to ECR       │    │ • Auto policies     │    │ • Pull from ECR     │
│ • Deploy functions  │    │ • Cross-account     │    │ • Execute code      │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

### Key Benefits

- 🏢 **Centralized Management**: Single ECR repository for all environments
- 🔒 **Security**: Automatic cross-account policies with least privilege
- 🚀 **Performance**: Faster deployments with container image caching
- 📊 **Governance**: Centralized image scanning and compliance
- 💰 **Cost Optimization**: Shared storage reduces duplication

## 💻 Local Development

### Run Container Locally

Test the Lambda function on your local machine:

```bash
# Start the Lambda runtime interface emulator
make run-local
```

The container will start on port 9000 and emulate the AWS Lambda runtime.

### Test Local Function

```bash
# Test the main endpoint
curl -XPOST 'http://localhost:9000/2015-03-31/functions/function/invocations' \
  -H 'Content-Type: application/json' \
  -d '{"path":"/sample","httpMethod":"GET"}'

# Test health check
curl -XPOST 'http://localhost:9000/2015-03-31/functions/function/invocations' \
  -H 'Content-Type: application/json' \
  -d '{"path":"/health","httpMethod":"GET"}'

# Test unknown route (404)
curl -XPOST 'http://localhost:9000/2015-03-31/functions/function/invocations' \
  -H 'Content-Type: application/json' \
  -d '{"path":"/unknown","httpMethod":"GET"}'
```

---

## 📋 Summary

This **Lambda Node.js Sample Service** demonstrates modern serverless deployment patterns with:

✅ **Production-ready containerized Lambda functions**
✅ **Cross-account ECR access with automatic policies**
✅ **Comprehensive testing and diagnostic tools**
✅ **Developer-friendly local development workflow**
✅ **Enterprise-grade centralized container management**

Perfect for teams looking to implement scalable, secure, and maintainable serverless architectures with centralized container registries.
