import { injectable } from 'tsyringe';
import { EventEmitter } from 'events';

/**
 * Mock implementation of BaileysConnectionManager for testing
 * Simulates WhatsApp connections without real network calls
 */
@injectable()
export class MockBaileysConnectionManager extends EventEmitter {
  private connections = new Map<string, any>();
  private connectionStates = new Map<string, string>();

  async createConnection(userId: string, _authState?: any): Promise<any> {
    // Simulate connection creation
    const mockSocket = {
      user: { id: `${userId}@s.whatsapp.net` },
      ev: new EventEmitter(),
      end: () => {
        this.connectionStates.set(userId, 'closed');
        this.emit('connection.update', { userId, connection: 'close' });
      },
      logout: async () => {
        this.connectionStates.set(userId, 'logged_out');
        this.connections.delete(userId);
      },
      sendMessage: async (_jid: string, _message: any) => {
        // Mock message sending
        return { key: { id: 'mock-message-id' }, status: 'sent' };
      }
    };

    this.connections.set(userId, mockSocket);
    this.connectionStates.set(userId, 'qr_pending');

    // Simulate QR code generation after a short delay
    setTimeout(() => {
      if (this.connectionStates.get(userId) === 'qr_pending') {
        this.emit('connection.update', {
          userId,
          connection: 'connecting',
          qr: 'mock-qr-code-data-for-testing'
        });
      }
    }, 100);

    // Return connection info structure that matches what the real service expects
    return {
      socket: mockSocket,
      status: 'qr_pending',
      qrCode: 'mock-qr-code-data-for-testing',
      phoneNumber: null
    };
  }

  async closeConnection(userId: string): Promise<void> {
    const connection = this.connections.get(userId);
    if (connection) {
      try {
        connection.end();
      } catch (error) {
        // Ignore errors during mock connection cleanup
      }
      this.connections.delete(userId);
      this.connectionStates.delete(userId);
    }
  }

  getConnection(userId: string): any {
    const socket = this.connections.get(userId);
    if (!socket) return null;

    return {
      socket,
      status: this.connectionStates.get(userId) || 'closed',
      qrCode: 'mock-qr-code-data-for-testing',
      phoneNumber: null
    };
  }

  hasConnection(userId: string): boolean {
    return this.connections.has(userId);
  }

  getConnectionState(userId: string): string {
    return this.connectionStates.get(userId) || 'closed';
  }

  async reconnectConnection(userId: string, authState?: any): Promise<any> {
    // Close existing connection if any
    await this.closeConnection(userId);

    // Create new connection
    return this.createConnection(userId, authState);
  }

  getAllConnections(): Map<string, any> {
    return new Map(this.connections);
  }

  getActiveConnectionCount(): number {
    return Array.from(this.connectionStates.values())
      .filter(state => state === 'open').length;
  }

  async closeAllConnections(): Promise<void> {
    const userIds = Array.from(this.connections.keys());
    await Promise.all(userIds.map(userId => this.closeConnection(userId)));
  }

  // Mock methods for testing specific scenarios
  simulateConnection(userId: string): void {
    const connection = this.connections.get(userId);
    if (connection) {
      this.connectionStates.set(userId, 'open');
      this.emit('connection.update', {
        userId,
        connection: 'open',
        user: { id: `${userId}@s.whatsapp.net` }
      });
    }
  }

  simulateDisconnection(userId: string): void {
    const connection = this.connections.get(userId);
    if (connection) {
      this.connectionStates.set(userId, 'close');
      this.emit('connection.update', {
        userId,
        connection: 'close'
      });
    }
  }

  simulateQRCode(userId: string, qrData: string = 'mock-qr-data'): void {
    this.emit('connection.update', {
      userId,
      connection: 'connecting',
      qr: qrData
    });
  }

  simulateError(userId: string, error: Error): void {
    this.emit('connection.update', {
      userId,
      connection: 'close',
      lastDisconnect: {
        error,
        date: new Date()
      }
    });
  }

  // Additional methods that tests expect
  getStatistics(): any {
    return {
      totalConnections: this.connections.size,
      activeConnections: this.getActiveConnectionCount(),
      connectionStates: Object.fromEntries(this.connectionStates)
    };
  }

  // Cleanup method for tests
  async destroy(): Promise<void> {
    await this.closeAllConnections();
    this.removeAllListeners();
  }
}
