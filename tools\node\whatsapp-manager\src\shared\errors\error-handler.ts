import { Request, Response, NextFunction } from 'express';
import { injectable, inject } from 'tsyringe';
import { BaseError } from './base.error';
import { ILoggerService } from '../logging/interfaces';

export interface ErrorResponse {
  error: {
    message: string;
    code: string;
    statusCode: number;
    timestamp: string;
    path: string;
    requestId?: string;
    details?: Record<string, unknown>;
  };
}

@injectable()
export class ErrorHandler {
  constructor(@inject('ILoggerService') private readonly logger: ILoggerService) {}

  handleError(
    error: Error | BaseError,
    req: Request,
    res: Response,
    _next: NextFunction
  ): void {
    const requestId = req.headers['x-request-id'] as string;
    const logger = requestId ? this.logger.withRequestId(requestId) : this.logger;

    if (this.isOperationalError(error)) {
      this.handleOperationalError(error as BaseError, req, res, logger);
    } else {
      this.handleProgrammerError(error, req, res, logger);
    }
  }

  private isOperationalError(error: Error): boolean {
    if (error instanceof BaseError) {
      return error.isOperational;
    }
    return false;
  }

  private handleOperationalError(
    error: BaseError,
    req: Request,
    res: Response,
    logger: ILoggerService
  ): void {
    logger.warn('Operational error occurred', {
      error: error.name,
      message: error.message,
      statusCode: error.statusCode,
      path: req.path,
      method: req.method,
      context: error.context,
    });

    const errorResponse: ErrorResponse = {
      error: {
        message: error.message,
        code: error.name,
        statusCode: error.statusCode,
        timestamp: new Date().toISOString(),
        path: req.path,
        requestId: req.headers['x-request-id'] as string,
      },
    };

    res.status(error.statusCode).json(errorResponse);
  }

  private handleProgrammerError(
    error: Error,
    req: Request,
    res: Response,
    logger: ILoggerService
  ): void {
    logger.error('Programmer error occurred', {
      error: error.name,
      message: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
    });

    const errorResponse: ErrorResponse = {
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_SERVER_ERROR',
        statusCode: 500,
        timestamp: new Date().toISOString(),
        path: req.path,
        requestId: req.headers['x-request-id'] as string,
      },
    };

    res.status(500).json(errorResponse);
  }

  handleUncaughtException(error: Error): void {
    this.logger.error('Uncaught exception', {
      error: error.name,
      message: error.message,
      stack: error.stack,
    });

    // Graceful shutdown
    process.exit(1);
  }

  handleUnhandledRejection(reason: unknown): void {
    this.logger.error('Unhandled promise rejection', {
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined,
    });

    // Graceful shutdown
    process.exit(1);
  }
}
