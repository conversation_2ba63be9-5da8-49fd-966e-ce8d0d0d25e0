import { inject, injectable } from 'tsyringe';
import { ISessionRepository, SessionAlreadyExistsError } from '../../domain/repositories/ISessionRepository';
import { IWhatsAppService } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';
import { ISessionDomainService } from '../../domain/services/ISessionDomainService';
import { SessionEntity, SessionStatus } from '../../domain/entities/SessionEntity';

/**
 * Request interface for starting a session
 */
export interface StartSessionRequest {
  userId: string;
  deviceName?: string;
  browserName?: string;
  forceNew?: boolean; // Force create new session even if one exists
}

/**
 * Response interface for starting a session
 */
export interface StartSessionResponse {
  sessionId: string;
  qrCode?: string;
  status: SessionStatus;
  expiresAt: Date;
  isExistingSession: boolean; // Indicates if reconnecting to existing session
  phoneNumber?: string;
}

/**
 * Use case for starting WhatsApp sessions
 */
@injectable()
export class StartSessionUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('IWhatsAppService') private whatsappService: IWhatsAppService,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('ISessionDomainService') private sessionDomainService: ISessionDomainService
  ) {}

  /**
   * Execute the start session use case
   */
  async execute(request: StartSessionRequest): Promise<StartSessionResponse> {
    const { userId, forceNew = false } = request;
    
    this.logger.info('Starting session use case', { 
      userId, 
      forceNew,
      deviceName: request.deviceName,
      browserName: request.browserName
    });

    try {
      // Validate session creation
      await this.sessionDomainService.validateSessionCreation(userId);

      // Check for existing session first
      let existingSession: SessionEntity | null = null;
      try {
        existingSession = await this.sessionRepository.findByUserId(userId);
      } catch (error) {
        // Session doesn't exist yet, which is fine for new session creation
        this.logger.debug('No existing session found, will create new one', { userId });
      }

      if (existingSession && !forceNew) {
        return await this.handleExistingSession(existingSession, request);
      }

      // Clean up existing session if force new is requested
      if (existingSession && forceNew) {
        await this.cleanupExistingSession(existingSession);
      }

      // Create new session
      return await this.createNewSession(request);

    } catch (error) {
      this.logger.error('Failed to start session', {
        userId,
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      throw error;
    }
  }

  /**
   * Handle existing session logic
   */
  private async handleExistingSession(
    existingSession: SessionEntity, 
    request: StartSessionRequest
  ): Promise<StartSessionResponse> {
    const { userId } = request;

    // If session is already connected, return it
    if (existingSession.status === 'connected' && existingSession.isActive()) {
      this.logger.info('Returning existing connected session', { userId });
      
      return {
        sessionId: existingSession.sessionId,
        status: existingSession.status,
        expiresAt: new Date(existingSession.createdAt.getTime() + (72 * 60 * 60 * 1000)),
        isExistingSession: true,
        phoneNumber: existingSession.phoneNumber || undefined
      };
    }

    // If session is QR pending and not expired, regenerate QR
    if (existingSession.status === 'qr_pending' && !existingSession.isExpired()) {
      this.logger.info('Regenerating QR for existing pending session', { userId });
      
      try {
        const qrResult = await this.whatsappService.regenerateQR(userId);
        return {
          sessionId: existingSession.sessionId,
          qrCode: qrResult.qrCode,
          status: 'qr_pending',
          expiresAt: new Date(existingSession.createdAt.getTime() + (72 * 60 * 60 * 1000)),
          isExistingSession: true
        };
      } catch (error) {
        this.logger.warn('Failed to regenerate QR, creating new session', {
          userId,
          error: (error as Error).message
        });
        await this.cleanupExistingSession(existingSession);
        return await this.createNewSession(request);
      }
    }

    // If session can be reconnected, attempt reconnection
    if (existingSession.canReconnect()) {
      this.logger.info('Attempting to reconnect existing session', { userId });
      
      try {
        const reconnectResult = await this.whatsappService.reconnectSession(userId);
        return {
          sessionId: existingSession.sessionId,
          qrCode: reconnectResult.qrCode,
          status: reconnectResult.status,
          expiresAt: new Date(existingSession.createdAt.getTime() + (72 * 60 * 60 * 1000)),
          isExistingSession: true,
          phoneNumber: reconnectResult.phoneNumber
        };
      } catch (error) {
        this.logger.warn('Failed to reconnect existing session, creating new one', {
          userId,
          error: (error as Error).message
        });
        await this.cleanupExistingSession(existingSession);
        return await this.createNewSession(request);
      }
    }

    // Clean up expired or disconnected session and create new one
    this.logger.info('Cleaning up expired/disconnected session and creating new one', { 
      userId,
      existingStatus: existingSession.status,
      isExpired: existingSession.isExpired()
    });
    
    await this.cleanupExistingSession(existingSession);
    return await this.createNewSession(request);
  }

  /**
   * Create a new session
   */
  private async createNewSession(request: StartSessionRequest): Promise<StartSessionResponse> {
    const { userId, deviceName, browserName } = request;

    // Create new session entity
    const newSession = SessionEntity.create(userId, deviceName, browserName);
    
    try {
      // Use createSession to ensure atomicity (fails if exists)
      await this.sessionRepository.createSession(newSession);
      
      this.logger.info('New session entity created', {
        userId,
        sessionId: newSession.sessionId
      });

    } catch (error) {
      if (error instanceof SessionAlreadyExistsError) {
        // Handle race condition - another request created session
        this.logger.warn('Session creation race condition detected', { userId });
        const concurrent = await this.sessionRepository.findByUserId(userId);
        if (concurrent) {
          return this.execute({ ...request, forceNew: false }); // Retry with existing
        }
      }
      throw error;
    }

    // Initialize WhatsApp connection
    try {
      const startResult = await this.whatsappService.startSession(userId);
      
      // Update session status based on start result
      if (startResult.qrCode) {
        await this.sessionRepository.updateSessionStatus(userId, 'qr_pending');
      }

      this.logger.info('WhatsApp session started successfully', {
        userId,
        sessionId: newSession.sessionId,
        status: startResult.status
      });

      return {
        sessionId: newSession.sessionId,
        qrCode: startResult.qrCode,
        status: startResult.status,
        expiresAt: new Date(newSession.createdAt.getTime() + (72 * 60 * 60 * 1000)),
        isExistingSession: false,
        phoneNumber: startResult.phoneNumber
      };

    } catch (error) {
      // Cleanup session entity if WhatsApp service fails
      this.logger.error('WhatsApp service failed, cleaning up session entity', {
        userId,
        sessionId: newSession.sessionId,
        error: (error as Error).message
      });

      try {
        await this.sessionRepository.delete(userId);
      } catch (cleanupError) {
        this.logger.warn('Failed to cleanup session after WhatsApp service failure', {
          userId,
          cleanupError: (cleanupError as Error).message
        });
      }

      throw error;
    }
  }

  /**
   * Clean up existing session
   */
  private async cleanupExistingSession(session: SessionEntity): Promise<void> {
    try {
      // Terminate WhatsApp connection if active
      if (await this.whatsappService.isSessionActive(session.userId)) {
        await this.whatsappService.terminateSession(session.userId);
      }

      // Delete session from repository
      await this.sessionRepository.delete(session.userId);

      this.logger.info('Existing session cleaned up', {
        userId: session.userId,
        sessionId: session.sessionId
      });

    } catch (error) {
      this.logger.warn('Failed to cleanup existing session', {
        userId: session.userId,
        error: (error as Error).message
      });
      // Don't throw here as we want to continue with new session creation
    }
  }

  // Removed unused validation method
}
