import { inject, injectable } from 'tsyringe';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { HealthChecker, ComponentHealth, HealthStatus } from '../../infrastructure/whatsapp/HealthChecker';

/**
 * Health check request
 */
export interface HealthCheckRequest {
  includeDetails?: boolean;
  components?: string[]; // Specific components to check
  timeout?: number; // Timeout in milliseconds
}

/**
 * Health check response
 */
export interface HealthCheckResponse {
  status: HealthStatus;
  timestamp: Date;
  uptime: number;
  version: string;
  environment: string;
  components: ComponentHealth[];
  metrics: {
    activeSessions: number;
    totalSessions: number;
    memoryUsage: {
      heapUsedMB: number;
      heapTotalMB: number;
      rssMB: number;
      heapUsagePercent: number;
    };
    systemLoad?: {
      cpu: number;
      memory: number;
    };
  };
  details?: any;
}

/**
 * Simple health status response
 */
export interface SimpleHealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message: string;
  timestamp: Date;
}

/**
 * Component-specific health check response
 */
export interface ComponentHealthResponse {
  component: string;
  status: HealthStatus;
  responseTime: number;
  details: any;
  error?: string;
  lastCheck: Date;
}

/**
 * Use case for health checking and system monitoring
 * Provides comprehensive health status of the WhatsApp Manager system
 */
@injectable()
export class HealthCheckUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('HealthChecker') private healthChecker: HealthChecker,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {}

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(request: HealthCheckRequest = {}): Promise<HealthCheckResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Performing health check', {
        includeDetails: request.includeDetails,
        components: request.components,
        timeout: request.timeout
      });

      // Get system health from health checker
      const systemHealth = await this.healthChecker.getSystemHealth();

      // Filter components if specified
      let components = systemHealth.components;
      if (request.components && request.components.length > 0) {
        components = components.filter(c => request.components!.includes(c.name));
      }

      // Enhance memory usage information
      const memoryUsage = this.enhanceMemoryUsage(systemHealth.metrics.memoryUsage);

      // Get system load if available
      const systemLoad = await this.getSystemLoad();

      const response: HealthCheckResponse = {
        status: systemHealth.status,
        timestamp: systemHealth.timestamp,
        uptime: systemHealth.uptime,
        version: systemHealth.version,
        environment: this.config.getOptional('NODE_ENV', 'development'),
        components,
        metrics: {
          activeSessions: systemHealth.metrics.activeSessions,
          totalSessions: systemHealth.metrics.totalSessions,
          memoryUsage,
          systemLoad
        },
        details: request.includeDetails ? {
          responseTime: Date.now() - startTime,
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          pid: process.pid,
          startTime: new Date(Date.now() - systemHealth.uptime)
        } : undefined
      };

      this.logger.debug('Health check completed', {
        status: response.status,
        componentCount: components.length,
        responseTime: Date.now() - startTime
      });

      return response;

    } catch (error) {
      this.logger.error('Health check failed', {
        error: (error as Error).message,
        responseTime: Date.now() - startTime
      });

      // Return degraded status with minimal information
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        uptime: Date.now() - startTime,
        version: this.config.getOptional('npm_package_version', '1.0.0'),
        environment: this.config.getOptional('NODE_ENV', 'development'),
        components: [],
        metrics: {
          activeSessions: 0,
          totalSessions: 0,
          memoryUsage: this.enhanceMemoryUsage(process.memoryUsage())
        }
      };
    }
  }

  /**
   * Get simple health status (for load balancers)
   */
  async getSimpleHealth(): Promise<SimpleHealthResponse> {
    try {
      // Get fresh system health data
      const systemHealth = await this.healthChecker.getSystemHealth();
      const status = systemHealth.status;
      const message = this.getStatusMessage(status);

      return {
        status,
        message,
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error('Simple health check failed', {
        error: (error as Error).message
      });

      return {
        status: 'unhealthy',
        message: 'Health check failed',
        timestamp: new Date()
      };
    }
  }

  /**
   * Check specific component health
   */
  async checkComponent(componentName: string): Promise<ComponentHealthResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Checking component health', { componentName });

      const componentHealth = await this.healthChecker.checkComponentHealth(componentName);

      return {
        component: componentName,
        status: componentHealth.status,
        responseTime: Date.now() - startTime,
        details: componentHealth.details,
        error: componentHealth.error,
        lastCheck: componentHealth.lastCheck
      };

    } catch (error) {
      this.logger.error('Component health check failed', {
        componentName,
        error: (error as Error).message
      });

      return {
        component: componentName,
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        details: {},
        error: (error as Error).message,
        lastCheck: new Date()
      };
    }
  }

  /**
   * Get readiness status (for Kubernetes readiness probes)
   */
  async getReadiness(): Promise<{
    ready: boolean;
    message: string;
    checks: { [component: string]: boolean };
  }> {
    try {
      const systemHealth = await this.healthChecker.getSystemHealth();
      
      // Check critical components for readiness
      const criticalComponents = ['database', 'sessions'];
      const checks: { [component: string]: boolean } = {};
      let allReady = true;

      for (const component of systemHealth.components) {
        if (criticalComponents.includes(component.name)) {
          const isReady = component.status === 'healthy';
          checks[component.name] = isReady;
          if (!isReady) {
            allReady = false;
          }
        }
      }

      return {
        ready: allReady,
        message: allReady ? 'Service is ready' : 'Service is not ready',
        checks
      };

    } catch (error) {
      this.logger.error('Readiness check failed', {
        error: (error as Error).message
      });

      return {
        ready: false,
        message: 'Readiness check failed',
        checks: {}
      };
    }
  }

  /**
   * Get liveness status (for Kubernetes liveness probes)
   */
  async getLiveness(): Promise<{
    alive: boolean;
    message: string;
    uptime: number;
  }> {
    try {
      const systemHealth = await this.healthChecker.getSystemHealth();
      
      // Basic liveness check - if we can respond, we're alive
      const alive = systemHealth.status !== 'unhealthy';

      return {
        alive,
        message: alive ? 'Service is alive' : 'Service is not responding properly',
        uptime: systemHealth.uptime
      };

    } catch (error) {
      this.logger.error('Liveness check failed', {
        error: (error as Error).message
      });

      return {
        alive: false,
        message: 'Liveness check failed',
        uptime: 0
      };
    }
  }

  /**
   * Get system metrics
   */
  async getMetrics(): Promise<{
    sessions: {
      active: number;
      total: number;
      byStatus: { [status: string]: number };
    };
    memory: {
      heapUsedMB: number;
      heapTotalMB: number;
      rssMB: number;
      heapUsagePercent: number;
    };
    uptime: number;
    version: string;
  }> {
    try {
      const systemHealth = await this.healthChecker.getSystemHealth();
      
      // Get session statistics
      const sessions = await this.sessionRepository.findAllSessions();
      const sessionsByStatus: { [status: string]: number } = {};
      
      for (const session of sessions) {
        const status = session.status || 'unknown';
        sessionsByStatus[status] = (sessionsByStatus[status] || 0) + 1;
      }

      return {
        sessions: {
          active: systemHealth.metrics.activeSessions,
          total: systemHealth.metrics.totalSessions,
          byStatus: sessionsByStatus
        },
        memory: this.enhanceMemoryUsage(systemHealth.metrics.memoryUsage),
        uptime: systemHealth.uptime,
        version: systemHealth.version
      };

    } catch (error) {
      this.logger.error('Failed to get metrics', {
        error: (error as Error).message
      });

      const memoryUsage = this.enhanceMemoryUsage(process.memoryUsage());

      return {
        sessions: {
          active: 0,
          total: 0,
          byStatus: {}
        },
        memory: memoryUsage,
        uptime: 0,
        version: this.config.getOptional('npm_package_version', '1.0.0')
      };
    }
  }

  /**
   * Enhance memory usage with calculated fields
   */
  private enhanceMemoryUsage(memoryUsage: NodeJS.MemoryUsage): {
    heapUsedMB: number;
    heapTotalMB: number;
    rssMB: number;
    heapUsagePercent: number;
  } {
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    const rssMB = Math.round(memoryUsage.rss / 1024 / 1024);
    const heapUsagePercent = Math.round((heapUsedMB / heapTotalMB) * 100);

    return {
      heapUsedMB,
      heapTotalMB,
      rssMB,
      heapUsagePercent
    };
  }

  /**
   * Get system load (CPU and memory)
   */
  private async getSystemLoad(): Promise<{ cpu: number; memory: number } | undefined> {
    try {
      // This would typically use system monitoring libraries
      // For now, return undefined as it's optional
      return undefined;
    } catch (error) {
      this.logger.debug('Failed to get system load', {
        error: (error as Error).message
      });
      return undefined;
    }
  }

  /**
   * Get status message for simple health response
   */
  private getStatusMessage(status: HealthStatus): string {
    switch (status) {
      case 'healthy':
        return 'All systems operational';
      case 'degraded':
        return 'Some systems experiencing issues';
      case 'unhealthy':
        return 'Critical systems are down';
      default:
        return 'Unknown system status';
    }
  }
}
