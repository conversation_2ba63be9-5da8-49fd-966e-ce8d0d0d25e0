{
  "name": "🔄 QR Code Management",
  "description": "QR code refresh and status management for WhatsApp sessions",
  "item": [
    {
      "name": "Refresh QR Code",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('QR code refreshed successfully', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData.success).to.be.true;",
              "    pm.expect(jsonData.data).to.have.property('qrCode');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"force\": true\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/refresh",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "refresh"]
        },
        "description": "Refresh QR code for a pending WhatsApp session. Rate limited to 5 requests per 5 minutes."
      },
      "response": []
    },
    {
      "name": "Get QR Code Status",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/status",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "status"]
        },
        "description": "Get QR code status and validity for a WhatsApp session."
      },
      "response": []
    }
  ]
},
{
  "name": "💬 Message Operations",
  "description": "Send and retrieve WhatsApp messages through active sessions",
  "item": [
    {
      "name": "Send Text Message",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('Message sent successfully', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData.success).to.be.true;",
              "    pm.expect(jsonData.data).to.have.property('messageId');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"Hello from WhatsApp Manager API! This is a test message sent via Postman.\",\n  \"type\": \"text\",\n  \"metadata\": {\n    \"source\": \"postman-collection\",\n    \"timestamp\": \"{{$timestamp}}\"\n  }\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]
        },
        "description": "Send a text message through an active WhatsApp session. Rate limited to 60 messages per minute."
      },
      "response": []
    },
    {
      "name": "Send Media Message",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"Check out this image!\",\n  \"type\": \"image\",\n  \"mediaUrl\": \"https://picsum.photos/800/600\",\n  \"metadata\": {\n    \"source\": \"postman-collection\",\n    \"mediaType\": \"image/jpeg\"\n  }\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]
        },
        "description": "Send a media message (image, video, document) through an active WhatsApp session."
      },
      "response": []
    },
    {
      "name": "Send Reply Message",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"This is a reply to your previous message.\",\n  \"type\": \"text\",\n  \"quotedMessageId\": \"MESSAGE_ID_TO_REPLY_TO\",\n  \"metadata\": {\n    \"source\": \"postman-collection\",\n    \"isReply\": true\n  }\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]
        },
        "description": "Send a reply message to a specific message in WhatsApp."
      },
      "response": []
    },
    {
      "name": "Get Messages",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test('Status code is 200', function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test('Messages retrieved successfully', function () {",
              "    const jsonData = pm.response.json();",
              "    pm.expect(jsonData.success).to.be.true;",
              "    pm.expect(jsonData.data).to.have.property('messages');",
              "    pm.expect(jsonData.data.messages).to.be.an('array');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages?limit=50&offset=0&type=text&hasMedia=false",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages"],
          "query": [
            {
              "key": "limit",
              "value": "50",
              "description": "Number of messages to return"
            },
            {
              "key": "offset",
              "value": "0",
              "description": "Number of messages to skip"
            },
            {
              "key": "type",
              "value": "text",
              "description": "Filter by message type"
            },
            {
              "key": "hasMedia",
              "value": "false",
              "description": "Filter by media presence"
            }
          ]
        },
        "description": "Get messages for a WhatsApp session with filtering options. Rate limited to 100 requests per minute."
      },
      "response": []
    },
    {
      "name": "Get Messages with Media Filter",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages?limit=20&hasMedia=true&type=image",
          "host": ["{{baseUrl}}"],
          "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages"],
          "query": [
            {
              "key": "limit",
              "value": "20",
              "description": "Number of messages to return"
            },
            {
              "key": "hasMedia",
              "value": "true",
              "description": "Only messages with media"
            },
            {
              "key": "type",
              "value": "image",
              "description": "Only image messages"
            }
          ]
        },
        "description": "Get messages filtered by media type and presence."
      },
      "response": []
    }
  ]
}
