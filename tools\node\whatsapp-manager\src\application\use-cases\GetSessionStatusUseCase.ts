import { inject, injectable } from 'tsyringe';
import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { IWhatsAppService } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';
import { SessionStatus } from '../../domain/entities/SessionEntity';

/**
 * Request interface for getting session status
 */
export interface GetSessionStatusRequest {
  userId: string;
}

/**
 * Response interface for session status
 */
export interface GetSessionStatusResponse {
  sessionId: string;
  userId: string;
  status: SessionStatus;
  phoneNumber?: string;
  connectedAt?: Date;
  disconnectedAt?: Date;
  lastActivity?: Date;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  canReconnect: boolean;
  deviceName?: string;
  browserName?: string;
}

/**
 * Use case for getting session status
 */
@injectable()
export class GetSessionStatusUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('IWhatsAppService') private whatsappService: IWhatsAppService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Execute the get session status use case
   */
  async execute(request: GetSessionStatusRequest): Promise<GetSessionStatusResponse> {
    const { userId } = request;
    
    this.logger.debug('Getting session status', { userId });

    try {
      // Find session in repository
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        // Return default disconnected status for non-existent sessions
        this.logger.debug('Session not found, returning default status', { userId });
        return {
          sessionId: `${userId}-default`,
          userId,
          status: 'disconnected' as SessionStatus,
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: false,
          canReconnect: false
        };
      }

      // Get real-time status from WhatsApp service if available
      let currentStatus = session.status;
      let lastActivity: Date | undefined;

      try {
        const serviceStatus = await this.whatsappService.getSessionStatus(userId);
        const isServiceActive = await this.whatsappService.isSessionActive(userId);
        
        // Use service status if it's more current
        if (isServiceActive && serviceStatus === 'connected') {
          currentStatus = serviceStatus;
          lastActivity = new Date(); // Service is active, so last activity is now
        } else if (serviceStatus !== session.status) {
          // Update repository with current service status
          await this.sessionRepository.updateSessionStatus(userId, serviceStatus);
          currentStatus = serviceStatus;
        }
      } catch (error) {
        this.logger.warn('Failed to get real-time status from WhatsApp service', {
          userId,
          error: (error as Error).message
        });
        // Continue with repository status
      }

      // Calculate expiration date
      const expiresAt = session.ttl ? new Date(session.ttl * 1000) : undefined;

      const response: GetSessionStatusResponse = {
        sessionId: session.sessionId,
        userId: session.userId,
        status: currentStatus,
        phoneNumber: session.phoneNumber || undefined,
        connectedAt: session.connectedAt,
        disconnectedAt: session.disconnectedAt,
        lastActivity: lastActivity || session.updatedAt,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        expiresAt,
        isActive: session.isActive(),
        canReconnect: session.canReconnect(),
        deviceName: session.deviceName,
        browserName: session.browserName
      };

      this.logger.debug('Session status retrieved', {
        userId,
        status: currentStatus,
        isActive: response.isActive,
        canReconnect: response.canReconnect
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to get session status', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Get session status with health check
   */
  async executeWithHealthCheck(request: GetSessionStatusRequest): Promise<GetSessionStatusResponse & { healthScore?: number }> {
    const basicStatus = await this.execute(request);
    
    try {
      // Perform basic health check
      const healthScore = this.calculateHealthScore(basicStatus);
      
      return {
        ...basicStatus,
        healthScore
      };
    } catch (error) {
      this.logger.warn('Failed to calculate health score', {
        userId: request.userId,
        error: (error as Error).message
      });
      return basicStatus;
    }
  }

  /**
   * Calculate basic health score for session
   */
  private calculateHealthScore(status: GetSessionStatusResponse): number {
    let score = 0;

    // Base score for existing session
    score += 20;

    // Status-based scoring
    switch (status.status) {
      case 'connected':
        score += 50;
        break;
      case 'qr_pending':
        score += 30;
        break;
      case 'connecting':
        score += 25;
        break;
      case 'disconnected':
        score += 10;
        break;
      case 'error':
        score += 5;
        break;
      default:
        score += 15;
    }

    // Activity-based scoring
    if (status.lastActivity) {
      const hoursSinceActivity = (Date.now() - status.lastActivity.getTime()) / (1000 * 60 * 60);
      if (hoursSinceActivity < 1) {
        score += 20;
      } else if (hoursSinceActivity < 24) {
        score += 10;
      } else if (hoursSinceActivity < 72) {
        score += 5;
      }
    }

    // Connection stability
    if (status.connectedAt && status.phoneNumber) {
      score += 10;
    }

    return Math.min(100, Math.max(0, score));
  }

  // Removed unused validation method
}
