import request from 'supertest';
import { createApp } from '../../src/app';
import { TestDIContainer } from '../../src/di/test-container';

describe('Health Endpoint E2E', () => {
  let server: any;

  beforeAll(async () => {
    // Initialize test container before creating app
    TestDIContainer.initialize();
    server = createApp();
  });

  afterAll(async () => {
    TestDIContainer.destroy();
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(server)
        .get('/health')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('environment', 'test');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('checks');

      expect(['ok', 'degraded', 'down']).toContain(response.body.status);
    });

    it('should include request ID in response headers', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      expect(response.headers).toHaveProperty('x-request-id');
      expect(response.headers['x-request-id']).toMatch(/^[0-9a-f-]{36}$/);
    });

    it('should accept custom request ID', async () => {
      const customRequestId = 'test-request-123';
      
      const response = await request(server)
        .get('/health')
        .set('x-request-id', customRequestId)
        .expect(200);

      expect(response.headers['x-request-id']).toBe(customRequestId);
    });

    it('should include database check', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      expect(response.body.checks).toHaveProperty('database');
      expect(response.body.checks.database).toHaveProperty('status');
      expect(response.body.checks.database).toHaveProperty('message');
      expect(response.body.checks.database).toHaveProperty('responseTime');
      expect(['ok', 'warn', 'error']).toContain(response.body.checks.database.status);
    });

    it('should include memory check', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      expect(response.body.checks).toHaveProperty('memory');
      expect(response.body.checks.memory).toHaveProperty('status');
      expect(response.body.checks.memory).toHaveProperty('message');
      expect(response.body.checks.memory).toHaveProperty('details');
      expect(['ok', 'warn', 'error']).toContain(response.body.checks.memory.status);
    });
  });

  describe('GET /api/health', () => {
    it('should also work on versioned API endpoint', async () => {
      const response = await request(server)
        .get('/api/health')
        .expect('Content-Type', /json/)
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('environment', 'test');
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(server)
        .get('/non-existent-route')
        .expect('Content-Type', /json/)
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message', 'Route not found');
      expect(response.body.error).toHaveProperty('code', 'NOT_FOUND');
      expect(response.body.error).toHaveProperty('statusCode', 404);
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(server)
        .get('/health')
        .set('Origin', 'http://localhost:3001')
        .expect(200);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });
  });

  describe('Security headers', () => {
    it('should include security headers', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      // Helmet security headers
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options');
    });
  });
});
