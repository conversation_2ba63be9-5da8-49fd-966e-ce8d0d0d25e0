"""
Domain Services for Document Ingestion Pipeline

This module contains domain services that encapsulate business logic
that doesn't naturally fit within a single entity.
"""

from typing import Any, Dict, List

from .entities import EmbeddingVector, ProductData
from .interfaces import IEmbeddingService, ILogger


class SearchableTextGenerator:
    """Domain service for generating searchable text from product data"""

    # Common field names that should be included in search text
    DEFAULT_SEARCH_FIELDS = [
        "name",
        "title",
        "product_name",
        "description",
        "desc",
        "product_description",
        "category",
        "type",
        "product_type",
        "brand",
        "manufacturer",
        "tags",
        "keywords",
        "sku",
        "model",
        "product_id",
    ]

    def __init__(self, search_fields: list[str] = None):
        self.search_fields = search_fields or self.DEFAULT_SEARCH_FIELDS

    def generate(self, product_data: ProductData) -> str:
        """Generate searchable text from product data"""
        return product_data.get_searchable_text(self.search_fields)


class EmbeddingGenerationService:
    """Domain service for coordinating embedding generation"""

    def __init__(
        self,
        embedding_service: IEmbeddingService,
        text_generator: SearchableTextGenerator,
        logger: ILogger,
    ):
        self._embedding_service = embedding_service
        self._text_generator = text_generator
        self._logger = logger

    async def generate_product_embeddings(
        self, products: list[ProductData]
    ) -> list[dict[str, Any]]:
        """
        Generate embeddings for a list of products

        Args:
            products: List of product data

        Returns:
            List of dictionaries containing product data and embeddings
        """
        if not products:
            return []

        self._logger.info(f"Generating embeddings for {len(products)} products")

        # Generate searchable text for all products
        searchable_texts = []
        for product in products:
            text = self._text_generator.generate(product)
            searchable_texts.append(text)

        # Generate embeddings in batch
        embeddings = await self._embedding_service.generate_embeddings(searchable_texts)

        # Combine product data with embeddings
        results = []
        for i, (product, embedding) in enumerate(zip(products, embeddings)):
            results.append(
                {
                    "row_index": product.row_index,
                    "product_data": product.data,
                    "searchable_text": searchable_texts[i],
                    "embedding_vector": embedding.vector,
                }
            )

        self._logger.info(f"Generated {len(results)} embeddings successfully")
        return results


class DocumentValidationService:
    """Domain service for document validation business rules"""

    def __init__(self, logger: ILogger):
        self._logger = logger

    def validate_file_size(self, content_size: int, max_size: int) -> bool:
        """Validate file size against limits"""
        if content_size > max_size:
            self._logger.warning(f"File size {content_size} exceeds limit {max_size}")
            return False
        return True

    def validate_row_count(self, row_count: int, max_rows: int) -> bool:
        """Validate row count against limits"""
        if row_count > max_rows:
            self._logger.warning(f"Row count {row_count} exceeds limit {max_rows}")
            return False
        return True

    def validate_column_names(self, column_names: list[str]) -> bool:
        """Validate column names"""
        if not column_names:
            self._logger.warning("No columns found in CSV")
            return False

        # Check for invalid column names
        invalid_columns = []
        for col in column_names:
            if not isinstance(col, str) or not col.strip():
                invalid_columns.append(col)
            elif len(col.strip()) > 255:  # Column name too long
                invalid_columns.append(col)

        if invalid_columns:
            self._logger.warning(f"Invalid column names found: {invalid_columns}")
            return False

        return True

    def calculate_data_quality_score(self, products: list[ProductData]) -> float:
        """Calculate data quality score for products"""
        if not products:
            return 0.0

        total_fields = 0
        non_empty_fields = 0

        for product in products:
            for value in product.data.values():
                total_fields += 1
                if value is not None and str(value).strip():
                    non_empty_fields += 1

        return non_empty_fields / total_fields if total_fields > 0 else 0.0


class MultiTenantSecurityService:
    """Domain service for multi-tenant security validation"""

    def __init__(self, logger: ILogger):
        self._logger = logger

    def validate_tenant_access(self, user_id: str, s3_key: str) -> bool:
        """Validate that the user has access to the S3 key"""
        # Extract user ID from S3 key path
        if not s3_key.startswith(f"{user_id}/"):
            self._logger.error(f"Tenant access violation: user {user_id} accessing {s3_key}")
            return False
        return True

    def sanitize_user_id(self, user_id: str) -> str:
        """Sanitize user ID for security"""
        # Remove any potentially dangerous characters
        sanitized = "".join(c for c in user_id if c.isalnum() or c in "-_")
        if sanitized != user_id:
            self._logger.warning(f"User ID sanitized from {user_id} to {sanitized}")
        return sanitized
