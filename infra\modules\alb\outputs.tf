output "alb_id" {
  description = "The ID of the load balancer"
  value       = aws_lb.this.id
}

output "alb_arn" {
  description = "The ARN of the load balancer"
  value       = aws_lb.this.arn
}

output "alb_dns_name" {
  description = "The DNS name of the load balancer"
  value       = aws_lb.this.dns_name
}

output "alb_zone_id" {
  description = "The canonical hosted zone ID of the load balancer"
  value       = aws_lb.this.zone_id
}

output "http_listener_arn" {
  description = "The ARN of the HTTP listener"
  value       = var.create_http_listener ? aws_lb_listener.http[0].arn : null
}

output "https_listener_arn" {
  description = "The ARN of the HTTPS listener"
  value       = var.create_https_listener ? aws_lb_listener.https[0].arn : null
}

output "default_target_group_arn" {
  description = "The ARN of the default target group"
  value       = var.default_target_group_arn != null ? var.default_target_group_arn : (var.create_default_target_group ? aws_lb_target_group.default[0].arn : null)
}

output "security_group_id" {
  description = "The ID of the security group created for the load balancer"
  value       = var.security_group_ids == null ? aws_security_group.this[0].id : null
}

output "alb_name" {
  description = "The name of the load balancer"
  value       = aws_lb.this.name
}
