import express, { Application } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { DIContainer } from './di/container';
import { IConfigService } from './shared/config/interfaces';
import { ILoggerService } from './shared/logging/interfaces';
import { ErrorHandler } from './shared/errors/error-handler';
import { requestIdMiddleware } from './api/middleware/request-id.middleware';
import { createLoggingMiddleware } from './api/middleware/logging.middleware';
import { createApiRoutes } from './api/routes';
import { createHealthRoutes } from './api/routes/health.routes';
import { qrAuthRoutes } from './api/routes/qrAuth.routes';

/**
 * Express application factory
 */
export class App {
  private readonly app: Application;
  private readonly configService: IConfigService;
  private readonly logger: ILoggerService;
  private readonly errorHandler: ErrorHandler;

  constructor() {
    this.app = express();
    this.configService = DIContainer.resolve<IConfigService>('IConfigService');
    this.logger = DIContainer.resolve<ILoggerService>('ILoggerService');
    this.errorHandler = DIContainer.resolve<ErrorHandler>('ErrorHandler');

    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    const serverConfig = this.configService.getServer();
    this.app.use(cors({
      origin: serverConfig.corsOrigins === '*' ? true : serverConfig.corsOrigins.split(','),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'x-request-id'],
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const rateLimitConfig = this.configService.getRateLimit();
    this.app.use(rateLimit({
      windowMs: rateLimitConfig.windowMs,
      max: rateLimitConfig.maxRequests,
      message: {
        error: {
          message: 'Too many requests from this IP, please try again later',
          code: 'RATE_LIMIT_EXCEEDED',
          statusCode: 429,
        },
      },
      standardHeaders: true,
      legacyHeaders: false,
    }));

    // Request parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request ID middleware (must be before logging)
    this.app.use(requestIdMiddleware);

    // Logging middleware
    this.app.use(createLoggingMiddleware(this.logger));
  }

  private setupRoutes(): void {
    // API routes
    this.app.use('/api', createApiRoutes());

    // QR Authentication routes (for scanned QR codes) - mounted directly for user-friendly URLs
    this.app.use('/auth', qrAuthRoutes);

    // Health check at root level for load balancers
    this.app.use('/health', createHealthRoutes());

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: {
          message: 'Route not found',
          code: 'NOT_FOUND',
          statusCode: 404,
          path: req.originalUrl,
        },
      });
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      this.errorHandler.handleError(error, req, res, next);
    });

    // Uncaught exception handler
    process.on('uncaughtException', (error: Error) => {
      this.errorHandler.handleUncaughtException(error);
    });

    // Unhandled promise rejection handler
    process.on('unhandledRejection', (reason: unknown) => {
      this.errorHandler.handleUnhandledRejection(reason);
    });
  }

  getApp(): Application {
    return this.app;
  }

  async start(): Promise<void> {
    const serverConfig = this.configService.getServer();
    
    return new Promise((resolve, reject) => {
      const server = this.app.listen(serverConfig.port, serverConfig.host, () => {
        this.logger.info('Server started successfully', {
          port: serverConfig.port,
          host: serverConfig.host,
          environment: this.configService.getEnvironment(),
          nodeVersion: process.version,
          pid: process.pid,
        });
        resolve();
      });

      server.on('error', (error: Error) => {
        this.logger.error('Server failed to start', { error });
        reject(error);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => {
        this.logger.info('SIGTERM received, shutting down gracefully');
        server.close(() => {
          this.logger.info('Server closed');
          process.exit(0);
        });
      });

      process.on('SIGINT', () => {
        this.logger.info('SIGINT received, shutting down gracefully');
        server.close(() => {
          this.logger.info('Server closed');
          process.exit(0);
        });
      });
    });
  }
}

/**
 * Factory function to create Express application
 * Used by tests and other modules
 */
export function createApp(): Application {
  const app = new App();
  return app.getApp();
}
