import { EventEmitter } from 'events';
import { inject, injectable } from 'tsyringe';

import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { SessionEvent, SessionEventType } from '../../domain/services/IWhatsAppService';

/**
 * Event listener interface
 */
export interface EventListener {
  id: string;
  event: string;
  handler: (...args: any[]) => void | Promise<void>;
  once?: boolean;
  priority?: number;
}

/**
 * Event statistics
 */
export interface EventStatistics {
  totalEvents: number;
  eventsByType: { [type: string]: number };
  listenerCount: number;
  errorCount: number;
  lastEvent?: {
    type: string;
    timestamp: Date;
    userId?: string;
  };
}

/**
 * Enhanced event emitter for Baileys WhatsApp events
 * Provides structured event handling with logging, filtering, and monitoring
 */
@injectable()
export class BaileysEventEmitter extends EventEmitter {
  private readonly eventListeners = new Map<string, EventListener>();
  private readonly eventStatistics: EventStatistics = {
    totalEvents: 0,
    eventsByType: {},
    listenerCount: 0,
    errorCount: 0
  };
  private readonly maxListeners: number;
  private readonly enableEventLogging: boolean;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    super();

    this.maxListeners = this.config.getOptional('MAX_EVENT_LISTENERS', 100);
    this.enableEventLogging = this.config.getOptional('ENABLE_EVENT_LOGGING', true);

    // Set max listeners to prevent memory leaks
    this.setMaxListeners(this.maxListeners);

    // Set up error handling
    this.on('error', (error: Error) => {
      this.eventStatistics.errorCount++;
      this.logger.error('Event emitter error', {
        error: error.message,
        stack: error.stack
      });
    });
  }

  /**
   * Add event listener with enhanced features
   */
  addEventListener(
    event: string,
    handler: (...args: any[]) => void | Promise<void>,
    options?: {
      once?: boolean;
      priority?: number;
      id?: string;
    }
  ): string {
    const listenerId = options?.id || this.generateListenerId();
    
    const listener: EventListener = {
      id: listenerId,
      event,
      handler,
      once: options?.once || false,
      priority: options?.priority || 0
    };

    // Wrap handler for logging and error handling
    const wrappedHandler = async (...args: any[]) => {
      try {
        if (this.enableEventLogging) {
          this.logger.debug('Event handler called', {
            event,
            listenerId,
            argsCount: args.length
          });
        }

        await handler(...args);
      } catch (error) {
        this.eventStatistics.errorCount++;
        this.logger.error('Event handler error', {
          event,
          listenerId,
          error: (error as Error).message
        });
        
        // Emit error event
        this.emit('error', error, event);
      }
    };

    // Store listener info
    this.eventListeners.set(listenerId, listener);
    this.eventStatistics.listenerCount++;

    // Add to EventEmitter
    if (listener.once) {
      this.once(event, wrappedHandler);
    } else {
      this.on(event, wrappedHandler);
    }

    this.logger.debug('Event listener added', {
      event,
      listenerId,
      once: listener.once,
      priority: listener.priority,
      totalListeners: this.eventStatistics.listenerCount
    });

    return listenerId;
  }

  /**
   * Remove event listener by ID
   */
  removeEventListener(listenerId: string): boolean {
    const listener = this.eventListeners.get(listenerId);
    if (!listener) {
      return false;
    }

    // Remove from EventEmitter
    this.removeAllListeners(listener.event);

    // Re-add other listeners for the same event
    for (const [id, otherListener] of this.eventListeners.entries()) {
      if (id !== listenerId && otherListener.event === listener.event) {
        if (otherListener.once) {
          this.once(otherListener.event, otherListener.handler);
        } else {
          this.on(otherListener.event, otherListener.handler);
        }
      }
    }

    // Remove from our tracking
    this.eventListeners.delete(listenerId);
    this.eventStatistics.listenerCount--;

    this.logger.debug('Event listener removed', {
      event: listener.event,
      listenerId,
      remainingListeners: this.eventStatistics.listenerCount
    });

    return true;
  }

  /**
   * Emit session event with structured data
   */
  emitSessionEvent(
    userId: string,
    sessionId: string,
    eventType: SessionEventType,
    data?: any,
    metadata?: Record<string, any>
  ): boolean {
    const sessionEvent: SessionEvent = {
      type: eventType,
      userId,
      sessionId,
      timestamp: new Date(),
      data,
      metadata
    };

    return this.emitEvent('session_event', sessionEvent);
  }

  /**
   * Emit QR code event
   */
  emitQREvent(
    userId: string,
    qrCode: string,
    expiresAt?: Date,
    metadata?: Record<string, any>
  ): boolean {
    return this.emitEvent('qr', {
      userId,
      qrCode,
      expiresAt,
      timestamp: new Date(),
      metadata
    });
  }

  /**
   * Emit connection event
   */
  emitConnectionEvent(
    userId: string,
    status: string,
    phoneNumber?: string,
    reason?: string,
    metadata?: Record<string, any>
  ): boolean {
    const eventData = {
      userId,
      status,
      phoneNumber,
      reason,
      timestamp: new Date(),
      metadata
    };

    // Emit specific connection events
    switch (status) {
      case 'connecting':
        this.emitEvent('connecting', eventData);
        break;
      case 'connected':
        this.emitEvent('connected', eventData);
        break;
      case 'disconnected':
        this.emitEvent('disconnected', eventData);
        break;
      case 'qr_pending':
        this.emitEvent('qr_pending', eventData);
        break;
    }

    // Also emit generic connection event
    return this.emitEvent('connection_update', eventData);
  }

  /**
   * Emit message event
   */
  emitMessageEvent(
    userId: string,
    messageType: 'received' | 'sent' | 'updated',
    messageData: any,
    metadata?: Record<string, any>
  ): boolean {
    const eventData = {
      userId,
      messageType,
      messageData,
      timestamp: new Date(),
      metadata
    };

    // Emit specific message events
    this.emitEvent(`message_${messageType}`, eventData);

    // Also emit generic message event
    return this.emitEvent('message', eventData);
  }

  /**
   * Emit error event
   */
  emitErrorEvent(
    userId: string,
    error: Error,
    context?: string,
    metadata?: Record<string, any>
  ): boolean {
    return this.emitEvent('error', {
      userId,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context,
      timestamp: new Date(),
      metadata
    });
  }

  /**
   * Enhanced emit with logging and statistics
   */
  private emitEvent(event: string, ...args: any[]): boolean {
    try {
      // Update statistics
      this.eventStatistics.totalEvents++;
      this.eventStatistics.eventsByType[event] = (this.eventStatistics.eventsByType[event] || 0) + 1;
      this.eventStatistics.lastEvent = {
        type: event,
        timestamp: new Date(),
        userId: args[0]?.userId
      };

      if (this.enableEventLogging) {
        this.logger.debug('Event emitted', {
          event,
          argsCount: args.length,
          listenerCount: this.listenerCount(event),
          totalEvents: this.eventStatistics.totalEvents
        });
      }

      // Emit the event
      return this.emit(event, ...args);
    } catch (error) {
      this.eventStatistics.errorCount++;
      this.logger.error('Failed to emit event', {
        event,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get event statistics
   */
  getStatistics(): EventStatistics {
    return { ...this.eventStatistics };
  }

  /**
   * Get all registered listeners
   */
  getListeners(): EventListener[] {
    return Array.from(this.eventListeners.values());
  }

  /**
   * Get listeners for specific event
   */
  getListenersForEvent(event: string): EventListener[] {
    return Array.from(this.eventListeners.values())
      .filter(listener => listener.event === event);
  }

  /**
   * Clear all listeners
   */
  clearAllListeners(): void {
    this.removeAllListeners();
    this.eventListeners.clear();
    this.eventStatistics.listenerCount = 0;
    
    this.logger.info('All event listeners cleared');
  }

  /**
   * Clear listeners for specific event
   */
  clearListenersForEvent(event: string): void {
    const listenersToRemove = Array.from(this.eventListeners.entries())
      .filter(([_, listener]) => listener.event === event)
      .map(([id, _]) => id);

    for (const id of listenersToRemove) {
      this.removeEventListener(id);
    }

    this.logger.debug('Event listeners cleared', {
      event,
      removedCount: listenersToRemove.length
    });
  }

  /**
   * Generate unique listener ID
   */
  private generateListenerId(): string {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Reset statistics
   */
  resetStatistics(): void {
    this.eventStatistics.totalEvents = 0;
    this.eventStatistics.eventsByType = {};
    this.eventStatistics.errorCount = 0;
    this.eventStatistics.lastEvent = undefined;
    
    this.logger.info('Event statistics reset');
  }

  /**
   * Check if event has listeners
   */
  hasListeners(event: string): boolean {
    return this.listenerCount(event) > 0;
  }

  /**
   * Get memory usage of event system
   */
  getMemoryUsage(): {
    listenerCount: number;
    eventTypesCount: number;
    statisticsSize: number;
  } {
    return {
      listenerCount: this.eventListeners.size,
      eventTypesCount: Object.keys(this.eventStatistics.eventsByType).length,
      statisticsSize: JSON.stringify(this.eventStatistics).length
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.clearAllListeners();
    this.resetStatistics();
    this.logger.info('BaileysEventEmitter destroyed');
  }
}
