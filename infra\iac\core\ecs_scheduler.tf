# ECS Scheduler for ${var.api_subdomain}.{env}.${var.domain_name}

# Create ECS Scheduler for services with tag schedule=true
module "api_scheduler" {
  source = "../../modules/ecs_scheduler"

  env              = var.env
  app_name         = "api"
  ecs_cluster_name = module.api_platform.cluster_name

  # Tag configuration - target services with schedule=true tag
  tag_key   = "schedule"
  tag_value = "true"

  # Default desired count when scaling up
  default_desired_count = 1

  # Schedule expressions
  scale_down_expression = "cron(0 12 ? * MON-FRI *)" # 8 PM SGT = 12 PM UTC
  scale_up_expression   = "cron(0 0 ? * MON-FRI *)"  # 8 AM SGT = 0 AM UTC

  # Enable the scheduler
  enabled = true

  tags = merge(local.this.tags, {
    component = "ecs-scheduler"
  })
}

# Outputs
output "scheduler_scale_down_arn" {
  value       = module.api_scheduler.scale_down_schedule_arn
  description = "ARN of the scale down schedule"
}

output "scheduler_scale_up_arn" {
  value       = module.api_scheduler.scale_up_schedule_arn
  description = "ARN of the scale up schedule"
}

output "scheduler_lambda_arn" {
  value       = module.api_scheduler.lambda_function_arn
  description = "ARN of the Lambda function"
}
