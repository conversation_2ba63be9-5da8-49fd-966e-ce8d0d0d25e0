{"info": {"name": "WhatsApp Manager API - Ezychat", "description": "Complete API collection for WhatsApp Session Management using Baileys + DynamoDB", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "apiPrefix", "value": "/api", "type": "string"}, {"key": "userId", "value": "test-user-123", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health"]}}, "response": []}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/detailed", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "detailed"]}}, "response": []}]}, {"name": "Session Management", "item": [{"name": "Start Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"WhatsApp Manager\",\n  \"browserName\": \"Chrome\",\n  \"forceNew\": false\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Start Session (Force New)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"WhatsApp Manager\",\n  \"browserName\": \"Chrome\",\n  \"forceNew\": true\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Reconnect Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Manual reconnection\"\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/reconnect", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "reconnect"]}}, "response": []}, {"name": "Get Session Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Get Session Status with Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}?includeHealth=true", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"], "query": [{"key": "includeHealth", "value": "true"}]}}, "response": []}, {"name": "Terminate Session", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Manual termination\",\n  \"forceDelete\": false\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}, {"name": "Force Terminate Session", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Force termination\",\n  \"forceDelete\": true\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}}, "response": []}]}, {"name": "Session Listing & Statistics", "item": [{"name": "List All Sessions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions"]}}, "response": []}, {"name": "List Sessions with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions?limit=10&offset=0&sortBy=updatedAt&sortOrder=desc", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}, {"key": "sortBy", "value": "updatedAt"}, {"key": "sortOrder", "value": "desc"}]}}, "response": []}, {"name": "List Sessions by Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions?status=connected", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions"], "query": [{"key": "status", "value": "connected"}]}}, "response": []}, {"name": "List Active Sessions Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/active", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "active"]}}, "response": []}, {"name": "Get Session Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/statistics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "statistics"]}}, "response": []}]}, {"name": "Admin Operations", "item": [{"name": "Cleanup Expired Sessions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/cleanup/expired", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "cleanup", "expired"]}}, "response": []}, {"name": "Terminate All Sessions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"System maintenance\"\n}"}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/terminate-all", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "terminate-all"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic userId if not already set", "if (!pm.collectionVariables.get('userId')) {", "    pm.collectionVariables.set('userId', 'test-user-' + Date.now());", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}]}