import { HealthService } from '../../src/application/services/health.service';
import { TestDIContainer } from '../../src/di/test-container';

describe('HealthService', () => {
  let healthService: HealthService;

  beforeEach(() => {
    healthService = TestDIContainer.resolve<HealthService>('HealthService');
  });

  describe('getHealth', () => {
    it('should return health status with all checks', async () => {
      const health = await healthService.getHealth();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('environment', 'test');
      expect(health).toHaveProperty('version');
      expect(health).toHaveProperty('uptime');
      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('checks');

      expect(health.checks).toHaveProperty('database');
      expect(health.checks).toHaveProperty('memory');
      expect(health.checks).toHaveProperty('dependencies');

      expect(['ok', 'degraded', 'down']).toContain(health.status);
    });

    it('should have valid timestamp format', async () => {
      const health = await healthService.getHealth();
      const timestamp = new Date(health.timestamp);
      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).not.toBeNaN();
    });

    it('should have positive uptime', async () => {
      const health = await healthService.getHealth();
      expect(health.uptime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('checkDatabase', () => {
    it('should return ok status with valid configuration', async () => {
      const dbCheck = await healthService.checkDatabase();

      expect(dbCheck.status).toBe('ok');
      expect(dbCheck.message).toBe('Database configuration valid');
      expect(dbCheck).toHaveProperty('responseTime');
      expect(dbCheck.responseTime).toBeGreaterThanOrEqual(0);
      expect(dbCheck.details).toEqual({
        tableName: 'WhatsAppSessions-test',
        region: 'ap-southeast-1',
        endpoint: 'AWS DynamoDB',
      });
    });

    it('should return database check with proper structure', async () => {
      const dbCheck = await healthService.checkDatabase();

      expect(dbCheck).toHaveProperty('status');
      expect(dbCheck).toHaveProperty('message');
      expect(dbCheck).toHaveProperty('responseTime');
      expect(typeof dbCheck.responseTime).toBe('number');
      expect(dbCheck.responseTime).toBeGreaterThanOrEqual(0);

      if (dbCheck.status === 'ok') {
        expect(dbCheck).toHaveProperty('details');
        expect(dbCheck.details).toHaveProperty('tableName');
        expect(dbCheck.details).toHaveProperty('region');
      }
    });
  });

  describe('checkMemory', () => {
    it('should return memory usage information', () => {
      const memoryCheck = healthService.checkMemory();

      expect(memoryCheck).toHaveProperty('status');
      expect(['ok', 'warn', 'error']).toContain(memoryCheck.status);
      expect(memoryCheck).toHaveProperty('message');
      expect(memoryCheck).toHaveProperty('details');

      expect(memoryCheck.details).toHaveProperty('rss');
      expect(memoryCheck.details).toHaveProperty('heapUsed');
      expect(memoryCheck.details).toHaveProperty('heapTotal');
      expect(memoryCheck.details).toHaveProperty('heapUsagePercent');

      // Verify format
      expect(memoryCheck.details).toBeDefined();
      if (memoryCheck.details) {
        const details = memoryCheck.details as any;
        expect(details.rss).toMatch(/^\d+MB$/);
        expect(details.heapUsed).toMatch(/^\d+MB$/);
        expect(details.heapTotal).toMatch(/^\d+MB$/);
        expect(details.heapUsagePercent).toMatch(/^\d+%$/);
      }
    });

    it('should return ok status for normal memory usage', () => {
      const memoryCheck = healthService.checkMemory();
      
      // In test environment, memory usage should be normal
      expect(['ok', 'warn', 'error']).toContain(memoryCheck.status);
    });
  });

  describe('overall status determination', () => {
    it('should return ok when all checks pass', async () => {
      const health = await healthService.getHealth();
      
      // In test environment with valid config, should be ok or degraded
      expect(['ok', 'degraded', 'down']).toContain(health.status);
    });
  });
});
