name: App - ECS Python CI/CD

on:
  push:
    branches: [main]
    paths:
      - "sample/ecs-python/**"
  pull_request:
    branches: [main]
    paths:
      - "sample/ecs-python/**"
  workflow_dispatch:

# 🔧 PROJECT CONFIGURATION
# For new ECS Python applications, modify these values:
# 1. app-path: Directory containing your application code
# 2. service-name: Key name in ecs_services.yaml config file
# 3. test-hosts: ALB DNS names for health check links in summary
#
# Note: This workflow uses GitOps approach - it updates ecs_services.yaml
# and lets Terraform handle the actual ECS deployment when merged to main

env:
  # Project-specific settings (modify these for your application)
  APP_PATH: sample/ecs-python
  SERVICE_NAME: ecs-python-sample # Must match key in ecs_services.yaml
  UAT_TEST_HOST: app.uat.ezychat.ai
  PROD_TEST_HOST: app.ezychat.ai

jobs:
  # Stage 1: Test and Build Python Application
  test-build:
    uses: ./.github/workflows/reusable-python-test-build.yml
    with:
      lambda-path: sample/ecs-python # Reusing lambda-path parameter for app path
      python-version: "3.11"
      run-tests: true
      run-lint: true
      run-build: false # No build step needed for Python ECS apps

  # Stage 2: Build and Push Docker Image to ECR
  docker-build:
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    uses: ./.github/workflows/reusable-docker-build-push.yml
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: sample/ecs-python # Reusing lambda-path parameter for app path
      function-name: ecs-python-sample # Reusing function-name parameter for image name
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  # Stage 3: Update UAT Config (Triggers Terraform Deployment)
  deploy-uat:
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    uses: ./.github/workflows/reusable-ecs-deploy.yml
    permissions:
      contents: write
      pull-requests: write
    with:
      service-name: ecs-python-sample
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      environment-name: uat

  # Summary Job
  summary:
    needs: [test-build, docker-build, deploy-uat]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Workflow Summary
        run: |
          echo "## 🐍 ECS Python CI/CD Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "- **Test & Build**: ${{ needs.test-build.result == 'success' && '✅ Passed' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Docker Build**: ${{ needs.docker-build.result == 'success' && '✅ Passed' || needs.docker-build.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **UAT Config Update**: ${{ needs.deploy-uat.result == 'success' && '✅ Passed' || needs.deploy-uat.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Application Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Application**: ECS Python Sample" >> $GITHUB_STEP_SUMMARY
          echo "- **Path**: \`${{ env.APP_PATH }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Service**: \`${{ env.SERVICE_NAME }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Python Version**: 3.11" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${{ needs.docker-build.result }}" == "success" ]; then
            echo "### 🐳 Docker Image" >> $GITHUB_STEP_SUMMARY
            echo "- **Image URI**: \`${{ needs.docker-build.outputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
          if [ "${{ needs.deploy-uat.result }}" == "success" ]; then
            if [ "${{ needs.deploy-uat.outputs.deployment-status }}" = "deployed" ]; then
              echo "### 🔄 GitOps Deployment Status" >> $GITHUB_STEP_SUMMARY
              echo "- **Config Updated**: ✅ ecs_services.yaml updated with new image" >> $GITHUB_STEP_SUMMARY
              echo "- **Auto-Merge Branch**: \`${{ needs.deploy-uat.outputs.branch-name }}\`" >> $GITHUB_STEP_SUMMARY
              echo "- **Pull Request**: [#${{ needs.deploy-uat.outputs.pr-number }}](${{ needs.deploy-uat.outputs.pr-url }}) ✅ **Auto-merged**" >> $GITHUB_STEP_SUMMARY
              echo "- **Terraform Deploy**: 🔄 Triggered by merge to main" >> $GITHUB_STEP_SUMMARY
            else
              echo "### ⏭️ Deployment Skipped" >> $GITHUB_STEP_SUMMARY
              echo "- **Reason**: Image unchanged - no deployment needed" >> $GITHUB_STEP_SUMMARY
              echo "- **Current Image**: \`${{ needs.docker-build.outputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
              echo "- **Status**: ✅ Service already running latest version" >> $GITHUB_STEP_SUMMARY
            fi
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 📋 Deployment Details" >> $GITHUB_STEP_SUMMARY
            echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
            echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY
            echo "| **Service** | \`${{ env.SERVICE_NAME }}\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Environment** | \`uat\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Image** | \`${{ needs.docker-build.outputs.image-uri }}\` |" >> $GITHUB_STEP_SUMMARY
            echo "| **Config File** | \`infra/iac/core/configs/ecs_services.yaml\` |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            if [ "${{ needs.deploy-uat.outputs.deployment-status }}" = "deployed" ]; then
              echo "### 🌐 UAT Environment (After Terraform Deploy)" >> $GITHUB_STEP_SUMMARY
              echo "- **Health Check**: https://${{ env.UAT_TEST_HOST }}/health" >> $GITHUB_STEP_SUMMARY
              echo "- **Python Test**: https://${{ env.UAT_TEST_HOST }}/python-test" >> $GITHUB_STEP_SUMMARY
            else
              echo "### 🌐 UAT Environment (Current)" >> $GITHUB_STEP_SUMMARY
              echo "- **Health Check**: https://${{ env.UAT_TEST_HOST }}/health" >> $GITHUB_STEP_SUMMARY
              echo "- **Python Test**: https://${{ env.UAT_TEST_HOST }}/python-test" >> $GITHUB_STEP_SUMMARY
              echo "- **Status**: Already running the requested image version" >> $GITHUB_STEP_SUMMARY
            fi
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🔗 Quick Links" >> $GITHUB_STEP_SUMMARY
            if [ "${{ needs.deploy-uat.outputs.deployment-status }}" = "deployed" ]; then
              echo "- 📋 **[View Pull Request #${{ needs.deploy-uat.outputs.pr-number }}](${{ needs.deploy-uat.outputs.pr-url }})** - See what was changed" >> $GITHUB_STEP_SUMMARY
            fi
            echo "- 📝 **[View Config File](https://github.com/${{ github.repository }}/blob/main/infra/iac/core/configs/ecs_services.yaml)** - ecs_services.yaml" >> $GITHUB_STEP_SUMMARY
            echo "- 🔄 **[View Actions](https://github.com/${{ github.repository }}/actions)** - Monitor Terraform deployment" >> $GITHUB_STEP_SUMMARY
            echo "- 🌐 **[UAT Health Check](https://${{ env.UAT_TEST_HOST }}/health)** - Test the deployment" >> $GITHUB_STEP_SUMMARY
          fi
