variable "env" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "app_name" {
  description = "Application name"
  type        = string
}

variable "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  type        = string
}

variable "tag_key" {
  description = "Tag key to identify services for scheduling"
  type        = string
  default     = "schedule"
}

variable "tag_value" {
  description = "Tag value to identify services for scheduling"
  type        = string
  default     = "true"
}

variable "default_desired_count" {
  description = "Default desired count for services when scaling up"
  type        = number
  default     = 1
}

variable "enabled" {
  description = "Whether the scheduler is enabled"
  type        = bool
  default     = true
}

variable "scale_down_expression" {
  description = "Schedule expression for scaling down (default: 8 PM SGT on weekdays)"
  type        = string
  default     = "cron(0 12 ? * MON-FRI *)" # 8 PM SGT = 12 PM UTC
}

variable "scale_up_expression" {
  description = "Schedule expression for scaling up (default: 8 AM SGT on weekdays)"
  type        = string
  default     = "cron(0 0 ? * MON-FRI *)" # 8 AM SGT = 0 AM UTC
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
