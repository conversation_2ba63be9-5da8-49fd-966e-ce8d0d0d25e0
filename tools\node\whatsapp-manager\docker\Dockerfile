# Multi-stage build for optimal production image
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code and build
COPY . .
RUN npm run build

# Production stage
FROM node:20-alpine AS runtime

# Security: create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S whatsapp -u 1001

# Install curl for health checks
RUN apk add --no-cache curl

WORKDIR /app

# Copy dependencies and built application
COPY --from=builder --chown=whatsapp:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=whatsapp:nodejs /app/dist ./dist
COPY --from=builder --chown=whatsapp:nodejs /app/package*.json ./
COPY --from=builder --chown=whatsapp:nodejs /app/environments ./environments

# Switch to non-root user
USER whatsapp

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["node", "dist/index.js"]
