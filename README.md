```
/ezychat/
│
├── apps/                             # Agent-specific orchestrators and entrypoints
│   └── dify-webhook/                 # Handles webhook events from Dify
│       └── README.md                 # Docs for webhook formats, routes, and processing logic
│
├── tools/                            # Reusable agent tools (Python/Node)
│   ├── python/
│   │   ├── agent-orchestrator/                   # Python tool (e.g. PDF parser, vector 
embedder)
│   │   │   └── README.md             # Purpose, endpoints, usage examples
│   │   └── tool_y/
│   │       └── README.md
│   └── node/
│       ├── whatsapp-manager/                   # Node.js tool (e.g. WhatsApp sender, API bridge)
│       │   └── README.md
│       └── tool_b/
│           └── README.md
│
├── libs/                             # Shared libraries for reuse across tools/apps
│   ├── python/
│   │   └── README.md                 # Shared Python utils, schemas, error handling
│   └── node/
│       └── README.md
│
├── infra/                            # Infrastructure related files
│   ├── iac/                          # Infrastructure-as-Code (modularized)
│   │   ├── aws-ezychat-prod-tf/      # Terraform resources to create PROD environment
│   │   │   └── README.md
│   │   ├── aws-ezychat-uat-tf/       # Terraform resources to create UAT environment
│   │   │   └── README.md
│   │   ├── tfc-workspace-tf/         # Resources to create Terraform workspace
│   │       └── README.md
│   └── README.md                     # Infra overview, bootstrap instructions
│
├── package.json / pyproject.toml     # Optional root dependencies (Node or Python)
├── .github/workflows/                # CI/CD pipelines (build, test, deploy)
│   └── README.md                     # CI/CD overview and environment info
└── README.md                         # High-level project overview, structure, and how to get started
```
