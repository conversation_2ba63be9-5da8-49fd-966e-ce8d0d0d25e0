import { parsePhoneNumber, isValidPhoneNumber, CountryCode } from 'libphonenumber-js';

/**
 * PhoneNumber value object
 * Provides validation and formatting for phone numbers using libphonenumber-js
 */
export class PhoneNumber {
  private readonly value: string;
  private readonly countryCode: string;
  private readonly nationalNumber: string;
  private readonly internationalFormat: string;

  constructor(value: string, defaultCountry?: CountryCode) {
    const cleanValue = this.cleanPhoneNumber(value);
    this.validateAndParse(cleanValue, defaultCountry);
    
    const parsed = parsePhoneNumber(cleanValue, defaultCountry);
    if (!parsed) {
      throw new Error('Invalid phone number format');
    }

    this.value = parsed.number;
    this.countryCode = parsed.countryCallingCode;
    this.nationalNumber = parsed.nationalNumber;
    this.internationalFormat = parsed.formatInternational();
  }

  /**
   * Create PhoneNumber from string with optional country code
   */
  static fromString(value: string, defaultCountry?: CountryCode): PhoneNumber {
    return new PhoneNumber(value, defaultCountry);
  }

  /**
   * Create PhoneNumber from international format
   */
  static fromInternational(value: string): PhoneNumber {
    if (!value.startsWith('+')) {
      throw new Error('International phone number must start with +');
    }
    return new PhoneNumber(value);
  }

  /**
   * Get the full international phone number (e.g., +1234567890)
   */
  toString(): string {
    return this.value;
  }

  /**
   * Get the raw value
   */
  getValue(): string {
    return this.value;
  }

  /**
   * Get the country calling code (e.g., "1" for US, "60" for Malaysia)
   */
  getCountryCode(): string {
    return this.countryCode;
  }

  /**
   * Get the national number without country code
   */
  getNationalNumber(): string {
    return this.nationalNumber;
  }

  /**
   * Get the local number (national number without leading zeros)
   */
  getLocalNumber(): string {
    return this.nationalNumber.replace(/^0+/, '');
  }

  /**
   * Get formatted international representation (e.g., "****** 567 8900")
   */
  getInternationalFormat(): string {
    return this.internationalFormat;
  }

  /**
   * Get formatted national representation
   */
  getNationalFormat(): string {
    const parsed = parsePhoneNumber(this.value);
    return parsed ? parsed.formatNational() : this.nationalNumber;
  }

  /**
   * Get the country ISO code if available
   */
  getCountryISO(): string | undefined {
    const parsed = parsePhoneNumber(this.value);
    return parsed?.country;
  }

  /**
   * Check if this is a mobile number
   */
  isMobile(): boolean {
    const parsed = parsePhoneNumber(this.value);
    return parsed ? parsed.getType() === 'MOBILE' : false;
  }

  /**
   * Check equality with another PhoneNumber
   */
  equals(other: PhoneNumber): boolean {
    return this.value === other.value;
  }

  /**
   * Check if the phone number is from a specific country
   */
  isFromCountry(countryCode: CountryCode): boolean {
    return this.getCountryISO() === countryCode;
  }

  /**
   * Get WhatsApp-compatible format (without + prefix, just digits)
   */
  getWhatsAppFormat(): string {
    return this.value.replace('+', '');
  }

  /**
   * Get display format for UI (masked for privacy if needed)
   */
  getDisplayFormat(maskDigits: boolean = false): string {
    if (!maskDigits) {
      return this.getInternationalFormat();
    }

    const formatted = this.getInternationalFormat();
    // Mask middle digits, keep country code and last 4 digits visible
    const parts = formatted.split(' ');
    if (parts.length >= 3) {
      const lastPart = parts[parts.length - 1];
      const maskedPart = '*'.repeat(Math.max(0, lastPart.length - 4)) + lastPart.slice(-4);
      parts[parts.length - 1] = maskedPart;
      return parts.join(' ');
    }
    return formatted;
  }

  /**
   * Validate and parse phone number
   */
  private validateAndParse(value: string, defaultCountry?: CountryCode): void {
    if (!value || typeof value !== 'string') {
      throw new Error('Phone number must be a non-empty string');
    }

    if (!isValidPhoneNumber(value, defaultCountry)) {
      throw new Error(`Invalid phone number: ${value}`);
    }

    const parsed = parsePhoneNumber(value, defaultCountry);
    if (!parsed) {
      throw new Error(`Unable to parse phone number: ${value}`);
    }

    if (!parsed.isValid()) {
      throw new Error(`Invalid phone number format: ${value}`);
    }
  }

  /**
   * Clean phone number by removing common formatting characters
   */
  private cleanPhoneNumber(value: string): string {
    // Remove common formatting characters but keep + for international format
    return value.replace(/[\s\-\(\)\.]/g, '');
  }

  /**
   * JSON serialization
   */
  toJSON(): string {
    return this.value;
  }

  /**
   * Create from JSON
   */
  static fromJSON(json: string): PhoneNumber {
    return new PhoneNumber(json);
  }

  /**
   * Create a masked version for logging (privacy protection)
   */
  toMaskedString(): string {
    const countryCodeLength = this.countryCode.length + 1; // +1 for the + sign
    const visibleDigits = 4;
    const totalLength = this.value.length;
    
    if (totalLength <= countryCodeLength + visibleDigits) {
      return this.value;
    }

    const prefix = this.value.substring(0, countryCodeLength);
    const suffix = this.value.substring(totalLength - visibleDigits);
    const maskLength = totalLength - countryCodeLength - visibleDigits;
    
    return `${prefix}${'*'.repeat(maskLength)}${suffix}`;
  }
}
