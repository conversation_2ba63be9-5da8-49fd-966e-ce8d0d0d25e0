import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  DeleteCommand,
  Query<PERSON>ommand,
  ScanCommand,
  BatchWriteCommand
} from '@aws-sdk/lib-dynamodb';
import { injectable, inject } from 'tsyringe';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';
import { AuthToken, AuthTokenError, AuthTokenNotFoundError, AuthTokenExpiredError, AuthTokenUsedError } from '../../domain/value-objects/AuthToken';
import { DI_TOKENS } from '../../di/tokens';

/**
 * DynamoDB implementation of IAuthTokenRepository
 * Uses single table design with TTL for automatic cleanup
 */
@injectable()
export class AuthTokenRepositoryDynamoDB implements IAuthTokenRepository {
  private readonly docClient: DynamoDBDocumentClient;
  private readonly tableName: string;

  constructor(
    @inject(DI_TOKENS.DynamoDBClient) dynamoClient: DynamoDBClient
  ) {
    this.docClient = DynamoDBDocumentClient.from(dynamoClient);
    this.tableName = process.env['AUTH_TOKEN_TABLE_NAME'] || 'AuthTokens-dev';
  }

  async create(token: AuthToken): Promise<void> {
    try {
      const item = token.toDynamoDBItem();
      
      await this.docClient.send(new PutCommand({
        TableName: this.tableName,
        Item: item,
        ConditionExpression: 'attribute_not_exists(PK)',
        ReturnConsumedCapacity: 'TOTAL'
      }));
    } catch (error) {
      if (error.name === 'ConditionalCheckFailedException') {
        throw new AuthTokenError(`Auth token with ID ${token.tokenId} already exists`);
      }
      throw new AuthTokenError(`Failed to create auth token: ${error.message}`);
    }
  }

  async findByTokenId(tokenId: string): Promise<AuthToken | null> {
    if (!tokenId) {
      throw new AuthTokenError('Token ID is required');
    }

    try {
      // Since we don't know the exact SK, we need to query instead
      return await this.queryByTokenId(tokenId);
    } catch (error) {
      throw new AuthTokenError(`Failed to find token by ID: ${error.message}`);
    }
  }

  async findByToken(tokenString: string): Promise<AuthToken | null> {
    if (!tokenString) {
      throw new AuthTokenError('Token string is required');
    }

    try {
      // Try to extract token ID from JWT to avoid scan operation
      const tokenId = this.extractTokenIdFromJWT(tokenString);
      if (tokenId) {
        return await this.findByTokenId(tokenId);
      }

      // Fallback to scan if token ID extraction fails
      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: '#token = :token AND begins_with(PK, :pkPrefix)',
        ExpressionAttributeNames: {
          '#token': 'token'
        },
        ExpressionAttributeValues: {
          ':token': tokenString,
          ':pkPrefix': 'AUTH_TOKEN#'
        },
        Limit: 1
      }));

      if (!result.Items || result.Items.length === 0) {
        return null;
      }

      return AuthToken.fromDynamoDBItem(result.Items[0]);
    } catch (error) {
      throw new AuthTokenError(`Failed to find token by string: ${error.message}`);
    }
  }

  /**
   * Extract token ID from JWT without full verification (for optimization)
   */
  private extractTokenIdFromJWT(jwtToken: string): string | null {
    try {
      // JWT has 3 parts separated by dots
      const parts = jwtToken.split('.');
      if (parts.length !== 3) {
        return null;
      }

      // Decode the payload (second part)
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
      return payload.tokenId || payload.jti || null;
    } catch (error) {
      // If extraction fails, return null to fallback to scan
      return null;
    }
  }

  /**
   * Find auth token by JTI (JWT ID)
   */
  async findByJti(jti: string): Promise<AuthToken | null> {
    if (!jti) {
      throw new AuthTokenError('JTI is required');
    }

    try {
      // Scan for the token by JTI in metadata
      // The metadata is stored as a JSON string, so we need to search for the JTI value
      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix) AND contains(#metadata, :jti)',
        ExpressionAttributeNames: {
          '#metadata': 'metadata'
        },
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#',
          ':jti': jti  // Just search for the JTI value itself
        },
        Limit: 1
      }));

      if (!result.Items || result.Items.length === 0) {
        return null;
      }

      return AuthToken.fromDynamoDBItem(result.Items[0]);
    } catch (error) {
      throw new AuthTokenError(`Failed to find token by JTI: ${error.message}`);
    }
  }

  async findByUserId(userId: string, includeExpired: boolean = false): Promise<AuthToken[]> {
    if (!userId) {
      throw new AuthTokenError('User ID is required');
    }

    try {
      const result = await this.docClient.send(new QueryCommand({
        TableName: this.tableName,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: {
          ':gsi1pk': `USER#${userId}`
        },
        ScanIndexForward: false // Most recent first
      }));

      if (!result.Items) {
        return [];
      }

      const tokens = result.Items
        .filter(item => item['PK']?.startsWith('AUTH_TOKEN#'))
        .map(item => AuthToken.fromDynamoDBItem(item));

      if (!includeExpired) {
        return tokens.filter(token => !token.isExpired());
      }

      return tokens;
    } catch (error) {
      throw new AuthTokenError(`Failed to find tokens by user ID: ${error.message}`);
    }
  }

  async update(token: AuthToken): Promise<void> {
    try {
      const item = token.toDynamoDBItem();
      
      await this.docClient.send(new PutCommand({
        TableName: this.tableName,
        Item: item,
        ConditionExpression: 'attribute_exists(PK)'
      }));
    } catch (error) {
      if (error.name === 'ConditionalCheckFailedException') {
        throw new AuthTokenNotFoundError(`Auth token with ID ${token.tokenId} not found`);
      }
      throw new AuthTokenError(`Failed to update auth token: ${error.message}`);
    }
  }

  async markAsUsed(tokenId: string): Promise<AuthToken> {
    if (!tokenId) {
      throw new AuthTokenError('Token ID is required');
    }

    try {
      // First, find the token to get the SK
      const existingToken = await this.queryByTokenId(tokenId);
      if (!existingToken) {
        throw new AuthTokenNotFoundError(`Auth token with ID ${tokenId} not found`);
      }

      if (existingToken.isExpired()) {
        throw new AuthTokenExpiredError('Cannot use expired token');
      }

      if (existingToken.isUsed) {
        throw new AuthTokenUsedError('Token has already been used');
      }

      const updatedToken = existingToken.markAsUsed();
      await this.update(updatedToken);
      
      return updatedToken;
    } catch (error) {
      if (error instanceof AuthTokenNotFoundError || 
          error instanceof AuthTokenExpiredError || 
          error instanceof AuthTokenUsedError) {
        throw error;
      }
      throw new AuthTokenError(`Failed to mark token as used: ${error.message}`);
    }
  }

  async delete(tokenId: string): Promise<boolean> {
    if (!tokenId) {
      throw new AuthTokenError('Token ID is required');
    }

    try {
      // First find the token to get the exact keys
      const token = await this.queryByTokenId(tokenId);
      if (!token) {
        return false;
      }

      await this.docClient.send(new DeleteCommand({
        TableName: this.tableName,
        Key: {
          PK: `AUTH_TOKEN#${tokenId}`,
          SK: `USER#${token.userId}`
        }
      }));

      return true;
    } catch (error) {
      throw new AuthTokenError(`Failed to delete token: ${error.message}`);
    }
  }

  async deleteByUserId(userId: string): Promise<number> {
    if (!userId) {
      throw new AuthTokenError('User ID is required');
    }

    try {
      const tokens = await this.findByUserId(userId, true);
      
      if (tokens.length === 0) {
        return 0;
      }

      // Batch delete in chunks of 25 (DynamoDB limit)
      const chunks = this.chunkArray(tokens, 25);
      let deletedCount = 0;

      for (const chunk of chunks) {
        const deleteRequests = chunk.map(token => ({
          DeleteRequest: {
            Key: {
              PK: `AUTH_TOKEN#${token.tokenId}`,
              SK: `USER#${token.userId}`
            }
          }
        }));

        await this.docClient.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: deleteRequests
          }
        }));

        deletedCount += chunk.length;
      }

      return deletedCount;
    } catch (error) {
      throw new AuthTokenError(`Failed to delete tokens by user ID: ${error.message}`);
    }
  }

  async deleteExpiredTokens(batchSize: number = 100): Promise<number> {
    try {
      const now = Math.floor(Date.now() / 1000);
      
      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix) AND #ttl < :now',
        ExpressionAttributeNames: {
          '#ttl': 'ttl'
        },
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#',
          ':now': now
        },
        Limit: batchSize
      }));

      if (!result.Items || result.Items.length === 0) {
        return 0;
      }

      // Batch delete expired tokens
      const chunks = this.chunkArray(result.Items, 25);
      let deletedCount = 0;

      for (const chunk of chunks) {
        const deleteRequests = chunk.map(item => ({
          DeleteRequest: {
            Key: {
              PK: item['PK'],
              SK: item['SK']
            }
          }
        }));

        await this.docClient.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: deleteRequests
          }
        }));

        deletedCount += chunk.length;
      }

      return deletedCount;
    } catch (error) {
      throw new AuthTokenError(`Failed to delete expired tokens: ${error.message}`);
    }
  }

  async countActiveTokensByUserId(userId: string): Promise<number> {
    if (!userId) {
      throw new AuthTokenError('User ID is required');
    }

    try {
      const tokens = await this.findByUserId(userId, false);
      return tokens.filter(token => !token.isUsed).length;
    } catch (error) {
      throw new AuthTokenError(`Failed to count active tokens: ${error.message}`);
    }
  }

  async countTotalTokens(includeExpired: boolean = false): Promise<number> {
    try {
      const params: any = {
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix)',
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#'
        },
        Select: 'COUNT'
      };

      if (!includeExpired) {
        const now = Math.floor(Date.now() / 1000);
        params.FilterExpression += ' AND #ttl >= :now';
        params.ExpressionAttributeNames = { '#ttl': 'ttl' };
        params.ExpressionAttributeValues[':now'] = now;
      }

      const result = await this.docClient.send(new ScanCommand(params));
      return result.Count || 0;
    } catch (error) {
      throw new AuthTokenError(`Failed to count total tokens: ${error.message}`);
    }
  }

  async findExpiringTokens(withinSeconds: number): Promise<AuthToken[]> {
    if (withinSeconds <= 0) {
      throw new AuthTokenError('Within seconds must be positive');
    }

    try {
      const now = Math.floor(Date.now() / 1000);
      const threshold = now + withinSeconds;

      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix) AND #ttl BETWEEN :now AND :threshold',
        ExpressionAttributeNames: {
          '#ttl': 'ttl'
        },
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#',
          ':now': now,
          ':threshold': threshold
        }
      }));

      if (!result.Items) {
        return [];
      }

      return result.Items.map(item => AuthToken.fromDynamoDBItem(item));
    } catch (error) {
      throw new AuthTokenError(`Failed to find expiring tokens: ${error.message}`);
    }
  }

  async batchCreate(tokens: AuthToken[]): Promise<void> {
    if (!tokens || tokens.length === 0) {
      return;
    }

    try {
      const chunks = this.chunkArray(tokens, 25);

      for (const chunk of chunks) {
        const putRequests = chunk.map(token => ({
          PutRequest: {
            Item: token.toDynamoDBItem()
          }
        }));

        await this.docClient.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: putRequests
          }
        }));
      }
    } catch (error) {
      throw new AuthTokenError(`Failed to batch create tokens: ${error.message}`);
    }
  }

  async batchDelete(tokenIds: string[]): Promise<number> {
    if (!tokenIds || tokenIds.length === 0) {
      return 0;
    }

    try {
      // First, get all tokens to find their exact keys
      const tokens = await Promise.all(
        tokenIds.map(id => this.queryByTokenId(id))
      );

      const validTokens = tokens.filter(token => token !== null) as AuthToken[];
      
      if (validTokens.length === 0) {
        return 0;
      }

      const chunks = this.chunkArray(validTokens, 25);
      let deletedCount = 0;

      for (const chunk of chunks) {
        const deleteRequests = chunk.map(token => ({
          DeleteRequest: {
            Key: {
              PK: `AUTH_TOKEN#${token.tokenId}`,
              SK: `USER#${token.userId}`
            }
          }
        }));

        await this.docClient.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: deleteRequests
          }
        }));

        deletedCount += chunk.length;
      }

      return deletedCount;
    } catch (error) {
      throw new AuthTokenError(`Failed to batch delete tokens: ${error.message}`);
    }
  }

  async exists(tokenId: string): Promise<boolean> {
    if (!tokenId) {
      throw new AuthTokenError('Token ID is required');
    }

    try {
      const token = await this.queryByTokenId(tokenId);
      return token !== null;
    } catch (error) {
      throw new AuthTokenError(`Failed to check token existence: ${error.message}`);
    }
  }

  async getStatistics(): Promise<{
    totalTokens: number;
    activeTokens: number;
    expiredTokens: number;
    usedTokens: number;
    tokensCreatedToday: number;
    tokensUsedToday: number;
  }> {
    try {
      const now = new Date();
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix)',
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#'
        }
      }));

      if (!result.Items) {
        return {
          totalTokens: 0,
          activeTokens: 0,
          expiredTokens: 0,
          usedTokens: 0,
          tokensCreatedToday: 0,
          tokensUsedToday: 0
        };
      }

      const tokens = result.Items.map(item => AuthToken.fromDynamoDBItem(item));
      
      return {
        totalTokens: tokens.length,
        activeTokens: tokens.filter(t => !t.isExpired() && !t.isUsed).length,
        expiredTokens: tokens.filter(t => t.isExpired()).length,
        usedTokens: tokens.filter(t => t.isUsed).length,
        tokensCreatedToday: tokens.filter(t => t.createdAt >= startOfDay).length,
        tokensUsedToday: tokens.filter(t => t.usedAt && t.usedAt >= startOfDay).length
      };
    } catch (error) {
      throw new AuthTokenError(`Failed to get statistics: ${error.message}`);
    }
  }

  async validateToken(tokenId: string): Promise<AuthToken> {
    const token = await this.queryByTokenId(tokenId);
    
    if (!token) {
      throw new AuthTokenNotFoundError(`Auth token with ID ${tokenId} not found`);
    }

    if (token.isExpired()) {
      throw new AuthTokenExpiredError('Auth token has expired');
    }

    if (token.isUsed) {
      throw new AuthTokenUsedError('Auth token has already been used');
    }

    return token;
  }

  async cleanupOldTokens(retentionDays: number): Promise<number> {
    if (retentionDays <= 0) {
      throw new AuthTokenError('Retention days must be positive');
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      const cutoffTimestamp = Math.floor(cutoffDate.getTime() / 1000);

      const result = await this.docClient.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'begins_with(PK, :pkPrefix) AND #ttl < :cutoff',
        ExpressionAttributeNames: {
          '#ttl': 'ttl'
        },
        ExpressionAttributeValues: {
          ':pkPrefix': 'AUTH_TOKEN#',
          ':cutoff': cutoffTimestamp
        }
      }));

      if (!result.Items || result.Items.length === 0) {
        return 0;
      }

      // Batch delete old tokens
      const chunks = this.chunkArray(result.Items, 25);
      let deletedCount = 0;

      for (const chunk of chunks) {
        const deleteRequests = chunk.map(item => ({
          DeleteRequest: {
            Key: {
              PK: item['PK'],
              SK: item['SK']
            }
          }
        }));

        await this.docClient.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: deleteRequests
          }
        }));

        deletedCount += chunk.length;
      }

      return deletedCount;
    } catch (error) {
      throw new AuthTokenError(`Failed to cleanup old tokens: ${error.message}`);
    }
  }

  /**
   * Helper method to query token by ID using GSI
   */
  private async queryByTokenId(tokenId: string): Promise<AuthToken | null> {
    try {
      const result = await this.docClient.send(new QueryCommand({
        TableName: this.tableName,
        KeyConditionExpression: 'PK = :pk',
        ExpressionAttributeValues: {
          ':pk': `AUTH_TOKEN#${tokenId}`
        },
        Limit: 1
      }));

      if (!result.Items || result.Items.length === 0) {
        return null;
      }

      return AuthToken.fromDynamoDBItem(result.Items[0]);
    } catch (error) {
      throw new AuthTokenError(`Failed to query token by ID: ${error.message}`);
    }
  }

  /**
   * Helper method to chunk arrays for batch operations
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}
