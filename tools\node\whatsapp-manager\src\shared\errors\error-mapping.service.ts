import { AuthTokenError, AuthTokenNotFoundError, AuthTokenExpiredError, AuthTokenUsedError } from '../../domain/value-objects/AuthToken';

/**
 * Error code to HTTP status code mapping
 */
export interface ErrorMapping {
  statusCode: number;
  code: string;
  message: string;
}

/**
 * Centralized error mapping service for consistent HTTP status codes
 */
export class ErrorMappingService {
  private static readonly ERROR_MAPPINGS: Map<string, number> = new Map([
    // Auth Token Errors
    ['TOKEN_NOT_FOUND', 404],
    ['TOKEN_EXPIRED', 410],
    ['TOKEN_USED', 409],
    ['TOKEN_INVALID', 401],
    ['AUTH_TOKEN_ERROR', 400],
    
    // Validation Errors
    ['VALIDATION_ERROR', 400],
    ['INVALID_REQUEST', 400],
    ['INVALID_USER_ID', 400],
    ['INVALID_EXPIRY', 400],
    ['INVALID_MAX_TOKENS', 400],
    ['INVALID_METADATA', 400],
    ['INVALID_BASE_URL', 400],
    ['INVALID_TOKEN_ID', 400],
    
    // Business Logic Errors
    ['MAX_TOKENS_EXCEEDED', 409],
    ['TOKEN_ALREADY_EXISTS', 409],
    
    // Database Errors
    ['DATABASE_ERROR', 500],
    ['DYNAMODB_ERROR', 500],
    
    // Rate Limiting
    ['RATE_LIMIT_EXCEEDED', 429],
    
    // Generic Errors
    ['INTERNAL_ERROR', 500],
    ['TOKEN_ERROR', 400]
  ]);

  /**
   * Get HTTP status code for error
   */
  static getStatusCode(error: any): number {
    // Handle specific error types
    if (error instanceof AuthTokenNotFoundError) {
      return 404;
    }
    
    if (error instanceof AuthTokenExpiredError) {
      return 410;
    }
    
    if (error instanceof AuthTokenUsedError) {
      return 409;
    }
    
    if (error instanceof AuthTokenError) {
      return this.ERROR_MAPPINGS.get(error.code || 'AUTH_TOKEN_ERROR') || 400;
    }

    // Handle error codes
    if (error.code && this.ERROR_MAPPINGS.has(error.code)) {
      return this.ERROR_MAPPINGS.get(error.code)!;
    }

    // Handle error names
    if (error.name) {
      switch (error.name) {
        case 'ValidationError':
          return 400;
        case 'ConditionalCheckFailedException':
          return 409;
        case 'ResourceNotFoundException':
          return 404;
        case 'ThrottlingException':
        case 'ProvisionedThroughputExceededException':
          return 429;
        default:
          break;
      }
    }

    // Handle error messages
    if (error.message) {
      if (error.message.includes('DynamoDB') || error.message.includes('AWS')) {
        return 500;
      }
      if (error.message.includes('JWT') || error.message.includes('token')) {
        return 401;
      }
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return 400;
      }
    }

    // Default to 500 for unknown errors
    return 500;
  }

  /**
   * Get error code for error
   */
  static getErrorCode(error: any): string {
    // Handle specific error types
    if (error instanceof AuthTokenNotFoundError) {
      return 'TOKEN_NOT_FOUND';
    }
    
    if (error instanceof AuthTokenExpiredError) {
      return 'TOKEN_EXPIRED';
    }
    
    if (error instanceof AuthTokenUsedError) {
      return 'TOKEN_USED';
    }
    
    if (error instanceof AuthTokenError) {
      return error.code || 'AUTH_TOKEN_ERROR';
    }

    // Return existing error code if available
    if (error.code) {
      return error.code;
    }

    // Map error names to codes
    if (error.name) {
      switch (error.name) {
        case 'ValidationError':
          return 'VALIDATION_ERROR';
        case 'ConditionalCheckFailedException':
          return 'TOKEN_ALREADY_EXISTS';
        case 'ResourceNotFoundException':
          return 'RESOURCE_NOT_FOUND';
        case 'ThrottlingException':
        case 'ProvisionedThroughputExceededException':
          return 'RATE_LIMIT_EXCEEDED';
        default:
          break;
      }
    }

    // Analyze error message for code
    if (error.message) {
      if (error.message.includes('DynamoDB') || error.message.includes('AWS')) {
        return 'DATABASE_ERROR';
      }
      if (error.message.includes('JWT') || error.message.includes('token')) {
        return 'TOKEN_ERROR';
      }
      if (error.message.includes('validation')) {
        return 'VALIDATION_ERROR';
      }
    }

    return 'INTERNAL_ERROR';
  }

  /**
   * Create standardized error response
   */
  static createErrorResponse(error: any): {
    success: false;
    message: string;
    error: {
      code: string;
      message: string;
      statusCode: number;
      details?: any;
    };
  } {
    const statusCode = this.getStatusCode(error);
    const errorCode = this.getErrorCode(error);
    const message = error.message || 'An unexpected error occurred';

    return {
      success: false,
      message,
      error: {
        code: errorCode,
        message,
        statusCode,
        details: process.env['NODE_ENV'] === 'development' ? {
          name: error.name,
          stack: error.stack
        } : undefined
      }
    };
  }

  /**
   * Check if error should be logged as warning vs error
   */
  static shouldLogAsWarning(error: any): boolean {
    const statusCode = this.getStatusCode(error);
    
    // Client errors (4xx) are typically warnings
    // Server errors (5xx) are errors
    return statusCode >= 400 && statusCode < 500;
  }
}
