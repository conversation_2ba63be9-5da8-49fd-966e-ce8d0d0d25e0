"""
Service Implementations for Document Ingestion Pipeline

This module provides concrete implementations of service interfaces
for external APIs and processing operations.
"""

import time
from typing import Any, Dict, List

import openai

from ..domain.entities import EmbeddingVector
from ..domain.interfaces import IConfiguration, IEmbeddingService, ILogger, IMetricsCollector


class OpenAIEmbeddingService(IEmbeddingService):
    """OpenAI implementation of embedding service"""

    def __init__(self, config: IConfiguration, logger: ILogger, metrics: IMetricsCollector):
        self._config = config
        self._logger = logger
        self._metrics = metrics
        self._client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize OpenAI client"""
        try:
            api_key = self._config.get_openai_api_key()
            if not api_key:
                raise ValueError("OpenAI API key not found")

            self._client = openai.OpenAI(api_key=api_key)
            self._logger.debug("OpenAI client initialized successfully")

        except Exception as e:
            self._metrics.record_error("openai_client_init_error")
            self._logger.error(f"Failed to initialize OpenAI client: {e}", error=e)
            raise

    async def generate_embeddings(self, texts: list[str]) -> list[EmbeddingVector]:
        """Generate embeddings for a list of texts"""
        if not texts:
            return []

        start_time = time.time()

        try:
            self._logger.info(f"Generating embeddings for {len(texts)} texts")

            # Get configuration
            config = self.get_embedding_config()
            batch_size = self._config.get_embedding_batch_size()

            # Process in batches to avoid API limits
            all_embeddings = []

            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i : i + batch_size]

                self._logger.debug(
                    f"Processing batch {i // batch_size + 1}/{(len(texts) + batch_size - 1) // batch_size}"
                )

                # Generate embeddings for batch
                batch_start = time.time()
                try:
                    response = self._client.embeddings.create(
                        model=config["model"], input=batch_texts, dimensions=config["dimensions"]
                    )

                    batch_duration = (time.time() - batch_start) * 1000
                    self._metrics.record_processing_time(
                        "openai_embedding_batch",
                        batch_duration,
                        batch_size=len(batch_texts),
                        model=config["model"],
                    )

                except Exception as e:
                    self._metrics.record_error("openai_embedding_error")
                    self._logger.error(
                        f"Failed to generate embeddings for batch {i // batch_size + 1}: {e}",
                        error=e,
                        batch_size=len(batch_texts),
                        model=config["model"],
                    )
                    raise

                # Convert to domain objects
                for embedding_obj in response.data:
                    embedding_vector = EmbeddingVector(
                        vector=embedding_obj.embedding,
                        dimensions=config["dimensions"],
                        model=config["model"],
                    )
                    all_embeddings.append(embedding_vector)

            total_duration = (time.time() - start_time) * 1000
            self._metrics.record_processing_time("openai_embedding_total", total_duration)
            self._metrics.record_success("openai_embedding_generation", count=len(all_embeddings))

            self._logger.info(
                f"Generated {len(all_embeddings)} embeddings in {total_duration:.2f}ms"
            )

            return all_embeddings

        except Exception as e:
            total_duration = (time.time() - start_time) * 1000
            self._metrics.record_processing_time("openai_embedding_failed", total_duration)
            self._logger.error(f"Error generating embeddings: {e}", error=e)
            raise

    def get_embedding_config(self) -> dict[str, Any]:
        """Get embedding configuration"""
        return {
            "model": self._config.get_openai_model(),
            "dimensions": self._config.get_openai_dimensions(),
        }


class RetryableOpenAIEmbeddingService(OpenAIEmbeddingService):
    """OpenAI embedding service with retry logic"""

    def __init__(self, config: IConfiguration, logger: ILogger, metrics: IMetricsCollector):
        super().__init__(config, logger, metrics)
        self._max_retries = config.get_retry_attempts()
        self._min_wait = config.get_retry_min_wait()
        self._max_wait = config.get_retry_max_wait()

    async def generate_embeddings(self, texts: list[str]) -> list[EmbeddingVector]:
        """Generate embeddings with retry logic"""
        last_exception = None

        for attempt in range(self._max_retries):
            try:
                return await super().generate_embeddings(texts)

            except openai.RateLimitError as e:
                last_exception = e
                wait_time = min(self._min_wait * (2**attempt), self._max_wait)

                self._logger.warning(
                    f"Rate limit hit, retrying in {wait_time}s (attempt {attempt + 1}/{self._max_retries})"
                )
                self._metrics.record_error("openai_rate_limit", attempt=attempt + 1)

                if attempt < self._max_retries - 1:
                    time.sleep(wait_time)

            except openai.APITimeoutError as e:
                last_exception = e
                wait_time = min(self._min_wait * (2**attempt), self._max_wait)

                self._logger.warning(
                    f"API timeout, retrying in {wait_time}s (attempt {attempt + 1}/{self._max_retries})"
                )
                self._metrics.record_error("openai_timeout", attempt=attempt + 1)

                if attempt < self._max_retries - 1:
                    time.sleep(wait_time)

            except openai.APIConnectionError as e:
                last_exception = e
                wait_time = min(self._min_wait * (2**attempt), self._max_wait)

                self._logger.warning(
                    f"API connection error, retrying in {wait_time}s (attempt {attempt + 1}/{self._max_retries})"
                )
                self._metrics.record_error("openai_connection_error", attempt=attempt + 1)

                if attempt < self._max_retries - 1:
                    time.sleep(wait_time)

            except (openai.AuthenticationError, openai.BadRequestError) as e:
                # Don't retry these errors
                self._metrics.record_error("openai_non_retryable_error")
                self._logger.error(f"Non-retryable OpenAI error: {e}", error=e)
                raise

            except Exception as e:
                # For other exceptions, don't retry
                self._metrics.record_error("openai_unexpected_error")
                self._logger.error(f"Unexpected error in OpenAI service: {e}", error=e)
                raise

        # If we get here, all retries failed
        self._metrics.record_error("openai_max_retries_exceeded")
        self._logger.error(f"Max retries exceeded for OpenAI embeddings: {last_exception}")
        raise last_exception
