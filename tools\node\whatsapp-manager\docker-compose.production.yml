version: '3.8'

services:
  whatsapp-manager:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      # Server Configuration
      NODE_ENV: production
      PORT: 3000
      LOG_LEVEL: info
      
      # AWS Configuration (use secrets in real deployment)
      AWS_REGION: ${AWS_REGION:-us-east-1}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      
      # DynamoDB Configuration
      DYNAMODB_TABLE_NAME: ${DYNAMODB_TABLE_NAME:-whatsapp-sessions-prod}
      
      # WhatsApp Configuration
      MAX_CONCURRENT_SESSIONS: ${MAX_CONCURRENT_SESSIONS:-100}
      QR_TOKEN_EXPIRY_SEC: ${QR_TOKEN_EXPIRY_SEC:-300}
      RECONNECT_MAX_ATTEMPTS: ${RECONNECT_MAX_ATTEMPTS:-5}
      CONNECTION_TIMEOUT_MS: ${CONNECTION_TIMEOUT_MS:-60000}
      CONNECTION_MAX_IDLE_TIME_MS: ${CONNECTION_MAX_IDLE_TIME_MS:-1800000}
      CONNECTION_MAX_AGE_MS: ${CONNECTION_MAX_AGE_MS:-86400000}
      
      # Rate Limiting Configuration
      RATE_LIMIT_MESSAGES_PER_MIN: ${RATE_LIMIT_MESSAGES_PER_MIN:-60}
      RATE_LIMIT_GLOBAL_MESSAGES_PER_MIN: ${RATE_LIMIT_GLOBAL_MESSAGES_PER_MIN:-1000}
      RATE_LIMIT_QR_GENERATION_PER_5MIN: ${RATE_LIMIT_QR_GENERATION_PER_5MIN:-5}
      RATE_LIMIT_SESSION_CREATION_PER_HOUR: ${RATE_LIMIT_SESSION_CREATION_PER_HOUR:-10}
      
      # Monitoring & Health Checks
      MONITORING_ALERTS_ENABLED: ${MONITORING_ALERTS_ENABLED:-true}
      HEALTH_CHECK_INTERVAL_MS: ${HEALTH_CHECK_INTERVAL_MS:-30000}
      METRICS_COLLECTION_INTERVAL_MS: ${METRICS_COLLECTION_INTERVAL_MS:-60000}
      ALERT_MEMORY_THRESHOLD: ${ALERT_MEMORY_THRESHOLD:-85}
      ALERT_CPU_THRESHOLD: ${ALERT_CPU_THRESHOLD:-80}
      ALERT_ERROR_RATE_THRESHOLD: ${ALERT_ERROR_RATE_THRESHOLD:-5}
      
      # Cleanup Configuration
      SESSION_CLEANUP_INTERVAL_MS: ${SESSION_CLEANUP_INTERVAL_MS:-3600000}
      EXPIRED_SESSIONS_MAX_AGE_MS: ${EXPIRED_SESSIONS_MAX_AGE_MS:-604800000}
      DISCONNECTED_SESSIONS_MAX_AGE_MS: ${DISCONNECTED_SESSIONS_MAX_AGE_MS:-86400000}
      ERROR_SESSIONS_MAX_AGE_MS: ${ERROR_SESSIONS_MAX_AGE_MS:-21600000}
      
      # Webhook Configuration
      WEBHOOK_MAX_QUEUE_SIZE: ${WEBHOOK_MAX_QUEUE_SIZE:-10000}
      WEBHOOK_DEFAULT_TIMEOUT_MS: ${WEBHOOK_DEFAULT_TIMEOUT_MS:-30000}
      WEBHOOK_PROCESSING_INTERVAL_MS: ${WEBHOOK_PROCESSING_INTERVAL_MS:-5000}
      WEBHOOK_CONCURRENCY: ${WEBHOOK_CONCURRENCY:-5}
      
    volumes:
      # Mount for persistent auth states (optional)
      - whatsapp_auth_states:/app/auth_states
      
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
      
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
          
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Optional: Redis for session state caching (if needed)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Optional: Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Optional: Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

volumes:
  whatsapp_auth_states:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    name: whatsapp-manager-network
