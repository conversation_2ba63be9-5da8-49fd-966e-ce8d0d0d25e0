# Example: Basic Network Foundation Usage
# This example shows how to use the network_foundation module in a UAT environment

terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
  }
}

provider "aws" {
  region = "ap-southeast-5"
}

# Basic network setup for UAT environment
module "network" {
  source = "../../"

  env                  = "uat"
  vpc_cidr             = "10.0.0.0/16"
  public_subnet_count  = 2
  private_subnet_count = 2

  # Cost-optimized NAT Gateway setup
  enable_nat_gateway   = true
  nat_gateway_strategy = "single"

  tags = {
    Project     = "EzyChat"
    Environment = "UAT"
    ManagedBy   = "Terraform"
    Owner       = "Platform Team"
  }

  # Additional VPC tags
  vpc_tags = {
    Name = "uat-ezychat-vpc"
  }

  # Tags for ELB integration
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
  }

  private_subnet_tags = {
    "kubernetes.io/role/internal-elb" = "1"
  }
}

# Example: Using the network outputs for ECS
module "ecs_platform" {
  source = "../../../ecs_app_platform"

  env      = "uat"
  app_name = "api"

  # Use the network foundation outputs
  vpc_id             = module.network.vpc_id
  alb_subnet_ids     = module.network.public_subnet_ids
  service_subnet_ids = module.network.private_subnet_ids

  # Other ECS configuration...
  services_config = {}

  tags = {
    Project     = "EzyChat"
    Environment = "UAT"
  }
}

# Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.network.vpc_id
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.network.public_subnet_ids
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.network.private_subnet_ids
}

output "nat_gateway_public_ips" {
  description = "Public IPs of NAT Gateways"
  value       = module.network.nat_gateway_public_ips
}
