# CI/CD Workflows

Modern GitHub Actions workflows for automated testing, building, and GitOps-based deployment of ECS services and Lambda functions using reusable workflows and intelligent change detection.

## Architecture Overview

This directory contains a **GitOps-based workflow system** that provides:

- **Modular CI/CD components** that can be shared across multiple services
- **GitOps configuration management** through automated PR creation and merging
- **Smart change detection** to skip unnecessary deployments
- **Centralized configuration** through YAML config files
- **Consistent deployment patterns** across ECS and Lambda services

## Workflow Types

### 1. Application Workflows

**Purpose**: Main CI/CD pipelines for specific services
**Patterns**:

- `app-ecs-{name}.yml` - ECS service workflows
- `app-{language}-lambda-{name}.yml` - Lambda function workflows

**Examples**:

- `app-ecs-python.yml` - ECS Python service
- `app-nodejs-lambda-sample.yml` - Node.js Lambda function

**Structure**:

- Uses reusable workflows for common tasks
- Contains project-specific configuration
- Orchestrates the complete CI/CD pipeline

### 2. Reusable Workflows

**Purpose**: Shared components that can be called by application workflows
**Pattern**: `reusable-{function}.yml`

**Available Reusable Workflows**:

- `reusable-nodejs-test-build.yml` - Node.js testing, linting, and building
- `reusable-docker-build-push.yml` - Docker image building and ECR pushing with caching
- `reusable-ecs-deploy.yml` - ECS service config update (GitOps)
- `reusable-lambda-deploy.yml` - Lambda function config update (GitOps)

## Complete CI/CD Pipeline Flow

### GitOps Deployment Architecture

```mermaid
graph TD
    A[Push to main/PR] --> B[test-build]
    B --> C[docker-build]
    C --> D[config-update]
    D --> E[terraform-deploy]
    E --> F[service-update]

    B --> B1[Language Setup]
    B1 --> B2[Install Dependencies]
    B2 --> B3[Run Tests]
    B3 --> B4[Run Linting]
    B4 --> B5[Run Build]

    C --> C1[Docker Buildx Setup]
    C1 --> C2[Layer Caching]
    C2 --> C3[Build Image]
    C3 --> C4[Push to ECR]

    D --> D1[Check Image Changed]
    D1 --> D2{Image Changed?}
    D2 -->|Yes| D3[Update Config YAML]
    D2 -->|No| D4[Skip Deployment]
    D3 --> D5[Create Auto-PR]
    D5 --> D6[Auto-Merge PR]

    E --> E1[Terraform Triggered]
    E1 --> E2[Plan Changes]
    E2 --> E3[Apply Changes]

    F --> F1[ECS Service Update]
    F --> F2[Lambda Function Update]
```

### Pipeline Stages

1. **test-build** (Automatic)

   - Runs on every push and PR
   - Uses `reusable-nodejs-test-build.yml`
   - Executes tests, linting, and building

2. **docker-build** (Automatic on main)

   - Runs after test-build succeeds
   - Uses `reusable-docker-build-push.yml`
   - Builds and pushes Docker image to ECR with caching

3. **config-update** (Automatic on main)

   - Runs after docker-build succeeds
   - Uses `reusable-ecs-deploy.yml` or `reusable-lambda-deploy.yml`
   - Updates config YAML files via GitOps
   - Creates and auto-merges PR if image changed
   - Skips if image unchanged (smart detection)

4. **terraform-deploy** (Triggered by config merge)

   - Automatically triggered when config PR is merged
   - Terraform detects config changes
   - Applies infrastructure and service updates

5. **production-deploy** (Manual via Terraform)
   - Team handles production deployment manually
   - Uses same Terraform infrastructure
   - No direct CI/CD automation for production

## Configuration Management

### GitOps Configuration Files

**Location**: `infra/iac/core/configs/`

**Service Configuration Files**:

- `ecs_services.yaml` - ECS service configurations
- `lambda_services.yaml` - Lambda function configurations

**Example ECS Service Config**:

```yaml
ecs-python-sample:
  container_image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/ecs-python-sample:19"
  environment_variables:
    PYTHON_ENV: "production"
    LOG_LEVEL: "info"
  tags:
    Service: "ECS Python Sample"
    Team: "Platform"
```

**Example Lambda Function Config**:

```yaml
sample-lambda:
  image_repository_uri: "************.dkr.ecr.ap-southeast-1.amazonaws.com/lambda-nodejs-sample:latest"
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"
  api_gateway:
    path_patterns:
      - "/sample"
      - "/sample/*"
  tags:
    Service: "Sample Lambda"
    Team: "Platform"
```

### Repository Variables (Infrastructure Settings)

**Location**: Repository Settings → Secrets and variables → Actions → Variables

These variables are **shared across all services**:

| Variable Name      | Description                    | Example Value                   |
| ------------------ | ------------------------------ | ------------------------------- |
| `AWS_REGION`       | Primary AWS region             | `ap-southeast-5`                |
| `ECR_ACCOUNT_ID`   | Central ECR account            | `************`                  |
| `UAT_ACCOUNT_ID`   | UAT environment account        | `************`                  |
| `PROD_ACCOUNT_ID`  | Production environment account | `************`                  |
| `ROLE_GITHUB_OIDC` | OIDC role name in ECR account  | `github-action-role`            |
| `ROLE_TARGET`      | Cross-account role name        | `OrganizationAccountAccessRole` |

### Project-Specific Settings (Workflow Level)

**Location**: Individual workflow YAML files

**For ECS Services**:

```yaml
env:
  SERVICE_NAME: ecs-python-sample # Must match key in ecs_services.yaml
  APP_PATH: sample/ecs-python # Path to application code
```

**For Lambda Functions**:

```yaml
# Simplified GitOps configuration
function-name: sample-lambda # Must match key in lambda_services.yaml
lambda-path: sample/lambda-nodejs # Path to Lambda code
environment-name: uat # Environment for deployment
```

### Environment Configuration

#### UAT Environment

- **AWS Account**: `${{ vars.UAT_ACCOUNT_ID }}`
- **Region**: `${{ vars.AWS_REGION }}`
- **Trigger**: Automatic on main branch push
- **Approval**: Not required
- **Authentication**: OIDC → Cross-account role assumption

#### Production Environment

- **AWS Account**: `${{ vars.PROD_ACCOUNT_ID }}`
- **Region**: `${{ vars.AWS_REGION }}`
- **Trigger**: Manual workflow dispatch only
- **Approval**: Manual trigger serves as approval
- **Authentication**: OIDC → Cross-account role assumption

## Authentication & Security

### OIDC Authentication (Recommended)

Our workflows use **OpenID Connect (OIDC)** for secure, keyless authentication to AWS:

```yaml
permissions:
  id-token: write # Required for OIDC
  contents: read # Required for checkout

steps:
  - name: Configure AWS OIDC role
    uses: aws-actions/configure-aws-credentials@v4
    with:
      role-to-assume: arn:aws:iam::${{ vars.ECR_ACCOUNT_ID }}:role/${{ vars.ROLE_GITHUB_OIDC }}
      role-session-name: GitHubActions-OIDC-${{ github.run_id }}
      aws-region: ${{ vars.AWS_REGION }}
```

### Cross-Account Role Assumption

After OIDC authentication, workflows assume target environment roles:

```bash
# Assume UAT role for UAT deployments
aws sts assume-role \
  --role-arn arn:aws:iam::${{ vars.UAT_ACCOUNT_ID }}:role/${{ vars.ROLE_TARGET }} \
  --role-session-name GitHubActions-UAT-${{ github.run_id }}

# Assume Production role for production deployments
aws sts assume-role \
  --role-arn arn:aws:iam::${{ vars.PROD_ACCOUNT_ID }}:role/${{ vars.ROLE_TARGET }} \
  --role-session-name GitHubActions-Prod-${{ github.run_id }}
```

### Required AWS IAM Setup

#### 1. OIDC Identity Provider (in ECR account)

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::************:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
          "token.actions.githubusercontent.com:sub": "repo:anchorsprint/ezychat:ref:refs/heads/main"
        }
      }
    }
  ]
}
```

#### 2. Cross-Account Roles (in UAT/Prod accounts)

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::************:role/github-action-role"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
```

## Creating New Lambda Function Workflows

### Step 1: Copy Application Workflow Template

```bash
# Copy the sample workflow
cp .github/workflows/app-nodejs-lambda-sample.yml .github/workflows/app-nodejs-lambda-myfunction.yml
```

### Step 2: Modify Project Configuration

Update the 4 project-specific values in your new workflow:

```yaml
# 🔧 PROJECT CONFIGURATION
# For new Lambda functions, modify these 4 values:

jobs:
  test-build:
    uses: ./.github/workflows/reusable-nodejs-test-build.yml
    with:
      lambda-path: my-new-lambda # 🔧 MODIFY: Path to Lambda code

  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    with:
      lambda-path: my-new-lambda # 🔧 MODIFY: Path to Lambda code
      function-name: my-new-lambda # 🔧 MODIFY: Base function name

  deploy-uat:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    with:
      function-name: uat-my-new-lambda # 🔧 MODIFY: UAT function name

  deploy-prod:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    with:
      function-name: prod-my-new-lambda # 🔧 MODIFY: Production function name
```

### Step 3: Update Workflow Triggers

```yaml
on:
  workflow_dispatch:
  push:
    branches: [main]
    paths:
      - "my-new-lambda/**" # 🔧 MODIFY: Path to your Lambda
      - ".github/workflows/app-nodejs-lambda-myfunction.yml"
  pull_request:
    branches: [main]
    paths:
      - "my-new-lambda/**" # 🔧 MODIFY: Path to your Lambda
      - ".github/workflows/app-nodejs-lambda-myfunction.yml"
```

### Complete Example: New Lambda Function Workflow

```yaml
name: App - My New Lambda CI/CD

on:
  workflow_dispatch:
  push:
    branches: [main]
    paths:
      - "my-new-lambda/**"
      - ".github/workflows/app-nodejs-lambda-mynewlambda.yml"
  pull_request:
    branches: [main]
    paths:
      - "my-new-lambda/**"
      - ".github/workflows/app-nodejs-lambda-mynewlambda.yml"

jobs:
  test-build:
    uses: ./.github/workflows/reusable-nodejs-test-build.yml
    with:
      lambda-path: my-new-lambda
      node-version: "18"
      run-tests: true
      run-lint: true
      run-build: true

  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    needs: test-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      lambda-path: my-new-lambda
      function-name: my-new-lambda
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}

  deploy-uat:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: uat-my-new-lambda
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.UAT_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: UAT
      test-payload: '{"path":"/my-endpoint","httpMethod":"GET"}'

  deploy-prod:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    needs: [docker-build, deploy-uat]
    if: github.ref == 'refs/heads/main' && github.event_name == 'workflow_dispatch'
    permissions:
      id-token: write
      contents: read
    with:
      function-name: prod-my-new-lambda
      image-uri: ${{ needs.docker-build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }}
      target-account-id: ${{ vars.PROD_ACCOUNT_ID }}
      github-oidc-role: ${{ vars.ROLE_GITHUB_OIDC }}
      target-role: ${{ vars.ROLE_TARGET }}
      environment-name: Production
      test-payload: '{"path":"/my-endpoint","httpMethod":"GET"}'
```

## Reusable Workflow Details

### 1. reusable-nodejs-test-build.yml

**Purpose**: Node.js testing, linting, and building

**Inputs**:

- `lambda-path` (required): Path to Lambda function directory
- `node-version` (optional): Node.js version (default: '18')
- `run-tests` (optional): Whether to run tests (default: true)
- `run-lint` (optional): Whether to run linting (default: true)
- `run-build` (optional): Whether to run build (default: true)

**Features**:

- Smart detection of package.json scripts
- Conditional execution based on file existence
- Detailed GitHub step summary

### 2. reusable-docker-build-push.yml

**Purpose**: Docker image building and ECR pushing

**Inputs**:

- `lambda-path` (required): Path to Lambda function directory
- `function-name` (required): Base function name for Docker image
- `aws-region` (required): AWS region
- `ecr-account-id` (required): ECR account ID
- `uat-account-id` (required): UAT account ID for role assumption
- `github-oidc-role` (required): OIDC role name
- `target-role` (required): Cross-account role name

**Outputs**:

- `image-uri`: Full ECR image URI for deployment

**Features**:

- OIDC authentication with cross-account role assumption
- Automatic ECR repository creation
- Docker image building and pushing
- Image URI output for downstream jobs

### 3. reusable-ecs-deploy.yml

**Purpose**: ECS service configuration update via GitOps

**Inputs**:

- `service-name` (required): ECS service name (key in ecs_services.yaml)
- `image-uri` (required): Docker image URI to update in config
- `environment-name` (required): Environment name (uat/prod) for branch naming
- `config-path` (optional): Path to ecs_services.yaml

**Outputs**:

- `pr-number`: Pull request number created
- `pr-url`: Pull request URL
- `deployment-status`: "deployed" or "skipped"
- `branch-name`: Auto-generated branch name
- `image-changed`: Whether image was changed

**Features**:

- Smart image change detection (skip if unchanged)
- GitOps configuration update via YAML modification
- Auto-PR creation and merge
- Rich deployment summaries with clickable links
- No AWS credentials required

### 4. reusable-lambda-deploy.yml

**Purpose**: Lambda function configuration update via GitOps

**Inputs**:

- `function-name` (required): Lambda function name (key in lambda_services.yaml)
- `image-uri` (required): Docker image URI to update in config
- `environment-name` (required): Environment name (uat/prod) for branch naming
- `config-path` (optional): Path to lambda_services.yaml

**Outputs**:

- `pr-number`: Pull request number created
- `pr-url`: Pull request URL
- `deployment-status`: "deployed" or "skipped"
- `branch-name`: Auto-generated branch name
- `image-changed`: Whether image was changed

**Features**:

- Smart image change detection (skip if unchanged)
- GitOps configuration update via YAML modification
- Auto-PR creation and merge
- Rich deployment summaries with clickable links
- No AWS credentials required

## Best Practices

### Security

- ✅ **Use OIDC authentication** instead of long-lived AWS keys
- ✅ **Cross-account role assumption** for environment isolation
- ✅ **Minimal permissions** - each role has only required permissions
- ✅ **Repository variables** for non-sensitive configuration
- ✅ **Manual approval** for production deployments (workflow_dispatch)

### Reusability

- ✅ **Modular workflows** - separate concerns into reusable components
- ✅ **Centralized configuration** - infrastructure settings in repository variables
- ✅ **Clear documentation** - comments showing what to modify for new functions
- ✅ **Consistent patterns** - same structure across all Lambda functions

### Performance

- ✅ **Parallel execution** where possible (test-build runs independently)
- ✅ **Conditional execution** - deployments only on main branch
- ✅ **Smart caching** - Docker layer caching in ECR
- ✅ **Efficient builds** - only rebuild when code changes

### Monitoring & Debugging

- ✅ **GitHub step summaries** - detailed execution reports
- ✅ **Structured logging** - clear step names and outputs
- ✅ **Error handling** - graceful failure with informative messages
- ✅ **Workflow status** - easy to see which step failed

### Maintenance

- ✅ **Version pinning** - use specific action versions (@v4)
- ✅ **Clear naming** - descriptive workflow and job names
- ✅ **Documentation** - comprehensive README with examples
- ✅ **Testing** - validate workflows in feature branches

## Troubleshooting

### Common Issues

#### 1. Permission Errors

```
Error: User: arn:aws:sts::************:assumed-role/github-action-role/GitHubActions-OIDC-123 is not authorized to perform: lambda:UpdateFunctionCode
```

**Solution**: Check IAM policies for the target role in the deployment account.

#### 2. OIDC Authentication Failures

```
Error: Could not assume role with OIDC: Access denied
```

**Solution**:

- Verify OIDC identity provider is configured in ECR account
- Check trust policy allows your repository and branch
- Ensure `id-token: write` permission is set

#### 3. ECR Repository Not Found

```
Error: Repository does not exist
```

**Solution**: The workflow automatically creates ECR repositories, but check:

- ECR account ID is correct in repository variables
- Cross-account permissions allow ECR operations

#### 4. Lambda Function Not Found

```
Error: Function not found: arn:aws:lambda:region:account:function:function-name
```

**Solution**: The workflow creates functions if they don't exist, but verify:

- Function name matches exactly
- Target account ID is correct
- Lambda execution role exists

#### 5. Environment Variable Access

```
Error: Unrecognized named-value: 'env'
```

**Solution**: Environment variables (`env`) cannot be used in reusable workflow calls. Use literal values or repository variables (`vars`).

### Debugging Commands

```bash
# List recent workflow runs
gh run list --limit 10

# View specific workflow run
gh run view <run-id>

# View logs for failed jobs only
gh run view <run-id> --log-failed

# Re-run failed jobs
gh run rerun <run-id> --failed

# Cancel running workflow
gh run cancel <run-id>

# Watch workflow in real-time
gh run watch <run-id>
```

### Monitoring Workflow Health

#### GitHub Actions Dashboard

- Repository → Actions tab
- Filter by workflow name
- Check success/failure rates
- Monitor execution times

#### AWS CloudWatch Integration

```yaml
- name: Send metrics to CloudWatch
  run: |
    aws cloudwatch put-metric-data \
      --namespace "GitHub/Actions" \
      --metric-data MetricName=DeploymentSuccess,Value=1,Unit=Count
```

## Migration Guide

### From Traditional Workflows to Reusable Workflows

#### Before (Traditional)

```yaml
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      # ... 50+ lines of deployment logic
```

#### After (Reusable)

```yaml
jobs:
  deploy:
    uses: ./.github/workflows/reusable-lambda-deploy.yml
    permissions:
      id-token: write
      contents: read
    with:
      function-name: my-function
      image-uri: ${{ needs.build.outputs.image-uri }}
      aws-region: ${{ vars.AWS_REGION }}
      # ... clean configuration
```

### Benefits of Migration

- **90% less code** in application workflows
- **Consistent deployment patterns** across all functions
- **Centralized maintenance** - fix once, applies everywhere
- **Better security** with OIDC authentication
- **Easier testing** of individual components

## Contributing

### Workflow Development Guidelines

1. **Feature Branch Development**

   ```bash
   git checkout -b feature/improve-nodejs-workflow
   # Make changes to reusable workflows
   # Test with a sample application workflow
   ```

2. **Testing Changes**

   - Create a test application workflow
   - Trigger workflow with `workflow_dispatch`
   - Verify all stages complete successfully
   - Check AWS resources are created correctly

3. **Pull Request Process**

   - Detailed description of changes
   - Include before/after examples
   - Update documentation if needed
   - Peer review required for reusable workflows

4. **Deployment Process**
   - Merge to main branch
   - Monitor first few workflow runs
   - Update dependent application workflows if needed

### Adding New Reusable Workflows

1. **Create workflow file**: `reusable-{purpose}.yml`
2. **Define inputs and outputs** clearly
3. **Add comprehensive error handling**
4. **Include step summaries** for visibility
5. **Document in README** with examples
6. **Test with multiple scenarios**

### Modifying Existing Workflows

1. **Maintain backward compatibility** when possible
2. **Version inputs** if breaking changes needed
3. **Update all dependent workflows**
4. **Test thoroughly** before merging
5. **Communicate changes** to team
