{"name": "@ezychat/whatsapp-manager", "version": "1.0.0", "description": "WhatsApp API Middleware Foundation - Production-ready WhatsApp Business API integration", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:dev": "npm run dev", "start:prod": "NODE_ENV=production npm run start", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "setup:local": "ts-node scripts/setup-local-db.ts", "docker:build": "docker build -f docker/Dockerfile -t whatsapp-manager .", "docker:dev": "docker-compose up --build", "docker:test": "docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit", "deploy": "bash deploy.sh", "deploy:tag": "bash deploy.sh", "deploy:compose": "docker-compose -f docker-compose.deploy.yml --profile build up --build", "typecheck": "tsc --noEmit", "clean": "rimraf dist coverage .nyc_output"}, "keywords": ["whatsapp", "api", "middleware", "baileys", "typescript", "express", "websocket", "qr-code", "business-api"], "author": "ezychat team", "license": "MIT", "dependencies": {"@aws-sdk/client-dynamodb": "^3.857.0", "@aws-sdk/client-secrets-manager": "^3.398.0", "@aws-sdk/lib-dynamodb": "^3.857.0", "@whiskeysockets/baileys": "^6.7.18", "axios": "^1.11.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv-flow": "^4.0.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validation": "^4.1.1", "express-validator": "^7.2.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.10", "morgan": "^1.10.0", "pino": "^8.15.0", "pino-pretty": "^10.2.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.13", "tsyringe": "^4.8.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^20.5.9", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "eslint": "^8.48.0", "jest": "^29.6.4", "prettier": "^3.0.3", "rimraf": "^5.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}