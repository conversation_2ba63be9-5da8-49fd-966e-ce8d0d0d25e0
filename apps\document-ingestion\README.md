# Document Ingestion Pipeline - Clean Architecture

[![CI/CD Pipeline](https://github.com/anchorsprint/ezychat/actions/workflows/ci.yml/badge.svg)](https://github.com/anchorsprint/ezychat/actions/workflows/ci.yml)
[![Code Coverage](https://codecov.io/gh/anchorsprint/ezychat/branch/main/graph/badge.svg)](https://codecov.io/gh/anchorsprint/ezychat)
[![Python 3.12](https://img.shields.io/badge/python-3.12-blue.svg)](https://www.python.org/downloads/release/python-3120/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Imports: isort](https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat&labelColor=ef8336)](https://pycqa.github.io/isort/)

A **production-ready** multi-tenant CSV document ingestion pipeline for the EzyChat RAG-powered sales assistant, built with **Clean Architecture** principles, **dependency injection**, and comprehensive **testing infrastructure**.

## 🏗️ Architecture Overview

This project follows **Clean Architecture** patterns with proper separation of concerns and **dependency injection** using the `dependency-injector` library:

```
src/
├── domain/                  # Business entities and rules (innermost layer)
│   ├── entities.py         # Document, ProductData, EmbeddingVector
│   ├── interfaces.py       # Repository and service interfaces (ports)
│   └── services.py         # Domain services (business logic)
├── application/             # Use cases and orchestration
│   └── use_cases.py        # ProcessDocumentUseCase (application logic)
├── infrastructure/          # External dependencies (outermost layer)
│   ├── configuration.py    # Environment configuration
│   ├── logging.py          # Structured logging
│   ├── metrics.py          # Metrics collection
│   ├── repositories.py     # Supabase implementations (adapters)
│   ├── storage.py          # S3 implementations (adapters)
│   ├── services.py         # OpenAI implementations (adapters)
│   ├── processors.py       # CSV processing (adapters)
│   ├── events.py           # Event publishing (adapters)
│   └── dependency_injection.py  # DI container
└── presentation/            # Controllers and handlers
    └── lambda_handler.py   # Lambda entry point

tests/
├── unit/                   # Fast, isolated unit tests
│   ├── domain/            # Domain layer tests
│   ├── application/       # Application layer tests
│   └── infrastructure/    # Infrastructure layer tests
├── integration/           # Integration tests with real dependencies
├── fixtures/              # Test data and fixtures
└── conftest.py           # Pytest configuration and shared fixtures
```

## 🔧 Technology Stack

- **Runtime:** Python 3.12 (latest stable)
- **Architecture:** Clean Architecture with SOLID principles
- **DI Framework:** [dependency-injector](https://pypi.org/project/dependency-injector/) 4.41+
- **Vector Store:** Supabase pgvector (migrated from Qdrant)
- **Embeddings:** OpenAI text-embedding-3-small (configurable)
- **Storage:** AWS S3, Supabase PostgreSQL
- **Processing:** Pandas for CSV handling
- **Testing:** pytest with comprehensive fixtures
- **Code Quality:** black, isort, flake8, mypy, bandit
- **CI/CD:** GitHub Actions with automated testing and deployment
- **Containerization:** Docker with AWS Lambda base image

## 🚀 Key Features

### Clean Architecture Benefits
- ✅ **Separation of Concerns:** Clear boundaries between layers
- ✅ **Dependency Inversion:** Business logic doesn't depend on infrastructure
- ✅ **Testability:** Easy to unit test with mocked dependencies
- ✅ **Maintainability:** Changes in one layer don't affect others
- ✅ **Extensibility:** New features can be added without breaking existing code

### Dependency Injection Benefits
- ✅ **Loose Coupling:** Components depend on abstractions, not concretions
- ✅ **Configuration Management:** Centralized dependency configuration
- ✅ **Singleton Management:** Automatic lifecycle management
- ✅ **Testing Support:** Easy to inject mocks for testing
- ✅ **Runtime Flexibility:** Can swap implementations without code changes

### Business Features
- ✅ **Multi-tenant isolation** with user_id-based partitioning
- ✅ **Comprehensive error handling** and retry mechanisms
- ✅ **Structured logging** with CloudWatch integration
- ✅ **Configurable embeddings** (model and dimensions)
- ✅ **Batch processing** for OpenAI API efficiency
- ✅ **CSV validation and cleaning** with data quality scoring
- ✅ **S3 file lifecycle management** (uploaded → processed/failed)

## 📋 Prerequisites

- **Python 3.12+** (required for latest features and performance)
- **AWS CLI** configured with appropriate permissions
- **OpenAI API key** with embeddings access
- **Supabase project** with pgvector extension enabled
- **Git** for version control
- **Make** (optional, for convenience commands)

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/anchorsprint/ezychat.git
cd ezychat/apps/document-ingestion

# Run automated setup script
# Linux/Mac:
./scripts/setup-dev.sh

# Windows PowerShell:
.\scripts\setup-dev.ps1
```

### Option 2: Manual Setup

1. **Create virtual environment:**
   ```bash
   python -m venv .venv

   # Activate virtual environment
   # Linux/Mac:
   source .venv/bin/activate
   # Windows:
   .venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -e ".[dev]"
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

4. **Install pre-commit hooks:**
   ```bash
   pre-commit install
   ```

5. **Run tests to verify setup:**
   ```bash
   make test-unit
   # or
   pytest tests/unit -v
   ```

## 🔧 Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and configure your values.

### Required Configuration

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `OPENAI_API_KEY` | Your OpenAI API key for generating embeddings | `sk-proj-abc123...` | Get from [OpenAI Platform](https://platform.openai.com/api-keys) |
| `SUPABASE_URL` | Your Supabase project URL | `https://your-project-id.supabase.co` | From Supabase project settings |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key (bypasses RLS) | `eyJ...` | Use for Lambda functions to bypass Row Level Security |

### Optional Configuration

#### Environment Settings
| Variable | Default | Description | Options |
|----------|---------|-------------|---------|
| `ENVIRONMENT` | `dev` | Deployment environment | `dev`, `development`, `local`, `staging`, `prod`, `production` |
| `SERVICE_VERSION` | `1.0.0` | Version of the service for logging and monitoring | Any semantic version |
| `LOG_LEVEL` | `INFO` | Logging level | `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL` |

#### OpenAI Configuration
| Variable | Default | Description | Options/Range |
|----------|---------|-------------|---------------|
| `OPENAI_EMBEDDING_MODEL` | `text-embedding-3-small` | OpenAI embedding model to use | `text-embedding-3-small`, `text-embedding-3-large`, `text-embedding-ada-002` |
| `OPENAI_EMBEDDING_DIMENSIONS` | `512` | Number of dimensions for embeddings | 256-3072 (for text-embedding-3-* models) |

#### Processing Limits
| Variable | Default | Description | Notes |
|----------|---------|-------------|-------|
| `MAX_FILE_SIZE_BYTES` | `52428800` (50MB) | Maximum allowed file size in bytes | AWS Lambda has payload limits |
| `MAX_ROWS` | `100000` | Maximum number of rows allowed in CSV files | Higher values may cause Lambda timeouts |
| `BATCH_SIZE` | `100` | General batch size for processing operations | - |

#### Retry Configuration
| Variable | Default | Description | Range |
|----------|---------|-------------|-------|
| `RETRY_ATTEMPTS` | `3` | Number of retry attempts for failed operations | - |
| `RETRY_MIN_WAIT` | `1` | Minimum wait time between retries (seconds) | - |
| `RETRY_MAX_WAIT` | `60` | Maximum wait time between retries (seconds) | - |

#### Batch Processing
| Variable | Default | Description | Notes |
|----------|---------|-------------|-------|
| `EMBEDDING_BATCH_SIZE` | `100` | Batch size for OpenAI embedding API calls | OpenAI allows up to 2048 inputs per request |
| `SUPABASE_BATCH_SIZE` | `100` | Batch size for Supabase insert operations | Larger batches may hit payload limits |

#### Vector Search Configuration
| Variable | Default | Description | Range |
|----------|---------|-------------|-------|
| `SIMILARITY_THRESHOLD` | `0.7` | Default similarity threshold for vector searches | 0.0-1.0 |
| `MAX_SEARCH_RESULTS` | `10` | Maximum number of results to return from vector searches | - |

#### AWS Configuration
| Variable | Default | Description | Notes |
|----------|---------|-------------|-------|
| `AWS_REGION` | `us-east-1` | AWS region for S3 and other AWS services | Auto-detected in Lambda |
| `DOCUMENT_BUCKET_NAME` | - | S3 bucket name for document storage | Automatically configured by Terraform deployment |

#### Dependency Injection
| Variable | Default | Description | Notes |
|----------|---------|-------------|-------|
| `DI_AUTO_WIRE` | `true` | Enable automatic dependency injection wiring | Uses dependency-injector library |
| `DI_STRICT_MODE` | `false` | Enable strict mode for dependency injection | Validates all dependencies at startup |

### Environment-Specific Configurations

#### Development Environment
```bash
ENVIRONMENT=dev
LOG_LEVEL=DEBUG
MAX_ROWS=1000
```

#### Staging Environment
```bash
ENVIRONMENT=staging
LOG_LEVEL=INFO
MAX_ROWS=10000
```

#### Production Environment
```bash
ENVIRONMENT=production
LOG_LEVEL=WARNING
MAX_ROWS=100000
```

### Security Notes

1. **Never commit actual API keys or secrets to version control**
2. **Use AWS Secrets Manager or Parameter Store for production deployments**
3. **Rotate API keys regularly**
4. **Use least privilege principle for Supabase keys**
5. **Monitor API usage and set up billing alerts**

## 🏃‍♂️ Usage

### Lambda Deployment
The main entry point is `src/presentation/lambda_handler.py` which implements the clean architecture handler:

```python
from src.presentation.lambda_handler import lambda_handler as clean_lambda_handler

def lambda_handler(event, context):
    return clean_lambda_handler(event, context)
```

### Local Testing
```python
from src.infrastructure.dependency_injection import ApplicationContainer
from src.application.use_cases import ProcessDocumentCommand

# Initialize container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Get use case
use_case = container.process_document_use_case()

# Execute
command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
result = await use_case.execute(command)
```

## 🧪 Testing

The project includes comprehensive testing infrastructure with **unit tests**, **integration tests**, and **fixtures**.

### Test Structure
```
tests/
├── unit/                   # Fast, isolated tests (no external dependencies)
│   ├── domain/            # Domain layer tests
│   ├── application/       # Application layer tests
│   └── infrastructure/    # Infrastructure layer tests
├── integration/           # Integration tests (with real services)
├── fixtures/              # Test data and sample files
└── conftest.py           # Shared fixtures and configuration
```

### Running Tests

```bash
# Run all tests
make test

# Run only unit tests (fast)
make test-unit

# Run only integration tests
make test-integration

# Run tests with coverage
make test-cov

# Run tests in watch mode
make test-watch

# Run specific test file
pytest tests/unit/domain/test_entities.py -v

# Run tests with specific markers
pytest -m "unit" -v
pytest -m "integration" -v
```

### Test Categories

- **Unit Tests** (`@pytest.mark.unit`): Fast, isolated tests with mocked dependencies
- **Integration Tests** (`@pytest.mark.integration`): Tests with real external services
- **Slow Tests** (`@pytest.mark.slow`): Performance and load tests

### Writing Tests

The dependency injection container makes testing easy:

```python
def test_process_document_use_case(mocked_container):
    """Test use case with all dependencies mocked"""
    use_case = mocked_container.process_document_use_case()

    # All dependencies are automatically mocked
    result = await use_case.execute(command)

    assert result.success is True
```

## 📊 Monitoring

### Structured Logging
All logs are structured JSON for CloudWatch:
```json
{
  "timestamp": "2025-08-07T10:30:00Z",
  "level": "INFO",
  "message": "Document processing completed",
  "user_id": "user123",
  "document_id": "doc456",
  "processed_rows": 1000,
  "duration_ms": 45000
}
```

### Metrics Collection
- Processing times
- Success/failure rates
- Error categorization
- Resource utilization

## 👨‍💻 Development Workflow

### Code Quality Standards

This project enforces high code quality standards with automated tools:

```bash
# Format code
make format

# Check formatting
make format-check

# Run all linting
make lint

# Type checking
make type-check

# Security scanning
make security

# Run pre-commit hooks
make pre-commit
```

### Pre-commit Hooks

The project uses pre-commit hooks to ensure code quality:

- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting and style checking
- **mypy**: Type checking
- **bandit**: Security scanning
- **pytest**: Run unit tests

### Development Commands

```bash
# Show all available commands
make help

# Setup development environment
make setup-dev

# Run tests with coverage
make test-cov

# Build Lambda deployment package
make build

# Build Docker image
make docker-build

# Clean build artifacts
make clean
```

### CI/CD Pipeline

The project includes a comprehensive CI/CD pipeline with GitHub Actions:

- **Automated Testing**: Unit and integration tests
- **Code Quality**: Linting, formatting, type checking
- **Security Scanning**: Dependency and code security checks
- **Build**: Lambda deployment package creation
- **Deploy**: Automated deployment to AWS Lambda

## 🔄 Data Flow

1. **S3 Upload:** CSV uploaded to `{user_id}/uploaded/{filename}.csv`
2. **Lambda Trigger:** S3 event triggers Lambda function
3. **Dependency Injection:** Container wires all dependencies
4. **Use Case Execution:** ProcessDocumentUseCase orchestrates the flow
5. **CSV Processing:** Parse, validate, and clean data
6. **Embedding Generation:** OpenAI generates vector embeddings
7. **Storage:** Save to Supabase with pgvector
8. **File Management:** Move to processed/failed folder
9. **Event Publishing:** Publish completion events

## 🛡️ Security

- **Multi-tenant isolation:** Complete data separation by user_id
- **Input validation:** CSV schema and content validation
- **Error handling:** Comprehensive error classification
- **Audit logging:** All operations logged with context
- **Secret management:** Environment variables for sensitive data

## 🚀 Deployment

### Docker Build
```bash
docker build -t document-ingestion .
```

### AWS Lambda
```bash
# Package for Lambda
zip -r function.zip src/ requirements.txt

# Deploy with AWS CLI
aws lambda update-function-code \
  --function-name document-ingestion \
  --zip-file fileb://function.zip
```

## 🔧 Troubleshooting

### Common Configuration Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **OpenAI API key not found** | Authentication errors, embedding failures | Check `OPENAI_API_KEY` is set correctly in `.env` |
| **Supabase connection failed** | Database connection errors | Verify `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` are correct |
| **File too large** | Upload rejected, processing fails | Adjust `MAX_FILE_SIZE_BYTES` or split large files |
| **Lambda timeout** | Function times out during processing | Reduce `MAX_ROWS` or increase Lambda timeout setting |
| **Rate limit exceeded** | OpenAI API rate limiting | Reduce `EMBEDDING_BATCH_SIZE` or add delays between requests |
| **Dependency injection failed** | Container initialization errors | Run `python src/infrastructure/test_di.py` to diagnose DI issues |
| **Memory issues** | Out of memory errors | Reduce batch sizes (`BATCH_SIZE`, `EMBEDDING_BATCH_SIZE`, `SUPABASE_BATCH_SIZE`) |
| **Vector search poor results** | Low quality search results | Adjust `SIMILARITY_THRESHOLD` (lower = more results, higher = more precise) |

### Environment-Specific Issues

| Environment | Common Issues | Recommended Settings |
|-------------|---------------|---------------------|
| **Development** | Slow processing, debugging needs | `LOG_LEVEL=DEBUG`, `MAX_ROWS=1000` |
| **Staging** | Performance testing | `LOG_LEVEL=INFO`, `MAX_ROWS=10000` |
| **Production** | High volume processing | `LOG_LEVEL=WARNING`, `MAX_ROWS=100000` |

### Performance Tuning

| Performance Issue | Configuration Adjustment |
|-------------------|-------------------------|
| **Slow embedding generation** | Increase `EMBEDDING_BATCH_SIZE` (max 2048) |
| **Slow database inserts** | Increase `SUPABASE_BATCH_SIZE` (watch payload limits) |
| **High memory usage** | Decrease `BATCH_SIZE` and related batch sizes |
| **API rate limiting** | Decrease batch sizes, add retry delays |

## 📚 Further Reading

- [Clean Architecture by Robert Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [dependency-injector Documentation](https://python-dependency-injector.ets-labs.org/)
- [Supabase pgvector Guide](https://supabase.com/docs/guides/database/extensions/pgvector)
- [OpenAI Embeddings API](https://platform.openai.com/docs/guides/embeddings)

## 🤝 Contributing

1. Follow clean architecture principles
2. Add tests for new features
3. Update documentation
4. Use dependency injection for new components
5. Maintain separation of concerns
