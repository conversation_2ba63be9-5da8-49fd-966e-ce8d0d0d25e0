import { Router } from 'express';
import { createHealthRoutes } from './health.routes';
import { createSessionRoutes } from './session.routes';
import { createAuthRoutes } from './auth';

/**
 * Main API routes configuration
 */
export function createApiRoutes(): Router {
  const router = Router();

  // Health check routes
  router.use('/health', createHealthRoutes());

  // Session management routes
  router.use('/sessions', createSessionRoutes());

  // Authentication routes
  router.use('/auth', createAuthRoutes());

  // Future routes will be added here:
  // router.use('/messages', createMessageRoutes());

  return router;
}
