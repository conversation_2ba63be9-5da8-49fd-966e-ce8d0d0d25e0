/**
 * DynamoDB item structure for auth token storage
 */
export interface AuthTokenDynamoDBItem {
  PK: string;           // AUTH_TOKEN#{tokenId}
  SK: string;           // USER#{userId}
  GSI1PK: string;       // USER#{userId}
  GSI1SK: string;       // TOKEN#{createdAt}
  tokenId: string;
  userId: string;
  token: string;
  expiresAt: string;    // ISO string
  createdAt: string;    // ISO string
  isUsed: boolean;
  usedAt?: string;      // ISO string
  metadata?: string;    // JSON serialized metadata
  ttl: number;          // Unix timestamp for automatic cleanup
}

/**
 * DynamoDB table definition for auth token storage
 */
export const AUTH_TOKEN_TABLE_DEFINITION = {
  TableName: '${AUTH_TOKEN_TABLE_NAME}', // Will be replaced by actual table name
  KeySchema: [
    { AttributeName: 'PK', KeyType: 'HASH' },
    { AttributeName: 'SK', KeyType: 'RANGE' }
  ],
  AttributeDefinitions: [
    { AttributeName: 'PK', AttributeType: 'S' },
    { AttributeName: 'SK', AttributeType: 'S' },
    { AttributeName: 'GSI1PK', AttributeType: 'S' },
    { AttributeName: 'GSI1SK', AttributeType: 'S' }
  ],
  GlobalSecondaryIndexes: [{
    IndexName: 'GSI1',
    KeySchema: [
      { AttributeName: 'GSI1PK', KeyType: 'HASH' },
      { AttributeName: 'GSI1SK', KeyType: 'RANGE' }
    ],
    Projection: { ProjectionType: 'ALL' }
  }],
  BillingMode: 'PAY_PER_REQUEST',
  TimeToLiveSpecification: {
    AttributeName: 'ttl',
    Enabled: true
  },
  StreamSpecification: {
    StreamEnabled: false
  },
  SSESpecification: {
    SSEEnabled: true
  },
  PointInTimeRecoverySpecification: {
    PointInTimeRecoveryEnabled: false
  },
  Tags: [
    { Key: 'Environment', Value: 'development' },
    { Key: 'Service', Value: 'whatsapp-manager' },
    { Key: 'Component', Value: 'auth-tokens' }
  ]
} as const;

/**
 * DynamoDB key patterns for auth token operations
 */
export const AUTH_TOKEN_KEY_PATTERNS = {
  /**
   * Primary key for auth token items
   */
  tokenPK: (tokenId: string) => `AUTH_TOKEN#${tokenId}`,
  tokenSK: (userId: string) => `USER#${userId}`,

  /**
   * GSI1 key for user-based queries
   */
  userGSI1PK: (userId: string) => `USER#${userId}`,
  userGSI1SK: (createdAt: Date) => `TOKEN#${createdAt.toISOString()}`,

  /**
   * Parse keys back to components
   */
  parseTokenPK: (pk: string) => {
    const match = pk.match(/^AUTH_TOKEN#(.+)$/);
    return match ? match[1] : null;
  },

  parseTokenSK: (sk: string) => {
    const match = sk.match(/^USER#(.+)$/);
    return match ? match[1] : null;
  },

  parseUserGSI1PK: (gsi1pk: string) => {
    const match = gsi1pk.match(/^USER#(.+)$/);
    return match ? match[1] : null;
  },

  parseUserGSI1SK: (gsi1sk: string) => {
    const match = gsi1sk.match(/^TOKEN#(.+)$/);
    return match ? new Date(match[1]) : null;
  }
} as const;

/**
 * DynamoDB condition expressions for auth token operations
 */
export const AUTH_TOKEN_CONDITIONS = {
  /**
   * Condition to ensure item doesn't exist (for create operations)
   */
  ITEM_NOT_EXISTS: 'attribute_not_exists(PK)',

  /**
   * Condition to ensure item exists (for update operations)
   */
  ITEM_EXISTS: 'attribute_exists(PK)',

  /**
   * Condition to check if token is not used
   */
  TOKEN_NOT_USED: '#isUsed = :false',

  /**
   * Condition to check TTL not expired
   */
  TTL_NOT_EXPIRED: 'attribute_not_exists(#ttl) OR #ttl > :currentTime',

  /**
   * Condition to check token is active (not used and not expired)
   */
  TOKEN_ACTIVE: '#isUsed = :false AND (#ttl > :currentTime)',

  /**
   * Condition to check token belongs to user
   */
  TOKEN_BELONGS_TO_USER: 'begins_with(SK, :userPrefix)',

  /**
   * Condition for auth token prefix
   */
  AUTH_TOKEN_PREFIX: 'begins_with(PK, :tokenPrefix)'
} as const;

/**
 * DynamoDB expression attribute names for auth token operations
 */
export const AUTH_TOKEN_EXPRESSION_NAMES = {
  '#isUsed': 'isUsed',
  '#ttl': 'ttl',
  '#token': 'token',
  '#userId': 'userId',
  '#tokenId': 'tokenId',
  '#expiresAt': 'expiresAt',
  '#createdAt': 'createdAt',
  '#usedAt': 'usedAt',
  '#metadata': 'metadata'
} as const;

/**
 * Helper functions for auth token DynamoDB operations
 */
export const AUTH_TOKEN_HELPERS = {
  /**
   * Generate current Unix timestamp
   */
  getCurrentUnixTimestamp: () => Math.floor(Date.now() / 1000),

  /**
   * Convert Date to Unix timestamp
   */
  dateToUnixTimestamp: (date: Date) => Math.floor(date.getTime() / 1000),

  /**
   * Check if token is expired based on TTL
   */
  isTokenExpired: (ttl: number) => {
    const now = Math.floor(Date.now() / 1000);
    return ttl <= now;
  },

  /**
   * Generate TTL for token expiry
   */
  generateTTL: (expirySeconds: number) => {
    return Math.floor(Date.now() / 1000) + expirySeconds;
  },

  /**
   * Validate token ID format (supports both UUID and qr_timestamp_random formats)
   */
  isValidTokenId: (tokenId: string) => {
    // UUID format (from AuthToken constructor)
    const uuidRegex = /^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/i;

    // QR token format (from AuthService.generateTokenId)
    const qrTokenRegex = /^qr_[a-z0-9]+_[a-z0-9]+$/i;

    return uuidRegex.test(tokenId) || qrTokenRegex.test(tokenId);
  },

  /**
   * Validate user ID format
   */
  isValidUserId: (userId: string) => {
    return userId && userId.trim().length > 0 && userId.length <= 255;
  }
} as const;

/**
 * DynamoDB query patterns for common auth token operations
 */
export const AUTH_TOKEN_QUERY_PATTERNS = {
  /**
   * Query pattern to find token by ID
   */
  findByTokenId: (tokenId: string) => ({
    KeyConditionExpression: 'PK = :pk',
    ExpressionAttributeValues: {
      ':pk': AUTH_TOKEN_KEY_PATTERNS.tokenPK(tokenId)
    }
  }),

  /**
   * Query pattern to find tokens by user ID
   */
  findByUserId: (userId: string) => ({
    IndexName: 'GSI1',
    KeyConditionExpression: 'GSI1PK = :gsi1pk',
    ExpressionAttributeValues: {
      ':gsi1pk': AUTH_TOKEN_KEY_PATTERNS.userGSI1PK(userId)
    },
    ScanIndexForward: false // Most recent first
  }),

  /**
   * Query pattern to find active tokens by user ID
   */
  findActiveTokensByUserId: (userId: string) => ({
    IndexName: 'GSI1',
    KeyConditionExpression: 'GSI1PK = :gsi1pk',
    FilterExpression: AUTH_TOKEN_CONDITIONS.TOKEN_ACTIVE,
    ExpressionAttributeNames: AUTH_TOKEN_EXPRESSION_NAMES,
    ExpressionAttributeValues: {
      ':gsi1pk': AUTH_TOKEN_KEY_PATTERNS.userGSI1PK(userId),
      ':false': false,
      ':currentTime': AUTH_TOKEN_HELPERS.getCurrentUnixTimestamp()
    },
    ScanIndexForward: false
  }),

  /**
   * Scan pattern to find token by token string
   */
  findByTokenString: (tokenString: string) => ({
    FilterExpression: '#token = :token AND begins_with(PK, :pkPrefix)',
    ExpressionAttributeNames: {
      '#token': 'token'
    },
    ExpressionAttributeValues: {
      ':token': tokenString,
      ':pkPrefix': 'AUTH_TOKEN#'
    },
    Limit: 1
  })
} as const;
