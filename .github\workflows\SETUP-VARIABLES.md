# GitHub Actions Variables Setup Guide

This guide explains how to set up GitHub repository variables to eliminate hardcoded values in workflows.

## 🎯 Why Use Variables?

- **Eliminate duplication**: Set common values once, use everywhere
- **Easy maintenance**: Update AWS accounts, regions in one place
- **Reusability**: Same variables work across multiple Lambda functions
- **Security**: Centralized configuration management

## 📋 Required Repository Variables

Go to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** → **Variables** tab

### **AWS Infrastructure Variables**

| Variable Name | Value | Description |
|---------------|-------|-------------|
| `AWS_REGION` | `ap-southeast-5` | Primary AWS region for all resources |
| `ECR_ACCOUNT_ID` | `************` | Central ECR account for Docker images |
| `UAT_ACCOUNT_ID` | `************` | UAT environment AWS account |
| `PROD_ACCOUNT_ID` | `************` | Production environment AWS account |

### **AWS IAM Variables**

| Variable Name | Value | Description |
|---------------|-------|-------------|
| `ROLE_GITHUB_OIDC` | `github-action-role` | OIDC role name in ECR account |
| `ROLE_TARGET` | `OrganizationAccountAccessRole` | Cross-account role name |

## 🔧 How to Set Up Variables

### **Step 1: Navigate to Repository Settings**
1. Go to your GitHub repository
2. Click **Settings** tab
3. In the left sidebar, click **Secrets and variables**
4. Click **Actions**
5. Click the **Variables** tab

### **Step 2: Add Each Variable**
For each variable in the table above:
1. Click **New repository variable**
2. Enter the **Name** (e.g., `AWS_REGION`)
3. Enter the **Value** (e.g., `ap-southeast-5`)
4. Click **Add variable**

### **Step 3: Verify Setup**
After adding all variables, you should see:
- ✅ AWS_REGION
- ✅ ECR_ACCOUNT_ID
- ✅ UAT_ACCOUNT_ID
- ✅ PROD_ACCOUNT_ID
- ✅ ROLE_GITHUB_OIDC
- ✅ ROLE_TARGET

## 📝 Usage in Workflows

### **Before (Hardcoded - Bad)**
```yaml
env:
  AWS_REGION: ap-southeast-5           # ❌ Duplicated everywhere
  ECR_ACCOUNT_ID: ************         # ❌ Hard to maintain
  UAT_ACCOUNT_ID: ************         # ❌ Copy-paste errors
  PROD_ACCOUNT_ID: ************        # ❌ Inconsistent values
  ROLE_GITHUB_OIDC: github-action-role # ❌ Repeated in every workflow
  ROLE_TARGET: OrganizationAccountAccessRole
```

### **After (Variables - Good)**
```yaml
env:
  # Only project-specific settings here
  LAMBDA_PATH: sample/lambda-nodejs
  FUNCTION_NAME: lambda-nodejs
  UAT_FUNCTION_NAME: uat-lambda-api-sample-lambda
  PROD_FUNCTION_NAME: prod-lambda-api-sample-lambda

jobs:
  docker-build:
    uses: ./.github/workflows/reusable-docker-build-push.yml
    with:
      aws-region: ${{ vars.AWS_REGION }}        # ✅ From repository variables
      ecr-account-id: ${{ vars.ECR_ACCOUNT_ID }} # ✅ Centrally managed
      uat-account-id: ${{ vars.UAT_ACCOUNT_ID }} # ✅ Single source of truth
```

## 🎯 Benefits

### **For New Lambda Functions**
Creating a new Lambda CI/CD pipeline is now super simple:

```yaml
name: App - My New Lambda CI/CD

env:
  # Only these 4 lines need to change!
  LAMBDA_PATH: my-new-lambda
  FUNCTION_NAME: my-new-lambda  
  UAT_FUNCTION_NAME: uat-my-new-lambda
  PROD_FUNCTION_NAME: prod-my-new-lambda

jobs:
  # All the rest stays exactly the same!
  test-build:
    uses: ./.github/workflows/reusable-nodejs-test-build.yml
    # ... uses vars.AWS_REGION, vars.ECR_ACCOUNT_ID automatically
```

### **For Infrastructure Changes**
- **Change AWS region**: Update 1 variable, affects all workflows
- **Change ECR account**: Update 1 variable, affects all workflows  
- **Add new environment**: Add new account variable, use in workflows

## 🚀 Organization-Level Variables (Optional)

For multiple repositories, consider setting these as **Organization variables**:
1. Go to GitHub Organization → **Settings** → **Secrets and variables** → **Actions**
2. Set the same variables at organization level
3. All repositories inherit these values automatically

## ⚠️ Important Notes

- **Repository variables** override organization variables
- **Environment variables** (`env:`) are for project-specific values
- **Repository variables** (`vars.`) are for shared infrastructure values
- Variables are **public** (visible in logs), use **Secrets** for sensitive data
