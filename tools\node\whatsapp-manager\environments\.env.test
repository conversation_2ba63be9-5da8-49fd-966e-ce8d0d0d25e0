# Test Environment Configuration

# Application
NODE_ENV=test
PORT=3001
API_PREFIX=/api

# JWT Configuration
JWT_SECRET=test-secret-key-123-minimum-32-characters-required-for-testing
JWT_EXPIRES_IN=1h
JWT_ISSUER=ezychat-whatsapp-manager
JWT_AUDIENCE=ezychat-client

# QR Token Configuration
QR_TOKEN_EXPIRY_SEC=300
QR_TOKEN_MAX_ACTIVE=5
QR_AUTH_BASE_URL=http://localhost:3001

# Database Configuration
DYNAMODB_TABLE_NAME=WhatsAppSessions-test
AUTH_TOKEN_TABLE_NAME=AuthTokens-test
AWS_REGION=ap-southeast-1

DYNAMODB_ENDPOINT=http://localhost:8000
AWS_ACCESS_KEY_ID=test-key-id
AWS_SECRET_ACCESS_KEY=test-secret-key

# WhatsApp Configuration
MAX_CONCURRENT_SESSIONS=100
SESSION_TTL_HOURS=72
QR_TOKEN_EXPIRY_SEC=300
QR_CODE_TIMEOUT_SECONDS=30
RECONNECT_ATTEMPTS=2
RECONNECT_MAX_ATTEMPTS=5
RECONNECT_DELAY_MS=1000
CONNECTION_TIMEOUT_MS=60000

# Encryption Configuration
ENCRYPTION_KEY=test-encryption-key-32-chars-minimum-for-testing-env

# Logging Configuration
LOG_LEVEL=error
LOG_FORMAT=json

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=2000

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# Rate Limiting (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Test Features
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false
