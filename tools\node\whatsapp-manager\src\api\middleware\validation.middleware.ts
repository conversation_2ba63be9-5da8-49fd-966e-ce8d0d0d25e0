import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';

/**
 * Validation middleware for session requests
 */
export const validateSessionRequest = {
  /**
   * Validate start session request
   */
  startSession: [
    param('userId')
      .isLength({ min: 3, max: 100 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('User ID must be 3-100 characters and contain only alphanumeric characters, underscores, and hyphens'),
    
    body('deviceName')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Device name must not exceed 50 characters'),
    
    body('browserName')
      .optional()
      .isLength({ max: 50 })
      .withMessage('Browser name must not exceed 50 characters'),
    
    body('forceNew')
      .optional()
      .isBoolean()
      .withMessage('forceNew must be a boolean'),
    
    handleValidationErrors
  ],

  /**
   * Validate reconnect session request
   */
  reconnectSession: [
    param('userId')
      .isLength({ min: 3, max: 100 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('User ID must be 3-100 characters and contain only alphanumeric characters, underscores, and hyphens'),
    
    body('reason')
      .optional()
      .isLength({ max: 200 })
      .withMessage('Reason must not exceed 200 characters'),
    
    handleValidationErrors
  ],

  /**
   * Validate get session status request
   */
  getSessionStatus: [
    param('userId')
      .isLength({ min: 3, max: 100 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('User ID must be 3-100 characters and contain only alphanumeric characters, underscores, and hyphens'),
    
    query('includeHealth')
      .optional()
      .isIn(['true', 'false'])
      .withMessage('includeHealth must be true or false'),
    
    handleValidationErrors
  ],

  /**
   * Validate terminate session request
   */
  terminateSession: [
    param('userId')
      .isLength({ min: 3, max: 100 })
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('User ID must be 3-100 characters and contain only alphanumeric characters, underscores, and hyphens'),
    
    body('reason')
      .optional()
      .isLength({ max: 200 })
      .withMessage('Reason must not exceed 200 characters'),
    
    body('forceDelete')
      .optional()
      .isBoolean()
      .withMessage('forceDelete must be a boolean'),
    
    handleValidationErrors
  ],

  /**
   * Validate list sessions request
   */
  listSessions: [
    query('status')
      .optional()
      .isIn(['initializing', 'qr_pending', 'connecting', 'connected', 'disconnected', 'error'])
      .withMessage('Invalid status value'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Limit must be between 1 and 1000'),
    
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be a non-negative integer'),
    
    query('includeInactive')
      .optional()
      .isIn(['true', 'false'])
      .withMessage('includeInactive must be true or false'),
    
    query('sortBy')
      .optional()
      .isIn(['createdAt', 'updatedAt', 'connectedAt'])
      .withMessage('sortBy must be one of: createdAt, updatedAt, connectedAt'),
    
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('sortOrder must be asc or desc'),
    
    handleValidationErrors
  ],

  /**
   * Validate terminate all sessions request
   */
  terminateAll: [
    body('reason')
      .optional()
      .isLength({ max: 200 })
      .withMessage('Reason must not exceed 200 characters'),
    
    handleValidationErrors
  ]
};

/**
 * Handle validation errors middleware
 */
function handleValidationErrors(req: Request, res: Response, next: NextFunction): void {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.type === 'field' ? (error as any).path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? (error as any).value : undefined
    }));

    res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        statusCode: 400,
        details: errorMessages
      }
    });
    return;
  }

  next();
}

/**
 * Generic validation middleware factory
 */
export function createValidationMiddleware(validations: any[]) {
  return [
    ...validations,
    handleValidationErrors
  ];
}

/**
 * Sanitize user input middleware
 */
export function sanitizeInput(req: Request, _res: Response, next: NextFunction): void {
  // Sanitize userId parameter
  if (req.params['userId']) {
    req.params['userId'] = req.params['userId'].trim().toLowerCase();
  }

  // Sanitize body fields
  if (req.body) {
    if (req.body.deviceName) {
      req.body.deviceName = req.body.deviceName.trim();
    }
    if (req.body.browserName) {
      req.body.browserName = req.body.browserName.trim();
    }
    if (req.body.reason) {
      req.body.reason = req.body.reason.trim();
    }
  }

  next();
}

/**
 * Content type validation middleware
 */
export function validateContentType(req: Request, res: Response, next: NextFunction): void {
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.get('Content-Type');
    
    if (!contentType || !contentType.includes('application/json')) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Content-Type must be application/json',
          code: 'INVALID_CONTENT_TYPE',
          statusCode: 400
        }
      });
      return;
    }
  }

  next();
}

/**
 * Request size validation middleware
 */
export function validateRequestSize(maxSize: number = 1024 * 1024) { // 1MB default
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      res.status(413).json({
        success: false,
        error: {
          message: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
          statusCode: 413,
          maxSize
        }
      });
      return;
    }

    next();
  };
}

/**
 * Custom validation functions
 */
export const customValidators = {
  /**
   * Validate user ID format
   */
  isValidUserId: (value: string): boolean => {
    return /^[a-zA-Z0-9_-]{3,100}$/.test(value);
  },

  /**
   * Validate session status
   */
  isValidSessionStatus: (value: string): boolean => {
    const validStatuses = ['initializing', 'qr_pending', 'connecting', 'connected', 'disconnected', 'error'];
    return validStatuses.includes(value);
  },

  /**
   * Validate sort parameters
   */
  isValidSortBy: (value: string): boolean => {
    const validSortFields = ['createdAt', 'updatedAt', 'connectedAt'];
    return validSortFields.includes(value);
  },

  /**
   * Validate sort order
   */
  isValidSortOrder: (value: string): boolean => {
    return ['asc', 'desc'].includes(value);
  }
};

/**
 * Error response helper
 */
export function createValidationError(message: string, details?: any) {
  return {
    success: false,
    error: {
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 400,
      details
    }
  };
}
