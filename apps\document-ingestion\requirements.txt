# Lambda function dependencies for document ingestion pipeline
# Optimized for AWS Lambda environment

# Core AWS and data processing
boto3>=1.34.0
pandas>=2.0.0
numpy>=1.24.0

# OpenAI for embeddings
openai>=1.99.0

# Supabase and PostgreSQL
supabase>=2.0.0
psycopg2-binary>=2.9.0

# Vector operations (Supabase vecs)
vecs>=0.4.0

# Utilities
requests>=2.31.0
tenacity>=8.2.0
python-dotenv>=1.0.0
pydantic>=2.0.0

# AWS SDK
boto3>=1.34.0
botocore>=1.34.0

# Dependency Injection
dependency-injector>=4.41.0

# JSON and data validation
jsonschema>=4.17.0
PyYAML>=6.0.0

# Development and testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
freezegun>=1.2.0
flake8>=6.0.0
