import { AuthService } from '../../../infrastructure/services/AuthService';
import { AuthTokenError } from '../../../domain/value-objects/AuthToken';

// Mock environment variables
const originalEnv = process.env;

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    // Reset environment variables
    process.env = {
      ...originalEnv,
      JWT_SECRET: 'test-secret-key-32-characters-long-minimum',
      QR_TOKEN_EXPIRY_SEC: '300',
      JWT_ISSUER: 'test-issuer',
      JWT_AUDIENCE: 'test-audience'
    };

    authService = new AuthService();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('constructor', () => {
    it('should initialize with valid configuration', () => {
      expect(authService).toBeInstanceOf(AuthService);
    });

    it('should throw error for short JWT secret', () => {
      process.env['JWT_SECRET'] = 'short';

      expect(() => {
        new AuthService();
      }).toThrow('JWT_SECRET must be at least 32 characters long');
    });

    it('should throw error for invalid expiry seconds', () => {
      process.env['QR_TOKEN_EXPIRY_SEC'] = '0';

      expect(() => {
        new AuthService();
      }).toThrow('QR_TOKEN_EXPIRY_SEC must be between 1 and 86400 seconds');
    });

    it('should throw error for missing JWT secret', () => {
      delete process.env['JWT_SECRET'];

      expect(() => {
        new AuthService();
      }).toThrow('Required environment variable JWT_SECRET is not set');
    });
  });

  describe('generateQrAuthToken', () => {
    it('should generate a valid JWT token', () => {
      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should generate token with custom expiry', () => {
      const userId = 'user123';
      const expirySeconds = 600;
      const token = authService.generateQrAuthToken(userId, expirySeconds);

      const payload = authService.verifyToken(token);
      const expectedExpiry = Math.floor(Date.now() / 1000) + expirySeconds;
      
      expect(payload.exp).toBeCloseTo(expectedExpiry, -1); // Allow 1 second tolerance
    });

    it('should throw error for empty user ID', () => {
      expect(() => {
        authService.generateQrAuthToken('');
      }).toThrow('User ID is required for token generation');
    });

    it('should throw error for invalid expiry seconds', () => {
      expect(() => {
        authService.generateQrAuthToken('user123', 0);
      }).toThrow('Expiry seconds must be between 1 and 86400');

      expect(() => {
        authService.generateQrAuthToken('user123', 86401);
      }).toThrow('Expiry seconds must be between 1 and 86400');
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', () => {
      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId);
      const payload = authService.verifyToken(token);

      expect(payload.userId).toBe(userId);
      expect(payload.purpose).toBe('qr_auth');
      expect(payload.tokenId).toBeDefined();
      expect(payload.iat).toBeDefined();
      expect(payload.exp).toBeDefined();
      expect(payload.jti).toBeDefined();
    });

    it('should throw error for empty token', () => {
      expect(() => {
        authService.verifyToken('');
      }).toThrow('Token is required for verification');
    });

    it('should throw error for invalid token format', () => {
      expect(() => {
        authService.verifyToken('invalid-token');
      }).toThrow(AuthTokenError);
    });

    it('should throw error for expired token', () => {
      // Mock Date.now to create a deterministic expired token
      const originalDateNow = Date.now;
      const mockTime = 1000000000000; // Fixed timestamp

      // Mock Date.now for token generation
      Date.now = jest.fn(() => mockTime);

      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId, 1);

      // Advance time to make token expired
      Date.now = jest.fn(() => mockTime + 2000); // 2 seconds later

      expect(() => {
        authService.verifyToken(token);
      }).toThrow('Token has expired');

      // Restore original Date.now
      Date.now = originalDateNow;
    });

    it('should throw error for token with wrong issuer', () => {
      // Create token with different service
      process.env['JWT_ISSUER'] = 'different-issuer';
      const differentAuthService = new AuthService();

      const token = authService.generateQrAuthToken('user123');

      expect(() => {
        differentAuthService.verifyToken(token);
      }).toThrow(AuthTokenError);
    });
  });

  describe('createAuthTokenFromJWT', () => {
    it('should create AuthToken from JWT', () => {
      const userId = 'user123';
      const jwtToken = authService.generateQrAuthToken(userId);
      const authToken = authService.createAuthTokenFromJWT(jwtToken);

      expect(authToken.userId).toBe(userId);
      expect(authToken.token).toBe(jwtToken);
      expect(authToken.metadata?.['purpose']).toBe('qr_auth');
      expect(authToken.isUsed).toBe(false);
    });

    it('should throw error for invalid JWT', () => {
      expect(() => {
        authService.createAuthTokenFromJWT('invalid-jwt');
      }).toThrow(AuthTokenError);
    });
  });

  describe('extractTokenId', () => {
    it('should extract token ID without verification', () => {
      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId);
      const tokenId = authService.extractTokenId(token);

      expect(typeof tokenId).toBe('string');
      expect(tokenId).toMatch(/^qr_/);
    });

    it('should throw error for invalid token', () => {
      expect(() => {
        authService.extractTokenId('invalid-token');
      }).toThrow('Failed to extract token ID');
    });
  });

  describe('extractUserId', () => {
    it('should extract user ID without verification', () => {
      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId);
      const extractedUserId = authService.extractUserId(token);

      expect(extractedUserId).toBe(userId);
    });

    it('should throw error for invalid token', () => {
      expect(() => {
        authService.extractUserId('invalid-token');
      }).toThrow('Failed to extract user ID');
    });
  });

  describe('isTokenExpired', () => {
    it('should return false for non-expired token', () => {
      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId);
      const isExpired = authService.isTokenExpired(token);

      expect(isExpired).toBe(false);
    });

    it('should return true for expired token', () => {
      // Mock Date.now for deterministic testing
      const originalDateNow = Date.now;
      const mockTime = 1000000000000; // Fixed timestamp

      // Mock Date.now for token generation
      Date.now = jest.fn(() => mockTime);

      const userId = 'user123';
      const token = authService.generateQrAuthToken(userId, 1);

      // Advance time to make token expired
      Date.now = jest.fn(() => mockTime + 2000); // 2 seconds later

      const isExpired = authService.isTokenExpired(token);
      expect(isExpired).toBe(true);

      // Restore original Date.now
      Date.now = originalDateNow;
    });

    it('should return true for invalid token', () => {
      const isExpired = authService.isTokenExpired('invalid-token');
      expect(isExpired).toBe(true);
    });
  });

  describe('getTokenExpiry', () => {
    it('should get token expiry date', () => {
      const userId = 'user123';
      const expirySeconds = 600;
      const token = authService.generateQrAuthToken(userId, expirySeconds);
      const expiry = authService.getTokenExpiry(token);

      expect(expiry).toBeInstanceOf(Date);
      expect(expiry.getTime()).toBeGreaterThan(Date.now());
    });

    it('should throw error for invalid token', () => {
      expect(() => {
        authService.getTokenExpiry('invalid-token');
      }).toThrow('Failed to extract expiry');
    });
  });

  describe('generateQrAuthUrl', () => {
    it('should generate QR auth URL with default base URL', () => {
      const token = 'test-token';
      const url = authService.generateQrAuthUrl(token);

      expect(url).toContain('https://app.ezychat.com');
      expect(url).toContain('/auth/qr');
      expect(url).toContain('token=test-token');
    });

    it('should generate QR auth URL with custom base URL', () => {
      const token = 'test-token';
      const baseUrl = 'https://custom.example.com';
      const url = authService.generateQrAuthUrl(token, baseUrl);

      expect(url).toContain(baseUrl);
      expect(url).toContain('/auth/qr');
      expect(url).toContain('token=test-token');
    });

    it('should throw error for empty token', () => {
      expect(() => {
        authService.generateQrAuthUrl('');
      }).toThrow('Token is required for URL generation');
    });

    it('should properly encode token in URL', () => {
      const token = 'test+token/with=special&chars';
      const url = authService.generateQrAuthUrl(token);

      expect(url).toContain(encodeURIComponent(token));
    });
  });

  describe('createTokenMetadata', () => {
    it('should create default metadata', () => {
      const metadata = authService.createTokenMetadata();

      expect(metadata['createdBy']).toBe('AuthService');
      expect(metadata['version']).toBe('1.0');
      expect(metadata['userAgent']).toBeDefined();
    });

    it('should merge additional metadata', () => {
      const additionalData = { customField: 'customValue' };
      const metadata = authService.createTokenMetadata(additionalData);

      expect(metadata['createdBy']).toBe('AuthService');
      expect(metadata['customField']).toBe('customValue');
    });
  });

  describe('getConfig', () => {
    it('should return configuration without JWT secret', () => {
      const config = authService.getConfig();

      expect(config.defaultExpirySeconds).toBe(300);
      expect(config.issuer).toBe('test-issuer');
      expect(config.audience).toBe('test-audience');
      expect(config).not.toHaveProperty('jwtSecret');
    });
  });
});
