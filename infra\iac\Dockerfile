FROM alpine:3.19

# Install dependencies
RUN apk add --no-cache \
    curl \
    unzip \
    bash \
    git \
    ca-certificates

# Install Terraform
ARG TERRAFORM_VERSION=1.9.5
RUN curl -fsSL https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip -o terraform.zip && \
    unzip terraform.zip && \
    mv terraform /usr/local/bin/ && \
    rm terraform.zip && \
    terraform version

# Set working directory
WORKDIR /workspace

# Copy the entire iac directory
COPY . /workspace/

# Set default command
CMD ["/bin/bash"]