# API Gateway Outputs
output "api_gateway_id" {
  description = "The ID of the API Gateway"
  value       = aws_api_gateway_rest_api.this.id
}

output "api_gateway_arn" {
  description = "The ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.this.arn
}

output "api_gateway_execution_arn" {
  description = "The execution ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.this.execution_arn
}

output "api_gateway_url" {
  description = "The URL of the API Gateway"
  value       = aws_api_gateway_stage.this.invoke_url
}

output "api_gateway_stage_name" {
  description = "The stage name of the API Gateway deployment"
  value       = aws_api_gateway_stage.this.stage_name
}

# Custom Domain Outputs
output "custom_domain_name" {
  description = "The custom domain name for the API Gateway"
  value       = var.domain_name != null ? aws_api_gateway_domain_name.this[0].domain_name : null
}

output "custom_domain_regional_domain_name" {
  description = "The regional domain name for the custom domain"
  value       = var.domain_name != null ? aws_api_gateway_domain_name.this[0].regional_domain_name : null
}

output "custom_domain_regional_zone_id" {
  description = "The regional zone ID for the custom domain"
  value       = var.domain_name != null ? aws_api_gateway_domain_name.this[0].regional_zone_id : null
}

# Lambda Function Outputs
output "lambda_function_arns" {
  description = "Map of Lambda function ARNs"
  value = {
    for k, v in aws_lambda_function.services : k => v.arn
  }
}

output "lambda_function_names" {
  description = "Map of Lambda function names"
  value = {
    for k, v in aws_lambda_function.services : k => v.function_name
  }
}

output "lambda_function_invoke_arns" {
  description = "Map of Lambda function invoke ARNs"
  value = {
    for k, v in aws_lambda_function.services : k => v.invoke_arn
  }
}

# IAM Role Outputs
output "lambda_execution_role_arns" {
  description = "Map of Lambda execution role ARNs"
  value = {
    for k, v in aws_iam_role.lambda_execution : k => v.arn
  }
}

# Route 53 Outputs
output "route53_record_name" {
  description = "The name of the Route 53 record"
  value       = var.domain_name != null && var.route53_zone_id != null ? aws_route53_record.api_domain[0].name : null
}

output "route53_record_fqdn" {
  description = "The FQDN of the Route 53 record"
  value       = var.domain_name != null && var.route53_zone_id != null ? aws_route53_record.api_domain[0].fqdn : null
}

# API Routes Information
output "api_routes" {
  description = "List of configured API routes"
  value = [
    for route in local.api_routes : {
      service_name = route.service_name
      path_pattern = route.path_pattern
      base_path    = route.base_path
      api_path     = route.api_path
      is_wildcard  = route.is_wildcard
      method       = "ANY"
      url          = "${aws_api_gateway_stage.this.invoke_url}${route.base_path}"
    }
  ]
}

# Summary Output
output "api_endpoints" {
  description = "Summary of API endpoints"
  value = {
    default_url   = aws_api_gateway_stage.this.invoke_url
    custom_domain = var.domain_name
    stage_name    = aws_api_gateway_stage.this.stage_name
    lambda_count  = length(local.lambda_services)
    routes_count  = length(local.api_routes)
  }
}

# CloudWatch Log Groups Outputs
output "lambda_log_group_names" {
  description = "Names of the CloudWatch Log Groups for Lambda functions"
  value = {
    for k, v in aws_cloudwatch_log_group.lambda_logs : k => v.name
  }
}

output "lambda_log_group_arns" {
  description = "ARNs of the CloudWatch Log Groups for Lambda functions"
  value = {
    for k, v in aws_cloudwatch_log_group.lambda_logs : k => v.arn
  }
}

# Lambda Version Outputs
output "lambda_function_versions" {
  description = "Published versions of Lambda functions"
  value = {
    for k, v in aws_lambda_function.services : k => v.version
  }
}

output "lambda_function_qualified_arns" {
  description = "Qualified ARNs of Lambda functions (includes version)"
  value = {
    for k, v in aws_lambda_function.services : k => v.qualified_arn
  }
}
