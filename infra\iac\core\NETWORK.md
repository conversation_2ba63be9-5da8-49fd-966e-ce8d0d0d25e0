# UAT Network Configuration

This document describes the validated network configuration for the UAT environment.

## ✅ Validated Network Architecture

### VPC Configuration
- **VPC CIDR**: `10.0.0.0/16` (65,536 IP addresses)
- **Region**: `ap-southeast-5` (Malaysia)
- **DNS Support**: Enabled
- **DNS Hostnames**: Enabled
- **Status**: ✅ Terraform validated

### Subnet Layout
The network is distributed across 2 availability zones for redundancy:

#### Public Subnets (Internet-accessible)
- **Count**: 2 subnets
- **Purpose**: Application Load Balancer + ECS tasks (current)
- **Auto-assign Public IP**: Yes
- **Route**: `0.0.0.0/0` → Internet Gateway
- **Usage**: Active for ALB and ECS workloads

#### Private Subnets (Reserved for future use)
- **Count**: 2 subnets
- **Purpose**: Reserved for production-like deployment
- **Auto-assign Public IP**: No
- **Route**: No internet route (NAT Gateway disabled)
- **Usage**: Created but unused (cost optimization)

### NAT Gateway Configuration
- **Strategy**: Disabled (maximum cost savings)
- **Reason**: ECS tasks run in public subnets with direct internet access
- **Cost**: $0/month (saves ~$45/month)
- **Internet Access**: Direct via Internet Gateway for public subnets

### ✅ Validated UAT Settings
```hcl
vpc_cidr             = "10.0.0.0/16"  # UAT CIDR range
public_subnet_count  = 2              # 2 AZs for redundancy
private_subnet_count = 2              # Reserved for future use
enable_nat_gateway   = false          # Disabled for maximum cost savings
nat_gateway_strategy = "single"       # Not used when disabled
use_private_subnets  = false          # ECS tasks in public subnets
```

**Validation Status**: ✅ `terraform validate` passed

## ✅ Validated Subnet CIDR Allocation

With 4 total subnets (2 public + 2 private), the automatic CIDR calculation creates:

| Subnet Type | AZ | CIDR Block | IP Count | Current Usage | Status |
|-------------|----|-----------|---------:|---------------|--------|
| Public | ap-southeast-5a | 10.0.0.0/19 | 8,192 | ALB + ECS tasks | ✅ Active |
| Public | ap-southeast-5b | *********/19 | 8,192 | ALB + ECS tasks | ✅ Active |
| Private | ap-southeast-5a | *********/19 | 8,192 | Reserved | ⏸️ Unused |
| Private | ap-southeast-5b | *********/19 | 8,192 | Reserved | ⏸️ Unused |

*Remaining: **********/17 (32,768 IPs) available for future expansion*

### Data Flow Validation
1. **Internet → ALB**: Internet Gateway → Public Subnets → ALB ✅
2. **ALB → ECS**: ALB → ECS tasks in public subnets ✅
3. **ECS → Internet**: ECS tasks → Internet Gateway → Internet ✅
4. **DNS**: api.uat.ezychat.ai → ALB ✅

## 💰 Validated Cost Analysis

### ✅ Current UAT Configuration (Maximum Cost Savings)
- **NAT Gateway**: $0/month (disabled - saves $45/month)
- **Data Processing**: $0/month (no NAT Gateway data charges)
- **Elastic IPs**: $0/month (none created)
- **VPC/Subnets**: $0/month (free tier)
- **Internet Gateway**: $0/month (free)
- **Total Network Cost**: ~$0-5/month (minimal AWS costs only)

### Cost Comparison Table
| Configuration | NAT Gateway | ECS Location | Monthly Cost | Savings |
|---------------|-------------|--------------|-------------:|--------:|
| **UAT (Current)** | Disabled | Public | ~$0-5 | Baseline |
| UAT with NAT | Single | Public | ~$45-50 | -$45 |
| UAT Production-like | Single | Private | ~$50-60 | -$55 |
| Production | Per-AZ | Private | ~$135+ | -$130+ |

### Alternative Configurations

#### Current UAT Setup (Public only)
```hcl
enable_nat_gateway = false
use_private_subnets = false
```
- **Cost**: ~$5-10/month (minimal)
- **Use case**: UAT/Development environment

#### High Availability (Per-AZ NAT)
```hcl
nat_gateway_strategy = "per_az"
```
- **Cost**: ~$90/month (2 NAT Gateways)
- **Use case**: Production-like testing

## Production Recommendations

For production environment, consider these settings:

```hcl
# Production Network Configuration
vpc_cidr             = "10.1.0.0/16"    # Separate CIDR from UAT
public_subnet_count  = 3                # 3 AZs for high availability
private_subnet_count = 3                # 3 private subnets
enable_nat_gateway   = true             # Required for private subnets
nat_gateway_strategy = "per_az"         # NAT Gateway per AZ (~$135/month)
use_private_subnets  = true             # Security: ECS in private subnets
```

### Production Benefits
- **High Availability**: No single point of failure
- **Security**: ECS tasks in private subnets
- **Scalability**: 3 AZs for better distribution
- **Isolation**: Separate CIDR from UAT

## Security Considerations

### Current UAT (Public Subnets)
- ✅ Cost-effective for testing
- ⚠️ ECS tasks have public IPs
- ⚠️ Direct internet exposure

### Recommended Production (Private Subnets)
- ✅ ECS tasks isolated from internet
- ✅ Outbound internet via NAT Gateway only
- ✅ Better security posture
- ❌ Higher cost

## Migration Path

To migrate UAT to private subnets:

1. **Update configuration**:
   ```hcl
   use_private_subnets = true
   ```

2. **Apply changes**:
   ```bash
   terraform plan
   terraform apply
   ```

3. **Verify connectivity**:
   - ECS tasks should still have internet access via NAT Gateway
   - No public IPs assigned to ECS tasks

## Monitoring

Key metrics to monitor:
- **NAT Gateway Data Processing**: Monitor costs
- **ECS Task Connectivity**: Ensure internet access works
- **Subnet Utilization**: Track IP usage per subnet

## Troubleshooting

### Common Issues
1. **ECS tasks can't reach internet**: Check NAT Gateway and route tables
2. **High NAT Gateway costs**: Monitor data transfer patterns
3. **IP exhaustion**: Consider subnet resizing or additional subnets
