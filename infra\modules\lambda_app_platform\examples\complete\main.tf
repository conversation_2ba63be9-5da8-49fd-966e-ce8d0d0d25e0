# Complete Example: Lambda App Platform with Custom Domain
# This example demonstrates a full deployment with multiple Lambda functions,
# custom domain binding, and Route 53 integration

terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0"
    }
  }
}

# Configure providers
provider "aws" {
  region = "ap-southeast-1"
}

# Provider alias for Route 53 (typically in master account)
provider "aws" {
  alias  = "master"
  region = "ap-southeast-1"
  # In real usage, this would have different credentials for the master account
}

# Data sources
data "aws_route53_zone" "main" {
  provider = aws.master
  name     = "ezychat.ai"
}

# Create ACM certificate for the custom domain
resource "aws_acm_certificate" "api" {
  domain_name       = "api.example.ezychat.ai"
  validation_method = "DNS"

  subject_alternative_names = [
    "*.api.example.ezychat.ai"
  ]

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name        = "api.example.ezychat.ai"
    Environment = "example"
  }
}

# Create DNS records for certificate validation
resource "aws_route53_record" "cert_validation" {
  provider = aws.master
  for_each = {
    for dvo in aws_acm_certificate.api.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  zone_id = data.aws_route53_zone.main.zone_id
  name    = each.value.name
  type    = each.value.type
  records = [each.value.record]
  ttl     = 60
}

# Validate the certificate
resource "aws_acm_certificate_validation" "api" {
  certificate_arn         = aws_acm_certificate.api.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]

  timeouts {
    create = "5m"
  }
}

# Local variables for Lambda services configuration
locals {
  lambda_services = {
    # Container-based Lambda example
    whatsapp-api = {
      image_repository_uri = "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-api:latest"
      timeout              = 30
      memory_size          = 512
      
      environment_variables = {
        NODE_ENV    = "production"
        API_VERSION = "v1"
        LOG_LEVEL   = "info"
      }
      
      secrets = {
        DATABASE_URL = "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:example/database-url"
        API_KEY      = "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:example/api-key"
      }
      
      api_gateway = {
        routes = [
          {
            path   = "/whatsapp/{proxy+}"
            method = "ANY"
          },
          {
            path   = "/whatsapp/health"
            method = "GET"
          }
        ]
      }
      
      tags = {
        Service = "WhatsApp API"
        Team    = "Backend"
      }
    }
    
    # Zip-based Lambda example
    notifications = {
      filename         = "notifications.zip"
      source_code_hash = "placeholder-hash"
      handler          = "index.handler"
      runtime          = "nodejs18.x"
      timeout          = 15
      memory_size      = 256
      
      environment_variables = {
        SERVICE_NAME = "notifications"
        ENVIRONMENT  = "example"
      }
      
      iam_policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Action = [
              "sns:Publish",
              "sns:CreateTopic"
            ]
            Resource = "*"
          }
        ]
      })
      
      api_gateway = {
        routes = [
          {
            path   = "/notifications"
            method = "POST"
          },
          {
            path   = "/notifications/{id}"
            method = "GET"
          }
        ]
      }
      
      tags = {
        Service = "Notifications"
        Team    = "Platform"
      }
    }
    
    # Health check Lambda
    health-check = {
      filename         = "health-check.zip"
      source_code_hash = "health-check-hash"
      handler          = "index.handler"
      runtime          = "nodejs18.x"
      timeout          = 5
      memory_size      = 128
      
      environment_variables = {
        SERVICE_VERSION = "1.0.0"
      }
      
      api_gateway = {
        routes = [
          {
            path   = "/health"
            method = "GET"
          }
        ]
      }
      
      tags = {
        Service = "Health Check"
        Team    = "Platform"
      }
    }
  }
}

# Deploy the Lambda App Platform
module "lambda_platform" {
  source = "../../"
  
  env      = "example"
  app_name = "api"
  
  # Lambda services configuration
  lambda_services_config = local.lambda_services
  
  # Custom domain configuration
  domain_name     = "api.example.ezychat.ai"
  certificate_arn = aws_acm_certificate_validation.api.certificate_arn
  route53_zone_id = data.aws_route53_zone.main.zone_id
  
  # API Gateway configuration
  api_stage_name = "v1"
  
  tags = {
    Environment = "Example"
    Project     = "EzyChat"
    ManagedBy   = "Terraform"
  }
  
  providers = {
    aws.route53_region = aws.master
  }
}

# Outputs
output "api_gateway_url" {
  description = "The invoke URL of the API Gateway"
  value       = module.lambda_platform.api_gateway_url
}

output "custom_domain_url" {
  description = "The custom domain URL"
  value       = module.lambda_platform.custom_domain_name != null ? "https://${module.lambda_platform.custom_domain_name}" : null
}

output "lambda_functions" {
  description = "Map of Lambda function ARNs"
  value       = module.lambda_platform.lambda_function_arns
}

output "api_endpoints" {
  description = "Summary of API endpoints"
  value       = module.lambda_platform.api_endpoints
}

output "api_routes" {
  description = "List of configured API routes"
  value       = module.lambda_platform.api_routes
}
