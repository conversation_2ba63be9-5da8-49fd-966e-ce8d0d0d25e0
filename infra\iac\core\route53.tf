# Query the main domain from master account
data "aws_route53_zone" "main_domain" {
  provider = aws.master
  name     = var.domain_name
}

# Create DNS record for the ALB API endpoint
# Production: {api_subdomain}.{domain} 
# Non-production: {api_subdomain}.{env}.{domain}
resource "aws_route53_record" "app" {
  provider = aws.master
  zone_id  = data.aws_route53_zone.main_domain.zone_id
  name     = local.app_domain_name
  type     = "A"

  alias {
    name                   = module.api_platform.alb_dns_name
    zone_id                = module.api_platform.alb_zone_id
    evaluate_target_health = true
  }
}

# DNS Outputs
output "main_domain_zone_id" {
  value       = data.aws_route53_zone.main_domain.zone_id
  description = "Zone ID of the main domain"
}

output "api_dns_name" {
  value       = local.api_domain_name
  description = "The DNS name of the API"
}

output "app_dns_name" {
  value       = local.app_domain_name
  description = "The DNS name of the App"
}
