// ECS Scheduler Lambda Function
// This function scales ECS services up or down based on tags
exports.handler = async (event) => {
  // Import AWS SDK v3 modules
  const {
    ECSClient,
    ListServicesCommand,
    DescribeServicesCommand,
    UpdateServiceCommand,
    ListTagsForResourceCommand,
  } = require("@aws-sdk/client-ecs");

  // Create ECS client
  const ecs = new ECSClient();

  // Get environment variables
  const clusterName = process.env.ECS_CLUSTER_NAME;
  const tagKey = process.env.TAG_KEY || "schedule";
  const tagValue = process.env.TAG_VALUE || "true";

  // Get action and desired count from the event
  const action = event.action;
  const desiredCount = parseInt(event.desiredCount);

  console.log(
    `Scaling ECS services in cluster ${clusterName} with tag ${tagKey}=${tagValue} to ${desiredCount} tasks (${action})`
  );

  try {
    // List all services in the cluster
    const listServicesCommand = new ListServicesCommand({
      cluster: clusterName,
    });
    const listServicesResponse = await ecs.send(listServicesCommand);
    const serviceArns = listServicesResponse.serviceArns || [];

    if (serviceArns.length === 0) {
      console.log("No services found in the cluster");
      return { statusCode: 200, body: "No services found in the cluster" };
    }

    console.log(`Found ${serviceArns.length} services in the cluster`);

    // Process services in batches of 10 (AWS API limit for describeServices)
    for (let i = 0; i < serviceArns.length; i += 10) {
      const batch = serviceArns.slice(i, i + 10);

      // Get details for each service
      const describeServicesCommand = new DescribeServicesCommand({
        cluster: clusterName,
        services: batch,
      });
      const describeServicesResponse = await ecs.send(describeServicesCommand);

      // Process each service
      for (const service of describeServicesResponse.services) {
        // Get tags for the service
        const listTagsCommand = new ListTagsForResourceCommand({
          resourceArn: service.serviceArn,
        });
        const listTagsResponse = await ecs.send(listTagsCommand);

        const tags = listTagsResponse.tags || [];
        const hasScheduleTag = tags.some(
          (tag) => tag.key === tagKey && tag.value === tagValue
        );

        if (hasScheduleTag) {
          console.log(
            `Scaling service ${service.serviceName} to ${desiredCount} tasks`
          );

          // Update the service desired count
          const updateServiceCommand = new UpdateServiceCommand({
            cluster: clusterName,
            service: service.serviceName,
            desiredCount: desiredCount,
          });
          await ecs.send(updateServiceCommand);

          console.log(
            `Successfully scaled service ${service.serviceName} to ${desiredCount} tasks`
          );
        } else {
          console.log(
            `Skipping service ${service.serviceName} as it does not have the required tag`
          );
        }
      }
    }

    return {
      statusCode: 200,
      body: `Successfully processed services in cluster ${clusterName}`,
    };
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};
