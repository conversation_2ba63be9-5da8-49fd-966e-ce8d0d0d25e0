# WhatsApp Manager API - Postman Collection

This directory contains comprehensive Postman collections for testing and documenting the WhatsApp Manager API.

## 📁 Collection Files

### `WhatsApp-Manager-Complete-API.postman_collection.json`
**Main comprehensive collection** containing all API endpoints organized into logical folders:

- 🏥 **Health & Monitoring** (9 endpoints)
- 🔑 **Authentication** (7 endpoints) 
- 📱 **Session Management** (6 endpoints)
- 🔄 **QR Code Management** (2 endpoints)
- 💬 **Message Operations** (2 endpoints)
- ⚙️ **Admin Operations** (3 endpoints)
- 🚨 **Error Testing** (3 endpoints)

**Total: 32 endpoints** with comprehensive test scripts and documentation.

### `WhatsApp-Manager-Complete-API-Part2.json`
Additional endpoints and examples for:
- Extended message operations (media messages, replies)
- Advanced filtering options
- Additional test scenarios

## 🚀 Quick Start

### 1. Import Collection
1. Open Postman
2. Click **Import** button
3. Select `WhatsApp-Manager-Complete-API.postman_collection.json`
4. Collection will be imported with all endpoints and variables

### 2. Configure Environment Variables
The collection includes pre-configured variables:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `baseUrl` | `http://localhost:3000` | API base URL |
| `apiPrefix` | `/api` | API path prefix |
| `userId` | `test-user-123` | Test user ID |
| `phoneNumber` | `<EMAIL>` | WhatsApp number for messaging |
| `webhookUrl` | `https://webhook.site/your-unique-url` | Webhook URL for testing |

**Auto-populated variables:**
- `tokenId` - Auth token ID (from auth responses)
- `jwtToken` - JWT token (from auth responses)  
- `sessionId` - Session ID (from session responses)

### 3. Update Configuration
Before testing, update these variables:
1. Set `baseUrl` to your API server URL
2. Set `phoneNumber` to a valid WhatsApp number
3. Set `webhookUrl` to your webhook endpoint (optional)

## 📋 API Endpoint Categories

### 🏥 Health & Monitoring
Monitor system health and performance:
- **Simple Health Check** - Basic health status
- **Detailed Health Check** - Component-level health
- **Kubernetes Probes** - Readiness and liveness
- **System Metrics** - Performance metrics
- **Monitoring Data** - Real-time monitoring
- **System Alerts** - Alert management
- **Trigger Cleanup** - System maintenance
- **Component Health** - Individual component status

### 🔑 Authentication  
QR token generation and management:
- **Generate QR Link** - Create authentication tokens
- **Get QR Details** - Token information
- **Validate Token** - Token validation
- **Use Token** - Mark token as used
- **Get User Tokens** - List user's tokens
- **Revoke Tokens** - Revoke user tokens
- **Delete Token** - Remove specific token

### 📱 Session Management
WhatsApp session lifecycle:
- **Start Session** - Create new WhatsApp session
- **Get Status** - Session status and health
- **Reconnect** - Reconnect existing session
- **Terminate** - End session
- **List Sessions** - All sessions with pagination
- **Statistics** - Session analytics

### 🔄 QR Code Management
QR code operations for sessions:
- **Refresh QR** - Generate new QR code
- **QR Status** - QR code validity

### 💬 Message Operations
Send and retrieve messages:
- **Send Text** - Send text messages
- **Send Media** - Send images/files
- **Send Reply** - Reply to messages
- **Get Messages** - Retrieve message history

### ⚙️ Admin Operations
Administrative functions:
- **Terminate All** - End all sessions
- **Auth Statistics** - Authentication metrics
- **Cleanup Tokens** - Remove expired tokens

### 🚨 Error Testing
Test error scenarios:
- **Invalid Requests** - Validation errors
- **Not Found** - 404 scenarios
- **Rate Limiting** - Rate limit testing

## 🧪 Testing Features

### Automated Tests
Each endpoint includes test scripts that verify:
- ✅ Response status codes
- ✅ Response structure validation
- ✅ Required fields presence
- ✅ Data type validation
- ✅ Business logic validation

### Variable Auto-Population
Test scripts automatically extract and store:
- Token IDs from auth responses
- Session IDs from session responses
- JWT tokens for subsequent requests

### Response Time Monitoring
Global tests check:
- Response time < 10 seconds
- Valid JSON responses
- Proper content types

## 📊 Rate Limiting Information

The collection includes rate limiting information for each endpoint:

| Endpoint Category | Rate Limit | Window |
|------------------|------------|---------|
| Auth Generation | 10 requests | 15 minutes |
| Auth Validation | 20 requests | 15 minutes |
| Session Operations | 30 requests | 1 minute |
| Message Sending | 60 requests | 1 minute |
| Health Checks | 60 requests | 1 minute |
| Admin Operations | 2-5 requests | 10 minutes |

## 🔧 Advanced Usage

### Environment Setup
For different environments, create Postman environments:

**Development:**
```json
{
  "baseUrl": "http://localhost:3000",
  "apiPrefix": "/api"
}
```

**Staging:**
```json
{
  "baseUrl": "https://staging-api.ezychat.com",
  "apiPrefix": "/api"
}
```

**Production:**
```json
{
  "baseUrl": "https://api.ezychat.com", 
  "apiPrefix": "/api"
}
```

### Automated Testing
Run collection with Newman for CI/CD:

```bash
# Install Newman
npm install -g newman

# Run collection
newman run WhatsApp-Manager-Complete-API.postman_collection.json \
  --environment your-environment.json \
  --reporters cli,json \
  --reporter-json-export results.json
```

### Custom Scripts
Add custom pre-request or test scripts:

**Pre-request Script Example:**
```javascript
// Generate timestamp
pm.collectionVariables.set("timestamp", Date.now());

// Generate random user ID
pm.collectionVariables.set("randomUserId", "user-" + Math.random().toString(36).substr(2, 9));
```

**Test Script Example:**
```javascript
// Custom validation
pm.test("Response contains expected data", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success', true);
    pm.expect(jsonData.data).to.not.be.empty;
});

// Extract data for next requests
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.collectionVariables.set("extractedId", response.data.id);
}
```

## 🛠️ Troubleshooting

### Common Issues

**1. Connection Refused**
- Verify API server is running
- Check `baseUrl` variable
- Confirm port accessibility

**2. Authentication Errors**
- Generate new auth token
- Check token expiration
- Verify JWT configuration

**3. Rate Limiting**
- Wait for rate limit window to reset
- Reduce request frequency
- Check rate limit headers

**4. Validation Errors**
- Verify request body format
- Check required fields
- Validate data types

### Debug Tips

1. **Enable Postman Console** - View detailed request/response logs
2. **Check Variables** - Verify variable values in collection/environment
3. **Review Test Results** - Check test tab for detailed assertions
4. **Monitor Network** - Use browser dev tools for network inspection

## 📚 Documentation

For detailed API documentation, see:
- [Main README](../README.md) - Project overview
- [API Documentation](../docs/) - Detailed endpoint docs
- [Local Development](../LOCAL_DEVELOPMENT_SETUP.md) - Setup guide

## 🤝 Contributing

To add new endpoints to the collection:

1. Create endpoint in appropriate folder
2. Add comprehensive test scripts
3. Include proper documentation
4. Update this README
5. Test thoroughly before committing

## 📄 License

This collection is part of the WhatsApp Manager project and follows the same license terms.
