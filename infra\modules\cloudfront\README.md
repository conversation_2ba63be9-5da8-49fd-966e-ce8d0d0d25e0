<!-- BEGIN_TF_DOCS -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_cloudfront_distribution.s3_distribution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_distribution) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_bucket_name"></a> [bucket\_name](#input\_bucket\_name) | The name of the bucket. | `any` | n/a | yes |
| <a name="input_cloudfront_origin_access_identity"></a> [cloudfront\_origin\_access\_identity](#input\_cloudfront\_origin\_access\_identity) | CloudFront Origin Access Identity for the bucket. | `any` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->