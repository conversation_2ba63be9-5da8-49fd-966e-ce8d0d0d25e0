#!/usr/bin/env ts-node

/**
 * Local DynamoDB Setup Script
 * 
 * This script creates the required DynamoDB tables for local development.
 * In production, tables are managed by Terraform/IaC.
 * 
 * Usage:
 *   npm run setup:local
 *   or
 *   npx ts-node scripts/setup-local-db.ts
 */

import 'reflect-metadata';
import '../src/utils/crypto-polyfill';
import { DynamoDBClient, CreateTableCommand, DescribeTableCommand, ResourceNotFoundException } from '@aws-sdk/client-dynamodb';
import { SESSION_TABLE_DEFINITION } from '../src/infrastructure/database/schema/SessionTableSchema';
import { AUTH_TOKEN_TABLE_DEFINITION } from '../src/infrastructure/database/schema/AuthTokenTableSchema';
import { ConfigService } from '../src/shared/config/config.service';
import { LoggerService } from '../src/shared/logging/logger.service';

interface SetupConfig {
  sessionTableName: string;
  authTokenTableName: string;
  region: string;
  endpoint?: string;
}

class LocalDynamoDBSetup {
  private dynamoClient: DynamoDBClient;
  private logger: LoggerService;
  private config: SetupConfig;

  constructor() {
    // Initialize configuration first
    const configService = new ConfigService();

    // Initialize logger with config
    this.logger = new LoggerService(configService);

    // Load database configuration
    const dbConfig = configService.getDatabase();
    
    // Handle Docker vs local environment
    let endpoint = dbConfig.endpoint;
    if (endpoint && endpoint.includes('dynamodb-local:8000')) {
      // Running outside Docker, use localhost
      endpoint = 'http://localhost:8000';
    }

    this.config = {
      sessionTableName: dbConfig.tableName,
      authTokenTableName: process.env['AUTH_TOKEN_TABLE_NAME'] || 'AuthTokens-dev',
      region: dbConfig.region,
      endpoint: endpoint
    };

    // Initialize DynamoDB client
    const clientConfig = {
      region: this.config.region,
      ...(this.config.endpoint && {
        endpoint: this.config.endpoint
      })
    };
    
    this.dynamoClient = new DynamoDBClient(clientConfig);
    
    this.logger.info('Local DynamoDB setup initialized', {
      sessionTableName: this.config.sessionTableName,
      authTokenTableName: this.config.authTokenTableName,
      region: this.config.region,
      endpoint: this.config.endpoint || 'AWS DynamoDB'
    });
  }

  /**
   * Check if table exists
   */
  async tableExists(tableName: string): Promise<boolean> {
    try {
      await this.dynamoClient.send(new DescribeTableCommand({
        TableName: tableName
      }));
      return true;
    } catch (error: any) {
      if (error instanceof ResourceNotFoundException || error.name === 'ResourceNotFoundException') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Create the sessions table
   */
  async createSessionTable(): Promise<void> {
    const tableDefinition = {
      ...SESSION_TABLE_DEFINITION,
      TableName: this.config.sessionTableName
    } as any; // Type assertion to handle readonly arrays

    this.logger.info('Creating Sessions DynamoDB table...', {
      tableName: this.config.sessionTableName,
      definition: JSON.stringify(tableDefinition, null, 2)
    });

    try {
      await this.dynamoClient.send(new CreateTableCommand(tableDefinition));
      this.logger.info('Sessions table created successfully', {
        tableName: this.config.sessionTableName
      });
    } catch (error: any) {
      this.logger.error('Failed to create sessions table', {
        tableName: this.config.sessionTableName,
        error: error.message,
        errorName: error.name
      });
      throw error;
    }
  }

  /**
   * Create the auth tokens table
   */
  async createAuthTokenTable(): Promise<void> {
    const tableDefinition = {
      ...AUTH_TOKEN_TABLE_DEFINITION,
      TableName: this.config.authTokenTableName
    } as any; // Type assertion to handle readonly arrays

    this.logger.info('Creating Auth Tokens DynamoDB table...', {
      tableName: this.config.authTokenTableName,
      definition: JSON.stringify(tableDefinition, null, 2)
    });

    try {
      await this.dynamoClient.send(new CreateTableCommand(tableDefinition));
      this.logger.info('Auth Tokens table created successfully', {
        tableName: this.config.authTokenTableName
      });
    } catch (error: any) {
      this.logger.error('Failed to create auth tokens table', {
        tableName: this.config.authTokenTableName,
        error: error.message,
        errorName: error.name
      });
      throw error;
    }
  }

  /**
   * Wait for table to become active
   */
  async waitForTableActive(tableName: string): Promise<void> {
    this.logger.info('Waiting for table to become active...', {
      tableName: tableName
    });

    let attempts = 0;
    const maxAttempts = 30; // 30 seconds max wait

    while (attempts < maxAttempts) {
      try {
        const result = await this.dynamoClient.send(new DescribeTableCommand({
          TableName: tableName
        }));

        if (result.Table?.TableStatus === 'ACTIVE') {
          this.logger.info('Table is now active', {
            tableName: tableName
          });
          return;
        }

        this.logger.debug('Table status', {
          tableName: tableName,
          status: result.Table?.TableStatus,
          attempt: attempts + 1
        });

      } catch (error) {
        this.logger.warn('Error checking table status', {
          tableName: tableName,
          error: (error as Error).message,
          attempt: attempts + 1
        });
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    }

    throw new Error(`Table did not become active within ${maxAttempts} seconds`);
  }

  /**
   * Run the complete setup process
   */
  async setup(): Promise<void> {
    try {
      this.logger.info('🚀 Starting local DynamoDB setup...');

      // Setup Sessions Table
      const sessionExists = await this.tableExists(this.config.sessionTableName);

      if (sessionExists) {
        this.logger.info('✅ Sessions table already exists', {
          tableName: this.config.sessionTableName
        });
      } else {
        await this.createSessionTable();
        await this.waitForTableActive(this.config.sessionTableName);
      }

      // Setup Auth Tokens Table
      const authTokenExists = await this.tableExists(this.config.authTokenTableName);

      if (authTokenExists) {
        this.logger.info('✅ Auth Tokens table already exists', {
          tableName: this.config.authTokenTableName
        });
      } else {
        await this.createAuthTokenTable();
        await this.waitForTableActive(this.config.authTokenTableName);
      }

      this.logger.info('🎉 Local DynamoDB setup completed successfully!');
      this.logger.info('📋 Setup Summary:', {
        sessionTableName: this.config.sessionTableName,
        authTokenTableName: this.config.authTokenTableName,
        region: this.config.region,
        endpoint: this.config.endpoint || 'AWS DynamoDB',
        status: 'ACTIVE'
      });
      
    } catch (error) {
      this.logger.error('❌ Local DynamoDB setup failed', {
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      throw error;
    }
  }

  /**
   * Cleanup - close connections
   */
  async cleanup(): Promise<void> {
    try {
      this.dynamoClient.destroy();
      this.logger.info('Cleanup completed');
    } catch (error) {
      this.logger.warn('Cleanup warning', {
        error: (error as Error).message
      });
    }
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const setup = new LocalDynamoDBSetup();
  
  try {
    await setup.setup();
    process.exit(0);
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  } finally {
    await setup.cleanup();
  }
}

// Handle process signals
process.on('SIGINT', async () => {
  console.log('\n🛑 Setup interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Setup terminated');
  process.exit(1);
});

// Run if this file is executed directly
if (require.main === module) {
  console.log('🔧 Local DynamoDB Setup Script');
  console.log('===============================\n');
  
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
