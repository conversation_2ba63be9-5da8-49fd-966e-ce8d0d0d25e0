import { EventEmitter } from 'events';
import { inject, injectable } from 'tsyringe';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import {
  IWhatsAppService,
  StartSessionResult,
  SessionConnectionState
} from '../../domain/services/IWhatsAppService';
import { SessionEntity, SessionStatus } from '../../domain/entities/SessionEntity';
import { BaileysConnectionManager } from './BaileysConnectionManager';

import { QRCodeManager } from './QRCodeManager';

import { RateLimiter } from './RateLimiter';
import { BaileysEventEmitter } from './BaileysEventEmitter';
import { ConnectionPool } from './ConnectionPool';
import { HealthChecker } from './HealthChecker';

/**
 * Production-ready WhatsApp service implementation using Bailey<PERSON>
 * Orchestrates all WhatsApp-related operations with real connections
 */
@injectable()
export class WhatsAppService extends EventEmitter implements IWhatsAppService {
  private readonly sessionStates = new Map<string, SessionConnectionState>();
  private readonly maxConcurrentSessions: number;
  private readonly qrTimeoutMs: number;
  private readonly reconnectAttempts: number;


  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('BaileysConnectionManager') private connectionManager: BaileysConnectionManager,
    @inject('QRCodeManager') private qrCodeManager: QRCodeManager,
    @inject('RateLimiter') private rateLimiter: RateLimiter,
    @inject('BaileysEventEmitter') private eventEmitter: BaileysEventEmitter,
    @inject('ConnectionPool') private connectionPool: ConnectionPool,
    @inject('HealthChecker') private healthChecker: HealthChecker,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private configService: IConfigService
  ) {
    super();

    // Load configuration
    this.maxConcurrentSessions = this.configService.getOptional('MAX_CONCURRENT_SESSIONS', 100);
    this.qrTimeoutMs = this.configService.getOptional('QR_TOKEN_EXPIRY_SEC', 300) * 1000;
    this.reconnectAttempts = this.configService.getOptional('RECONNECT_MAX_ATTEMPTS', 5);

    // Setup event forwarding from internal event emitter
    this.setupEventForwarding();

    // Setup connection manager event handlers
    this.setupConnectionEventHandlers();

    this.logger.info('Production WhatsAppService initialized', {
      maxConcurrentSessions: this.maxConcurrentSessions,
      qrTimeoutMs: this.qrTimeoutMs,
      reconnectAttempts: this.reconnectAttempts
    });
  }

  /**
   * Start a new WhatsApp session with real Baileys connection
   */
  async startSession(userId: string): Promise<StartSessionResult> {
    try {
      this.logger.info('Starting WhatsApp session', { userId });

      // Validate input
      this.validateUserId(userId);

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkLimits(userId, 'session_creation');
      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded for session creation. Try again after ${rateLimitResult.userLimit.resetTime.toISOString()}`);
      }

      // Check if session already exists and is active
      const existingConnection = this.connectionManager.getConnection(userId);
      if (existingConnection) {
        this.logger.warn('Session already exists', {
          userId,
          status: existingConnection.status
        });

        // Return existing session info
        const qrCode = existingConnection.qrCode;
        return {
          sessionId: userId,
          status: existingConnection.status,
          phoneNumber: existingConnection.phoneNumber,
          qrCode
        };
      }

      // Get or create session entity
      let session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        // Create new session
        session = SessionEntity.create(userId);
        await this.sessionRepository.saveSession(session);
        this.logger.info('New session entity created', {
          userId,
          sessionId: session.sessionId
        });
      }

      // Acquire connection from pool
      // const pooledConnection = await this.connectionPool.acquireConnection(userId, 1); // Unused for now

      // Create real Baileys connection
      const connectionInfo = await this.connectionManager.createConnection(userId, {
        printQRInTerminal: false,
        browser: ['WhatsApp Manager', 'Chrome', '1.0.0'],
        syncFullHistory: false,
        markOnlineOnConnect: true
      });

      // Initialize session state
      const sessionState: SessionConnectionState = {
        userId,
        status: connectionInfo.status,
        reconnectAttempts: 0,
        lastActivity: new Date()
      };
      this.sessionStates.set(userId, sessionState);

      // Update session status
      await this.sessionRepository.updateSessionStatus(userId, connectionInfo.status);

      // Update connection pool
      this.connectionPool.updateConnectionStatus(userId, connectionInfo.status);

      this.logger.info('WhatsApp connection created, waiting for QR generation', {
        userId,
        sessionId: session.sessionId,
        status: connectionInfo.status,
        hasQR: !!connectionInfo.qrCode
      });

      // Wait for QR code generation if connection is initializing or connecting
      let finalConnectionInfo = connectionInfo;
      if (connectionInfo.status === 'initializing' || connectionInfo.status === 'connecting') {
        this.logger.info('Waiting for QR code generation', { userId });

        try {
          finalConnectionInfo = await this.waitForQRGenerationWithRetry(userId, 15000, 2); // 15 second timeout, 2 retries
          this.logger.info('QR code generation completed', {
            userId,
            status: finalConnectionInfo.status,
            hasQR: !!finalConnectionInfo.qrCode
          });
        } catch (error) {
          this.logger.warn('QR code generation failed after retries', {
            userId,
            error: (error as Error).message
          });

          // Try to get current connection state as fallback
          const currentConnection = this.connectionManager.getConnection(userId);
          if (currentConnection) {
            finalConnectionInfo = currentConnection;
            this.logger.info('Using current connection state as fallback', {
              userId,
              status: currentConnection.status,
              hasQR: !!currentConnection.qrCode
            });
          }
        }
      }

      this.logger.info('WhatsApp session started successfully', {
        userId,
        sessionId: session.sessionId,
        status: finalConnectionInfo.status,
        hasQR: !!finalConnectionInfo.qrCode
      });

      return {
        sessionId: session.sessionId,
        status: finalConnectionInfo.status,
        phoneNumber: finalConnectionInfo.phoneNumber,
        qrCode: finalConnectionInfo.qrCode
      };

    } catch (error) {
      this.logger.error('Failed to start WhatsApp session', {
        userId,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'session_creation');

      // Cleanup on failure
      this.sessionStates.delete(userId);
      
      throw error;
    }
  }

  /**
   * Wait for QR code generation with retry logic
   */
  private async waitForQRGenerationWithRetry(userId: string, timeoutMs: number, maxRetries: number): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        this.logger.info('Attempting QR generation wait', {
          userId,
          attempt,
          maxAttempts: maxRetries + 1,
          timeoutMs
        });

        const result = await this.waitForQRGeneration(userId, timeoutMs);

        this.logger.info('QR generation successful', {
          userId,
          attempt,
          status: result.status,
          hasQR: !!result.qrCode
        });

        return result;

      } catch (error) {
        lastError = error as Error;

        this.logger.warn('QR generation attempt failed', {
          userId,
          attempt,
          error: lastError.message
        });

        // If this is not the last attempt, try to recreate the connection
        if (attempt <= maxRetries) {
          this.logger.info('Retrying QR generation with connection refresh', { userId, attempt });

          try {
            // Force a connection refresh by creating a new connection
            await this.refreshConnection(userId);

            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));

          } catch (refreshError) {
            this.logger.warn('Connection refresh failed', {
              userId,
              attempt,
              error: (refreshError as Error).message
            });
          }
        }
      }
    }

    // All attempts failed
    throw new Error(`QR generation failed after ${maxRetries + 1} attempts. Last error: ${lastError?.message}`);
  }

  /**
   * Wait for QR code generation with timeout (single attempt)
   */
  private async waitForQRGeneration(userId: string, timeoutMs: number): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.connectionManager.removeListener('connection.update', eventHandler);
        reject(new Error(`QR generation timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      const eventHandler = (eventData: any) => {
        if (eventData.userId === userId && eventData.event === 'qr') {
          clearTimeout(timeout);
          this.connectionManager.removeListener('connection.update', eventHandler);

          // Get updated connection info
          const updatedConnection = this.connectionManager.getConnection(userId);
          if (updatedConnection) {
            resolve(updatedConnection);
          } else {
            reject(new Error('Connection not found after QR generation'));
          }
        }
      };

      // Listen for QR generation event
      this.connectionManager.on('connection.update', eventHandler);

      // Also check if QR is already available (race condition protection)
      const currentConnection = this.connectionManager.getConnection(userId);
      if (currentConnection && currentConnection.qrCode) {
        clearTimeout(timeout);
        this.connectionManager.removeListener('connection.update', eventHandler);
        resolve(currentConnection);
      }
    });
  }

  /**
   * Refresh connection by recreating it
   */
  private async refreshConnection(userId: string): Promise<void> {
    this.logger.info('Refreshing connection for QR retry', { userId });

    try {
      // Close existing connection using the connection manager
      await this.connectionManager.closeConnection(userId);

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create new connection
      await this.connectionManager.createConnection(userId, {
        printQRInTerminal: false,
        browser: ['WhatsApp Manager', 'Chrome', '1.0.0'],
        syncFullHistory: false,
        markOnlineOnConnect: true
      });

      this.logger.info('Connection refreshed successfully', { userId });

    } catch (error) {
      this.logger.error('Failed to refresh connection', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Setup event forwarding from internal event emitter to this service
   */
  private setupEventForwarding(): void {
    // Forward QR events
    this.eventEmitter.addEventListener('qr', (data) => {
      this.emit('qr', data.userId, data.qrCode);
    });

    // Forward connection events
    this.eventEmitter.addEventListener('connecting', (data) => {
      this.emit('connecting', data.userId);
    });

    this.eventEmitter.addEventListener('connected', (data) => {
      this.emit('connected', data.userId, data.phoneNumber);
    });

    this.eventEmitter.addEventListener('disconnected', (data) => {
      this.emit('disconnected', data.userId, data.reason);
    });

    // Forward error events
    this.eventEmitter.addEventListener('error', (data) => {
      this.emit('error', data.userId, data.error);
    });

    // Forward message events
    this.eventEmitter.addEventListener('message', (data) => {
      this.emit('message', data.userId, data.messageData);
    });

    this.logger.debug('Event forwarding setup completed');
  }

  /**
   * Setup connection manager event handlers
   */
  private setupConnectionEventHandlers(): void {
    // Listen to BaileysConnectionManager events
    this.connectionManager.on('connection.update', async (eventData: {
      userId: string;
      event: string;
      data: any;
      timestamp: Date;
    }) => {
      try {
        const { userId, event, data } = eventData;
        
        this.logger.debug('Processing connection event', {
          userId,
          event,
          hasData: !!data
        });

        // Update session status based on event
        switch (event) {
          case 'qr':
            await this.sessionRepository.updateSessionStatus(userId, 'qr_pending');
            this.logger.info('Session status updated to qr_pending', { userId });
            break;
            
          case 'connected':
            await this.sessionRepository.updateSessionStatus(userId, 'connected');
            // Update phone number if available
            if (data?.phoneNumber) {
              const session = await this.sessionRepository.findByUserId(userId);
              if (session) {
                session.phoneNumber = data.phoneNumber;
                await this.sessionRepository.saveSession(session);
              }
            }
            this.logger.info('Session status updated to connected', { 
              userId, 
              phoneNumber: data?.phoneNumber 
            });
            break;
            
          case 'connecting':
            await this.sessionRepository.updateSessionStatus(userId, 'connecting');
            this.logger.info('Session status updated to connecting', { userId });
            break;
            
          case 'disconnected':
            await this.sessionRepository.updateSessionStatus(userId, 'disconnected');
            this.logger.info('Session status updated to disconnected', { 
              userId, 
              reason: data?.reason 
            });
            break;
            
          default:
            this.logger.debug('Unhandled connection event', { userId, event });
        }

        // Forward event to external listeners
        this.emit(`session.${event}`, { userId, data });
        
      } catch (error) {
        this.logger.error('Failed to handle connection event', {
          userId: eventData.userId,
          event: eventData.event,
          error: (error as Error).message
        });
      }
    });

    this.logger.debug('Connection event handlers setup completed');
  }

  /**
   * Validate user ID format
   */
  private validateUserId(userId: string): void {
    if (!userId || userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (userId.length > 100) {
      throw new Error('User ID is too long');
    }

    // Basic format validation
    if (!/^[a-zA-Z0-9_-]+$/.test(userId)) {
      throw new Error('User ID contains invalid characters');
    }
  }

  /**
   * Reconnect an existing session with production logic
   */
  async reconnectSession(userId: string): Promise<StartSessionResult> {
    try {
      this.logger.info('Reconnecting WhatsApp session', { userId });

      // Validate input
      this.validateUserId(userId);

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkLimits(userId, 'session_creation');
      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded for reconnection. Try again after ${rateLimitResult.userLimit.resetTime.toISOString()}`);
      }

      // Check if connection exists
      const existingConnection = this.connectionManager.getConnection(userId);
      if (existingConnection) {
        // If already connected, return current status
        if (existingConnection.status === 'connected') {
          this.logger.info('Session already connected', { userId });
          return {
            sessionId: userId,
            status: existingConnection.status,
            phoneNumber: existingConnection.phoneNumber,
            qrCode: existingConnection.qrCode
          };
        }

        // Try to close existing connection first
        try {
          await this.connectionManager.closeConnection(userId);
          this.logger.info('Closed existing connection for reconnection', { userId });
        } catch (error) {
          this.logger.warn('Failed to close existing connection', {
            userId,
            error: (error as Error).message
          });
        }
      }

      // Release from connection pool if exists
      this.connectionPool.removeConnection(userId);

      // Start new session (which will use existing auth state if available)
      const result = await this.startSession(userId);

      this.logger.info('Session reconnected successfully', {
        userId,
        status: result.status
      });

      return result;

    } catch (error) {
      this.logger.error('Failed to reconnect WhatsApp session', {
        userId,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'session_creation');

      throw error;
    }
  }

  /**
   * Regenerate QR code for pending session with production logic
   */
  async regenerateQR(userId: string): Promise<{ qrCode: string }> {
    try {
      this.logger.info('Regenerating QR code', { userId });

      // Validate input
      this.validateUserId(userId);

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'qr_generation');
      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded for QR generation. Try again after ${rateLimitResult.resetTime.toISOString()}`);
      }

      // Check if connection exists
      const connection = this.connectionManager.getConnection(userId);
      if (!connection) {
        throw new Error(`No active session found for user: ${userId}`);
      }

      // Check session status
      if (connection.status === 'connected') {
        throw new Error('Session is already connected. QR regeneration not needed.');
      }

      if (connection.status !== 'qr_pending' && connection.status !== 'initializing') {
        throw new Error(`Session is not in a state that requires QR code: ${connection.status}`);
      }

      // Close existing connection and create new one to get fresh QR
      await this.connectionManager.closeConnection(userId);

      // Create new connection which should generate new QR
      const newConnection = await this.connectionManager.createConnection(userId);

      // Update session state
      const sessionState = this.sessionStates.get(userId);
      if (sessionState) {
        sessionState.status = newConnection.status;
        sessionState.lastActivity = new Date();
      }

      // Update session status in database
      await this.sessionRepository.updateSessionStatus(userId, newConnection.status);

      // Update connection pool
      this.connectionPool.updateConnectionStatus(userId, newConnection.status);

      if (!newConnection.qrCode) {
        throw new Error('Failed to generate QR code');
      }

      this.logger.info('QR code regenerated successfully', {
        userId,
        status: newConnection.status
      });

      return { qrCode: newConnection.qrCode };

    } catch (error) {
      this.logger.error('Failed to regenerate QR code', {
        userId,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'qr_generation');

      throw error;
    }
  }

  /**
   * Get current session status with production logic
   */
  async getSessionStatus(userId: string): Promise<SessionStatus> {
    try {
      // Validate input
      this.validateUserId(userId);

      // Check active connection first
      const connection = this.connectionManager.getConnection(userId);
      if (connection) {
        // Update last activity
        const sessionState = this.sessionStates.get(userId);
        if (sessionState) {
          sessionState.lastActivity = new Date();
        }

        return connection.status;
      }

      // Check database for persisted session
      const session = await this.sessionRepository.findByUserId(userId);
      return session ? session.status : 'disconnected';

    } catch (error) {
      this.logger.error('Failed to get session status', {
        userId,
        error: (error as Error).message
      });
      return 'error';
    }
  }

  /**
   * Terminate and cleanup session with production logic
   */
  async terminateSession(userId: string): Promise<void> {
    try {
      this.logger.info('Terminating WhatsApp session', { userId });

      // Validate input
      this.validateUserId(userId);

      // Close connection through connection manager
      const connection = this.connectionManager.getConnection(userId);
      if (connection) {
        try {
          await this.connectionManager.closeConnection(userId);
          this.logger.info('Connection closed successfully', { userId });
        } catch (error) {
          this.logger.warn('Error during connection disconnect', {
            userId,
            error: (error as Error).message
          });
        }
      }

      // Remove from connection pool
      this.connectionPool.removeConnection(userId);

      // Cleanup session state
      this.sessionStates.delete(userId);

      // Update database
      try {
        await this.sessionRepository.updateSessionStatus(userId, 'disconnected', {
          disconnectedAt: new Date()
        });
        this.logger.info('Session status updated in database', { userId });
      } catch (error) {
        this.logger.warn('Failed to update session status in database', {
          userId,
          error: (error as Error).message
        });
      }

      // Emit disconnected event
      this.eventEmitter.emitConnectionEvent(userId, 'disconnected', undefined, 'manual_termination');

      this.logger.info('WhatsApp session terminated successfully', { userId });

    } catch (error) {
      this.logger.error('Failed to terminate WhatsApp session', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Restore all persisted sessions on startup with production logic
   */
  async restoreAllSessions(): Promise<number> {
    try {
      this.logger.info('Restoring persisted sessions');

      const activeSessions = await this.sessionRepository.findAllActive();
      let restoredCount = 0;
      let failedCount = 0;

      this.logger.info('Found sessions to restore', {
        totalSessions: activeSessions.length
      });

      for (const session of activeSessions) {
        try {
          // Check if session can be reconnected
          if (session.canReconnect()) {
            // Check if we have capacity
            if (!this.connectionPool.hasCapacity()) {
              this.logger.warn('Connection pool at capacity, skipping session restore', {
                userId: session.userId
              });
              continue;
            }

            // Attempt to restore session
            await this.startSession(session.userId);
            restoredCount++;

            this.logger.debug('Session restored successfully', {
              userId: session.userId
            });
          } else {
            // Mark expired sessions as disconnected
            await this.sessionRepository.updateSessionStatus(session.userId, 'disconnected');
            this.logger.debug('Expired session marked as disconnected', {
              userId: session.userId
            });
          }
        } catch (error) {
          failedCount++;
          this.logger.warn('Failed to restore session', {
            userId: session.userId,
            error: (error as Error).message
          });

          // Mark failed sessions as error state
          try {
            await this.sessionRepository.updateSessionStatus(session.userId, 'error');
          } catch (updateError) {
            this.logger.error('Failed to update failed session status', {
              userId: session.userId,
              error: (updateError as Error).message
            });
          }
        }
      }

      this.logger.info('Session restoration completed', {
        totalSessions: activeSessions.length,
        restoredCount,
        failedCount,
        skippedCount: activeSessions.length - restoredCount - failedCount
      });

      return restoredCount;

    } catch (error) {
      this.logger.error('Failed to restore sessions', {
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Check if session is active and connected with production logic
   */
  async isSessionActive(userId: string): Promise<boolean> {
    try {
      this.validateUserId(userId);

      const connection = this.connectionManager.getConnection(userId);
      return connection ? connection.status === 'connected' : false;
    } catch (error) {
      this.logger.error('Failed to check session active status', {
        userId,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get active session count with production logic
   */
  async getActiveSessionCount(): Promise<number> {
    try {
      const connections = this.connectionManager.getAllConnections();
      return Array.from(connections.values())
        .filter(conn => conn.status === 'connected').length;
    } catch (error) {
      this.logger.error('Failed to get active session count', {
        error: (error as Error).message
      });
      return 0;
    }
  }

  /**
   * Get all active user IDs with production logic
   */
  async getActiveUserIds(): Promise<string[]> {
    try {
      const connections = this.connectionManager.getAllConnections();
      return Array.from(connections.values())
        .filter(conn => conn.status === 'connected')
        .map(conn => conn.userId);
    } catch (error) {
      this.logger.error('Failed to get active user IDs', {
        error: (error as Error).message
      });
      return [];
    }
  }

  /**
   * Send a test message with production logic
   */
  async sendTestMessage(userId: string, phoneNumber: string, message: string): Promise<void> {
    try {
      this.logger.info('Sending test message', { userId, phoneNumber });

      // Validate inputs
      this.validateUserId(userId);
      if (!phoneNumber || !phoneNumber.includes('@')) {
        throw new Error('Invalid phone number format');
      }
      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'messages');
      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded for messages. Try again after ${rateLimitResult.resetTime.toISOString()}`);
      }

      // Get connection
      const connection = this.connectionManager.getConnection(userId);
      if (!connection) {
        throw new Error(`No active session found for user: ${userId}`);
      }

      if (connection.status !== 'connected') {
        throw new Error(`Session is not connected: ${connection.status}`);
      }

      // Send message through connection manager
      await this.connectionManager.sendMessage(userId, phoneNumber, { text: message });

      this.logger.info('Test message sent successfully', {
        userId,
        phoneNumber: phoneNumber.split('@')[0] + '@***' // Mask phone number in logs
      });

    } catch (error) {
      this.logger.error('Failed to send test message', {
        userId,
        phoneNumber: phoneNumber?.split('@')[0] + '@***',
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'messages');

      throw error;
    }
  }

  /**
   * Get system statistics
   */
  async getSystemStatistics(): Promise<{
    totalSessions: number;
    activeSessions: number;
    connectionPoolStats: any;
    rateLimitStats: any;
    healthStatus: string;
  }> {
    try {
      const connections = this.connectionManager.getAllConnections();
      const totalSessions = connections.size;
      const activeSessions = Array.from(connections.values())
        .filter(conn => conn.status === 'connected').length;

      const connectionPoolStats = this.connectionPool.getStatistics();
      const rateLimitStats = this.rateLimiter.getStatistics();
      const systemHealth = await this.healthChecker.getSystemHealth();

      return {
        totalSessions,
        activeSessions,
        connectionPoolStats,
        rateLimitStats,
        healthStatus: systemHealth.status
      };

    } catch (error) {
      this.logger.error('Failed to get system statistics', {
        error: (error as Error).message
      });

      return {
        totalSessions: 0,
        activeSessions: 0,
        connectionPoolStats: {},
        rateLimitStats: {},
        healthStatus: 'unhealthy'
      };
    }
  }

  /**
   * Graceful shutdown of all sessions
   */
  async shutdown(): Promise<void> {
    try {
      this.logger.info('Starting graceful shutdown of WhatsApp service');

      // Get all active connections
      const connections = this.connectionManager.getAllConnections();
      const userIds = Array.from(connections.keys());

      this.logger.info('Terminating active sessions', {
        sessionCount: userIds.length
      });

      // Terminate all sessions
      const terminationPromises = userIds.map(userId =>
        this.terminateSession(userId).catch(error => {
          this.logger.error('Failed to terminate session during shutdown', {
            userId,
            error: (error as Error).message
          });
        })
      );

      await Promise.allSettled(terminationPromises);

      // Destroy components
      await this.connectionManager.destroy();
      this.connectionPool.destroy();
      this.rateLimiter.destroy();
      this.eventEmitter.destroy();
      this.qrCodeManager.destroy();
      this.healthChecker.destroy();

      // Clear internal state
      this.sessionStates.clear();

      this.logger.info('WhatsApp service shutdown completed');

    } catch (error) {
      this.logger.error('Error during WhatsApp service shutdown', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Health check method
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      const systemHealth = await this.healthChecker.getSystemHealth();

      return {
        status: systemHealth.status,
        details: {
          uptime: systemHealth.uptime,
          activeSessions: systemHealth.metrics.activeSessions,
          totalSessions: systemHealth.metrics.totalSessions,
          memoryUsage: systemHealth.metrics.memoryUsage,
          components: systemHealth.components.map(c => ({
            name: c.name,
            status: c.status,
            responseTime: c.responseTime
          }))
        }
      };

    } catch (error) {
      this.logger.error('Health check failed', {
        error: (error as Error).message
      });

      return {
        status: 'unhealthy',
        details: {
          error: (error as Error).message
        }
      };
    }
  }
}
