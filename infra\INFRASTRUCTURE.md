# EzyChat Infrastructure Documentation

## Overview

This document describes the infrastructure architecture, modules, and CI/CD setup for the EzyChat platform. The infrastructure is built using Terraform with a modular approach and managed through Terraform Cloud.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    EzyChat Infrastructure                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Terraform     │  │   Terraform     │  │   Terraform     │  │
│  │     Cloud       │  │     Cloud       │  │     Cloud       │  │
│  │   Workspaces    │  │   Workspaces    │  │   Workspaces    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│          │                      │                      │          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  UAT Environment│  │ Prod Environment│  │ Cloudflare CDN  │  │
│  │    (AWS)        │  │    (AWS)        │  │    & DNS        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
ezychat/
├── iac/                              # Infrastructure as Code
│   ├── aws-ezychat-uat-tf/          # UAT Environment
│   │   ├── configs/                  # YAML Configuration Files
│   │   │   ├── ecs_services.yaml    # ECS Service Definitions
│   │   │   └── lambda_services.yaml # Lambda Function Definitions
│   │   ├── certificates.tf          # SSL/TLS Certificates
│   │   ├── config.yaml              # Environment Configuration
│   │   ├── ecs_app_platform.tf      # ECS Application Platform
│   │   ├── ecs_scheduler.tf         # ECS Scheduled Tasks
│   │   ├── lambda_platform.tf       # Lambda Functions Platform
│   │   ├── provider.tf              # Terraform Provider Configuration
│   │   ├── route53.tf               # DNS Configuration
│   │   └── Makefile                 # Build Commands
│   ├── aws-ezychat-prod-tf/         # Production Environment
│   ├── cloudflare-master-tf/        # Cloudflare Configuration
│   └── tfc-workspace-tf/            # Terraform Cloud Workspace Management
│       ├── configs/
│       │   ├── projects.yaml        # TFC Project Definitions
│       │   └── workspaces.yaml      # TFC Workspace Definitions
│       └── *.tf                     # Workspace Management Code
├── modules/                          # Reusable Terraform Modules
│   ├── acm_certificate/             # SSL Certificate Module
│   ├── alb/                         # Application Load Balancer
│   ├── cloudfront/                  # CloudFront Distribution
│   ├── ec2/                         # EC2 Instance Module
│   ├── ecs_app_platform/            # ECS Application Platform
│   ├── ecs_scheduler/               # ECS Scheduled Tasks
│   ├── lambda-app/                  # Lambda Function Module
│   └── s3_bucket/                   # S3 Bucket Module
└── tfshares/                        # Shared Terraform Configuration
    └── provider.tf                  # Shared Provider Configuration
```

## Infrastructure Components

### 1. Environment Structure

The infrastructure is organized into separate environments:

- **UAT Environment** (`aws-ezychat-uat-tf/`): Development and testing environment
- **Production Environment** (`aws-ezychat-prod-tf/`): Production environment
- **Cloudflare** (`cloudflare-master-tf/`): CDN and DNS management

### 2. Core Platforms

#### ECS Application Platform
- **File**: `ecs_app_platform.tf`
- **Configuration**: `configs/ecs_services.yaml`
- **Purpose**: Runs containerized applications on ECS with Application Load Balancer
- **Features**:
  - Auto-scaling ECS services
  - Application Load Balancer with SSL termination
  - Service discovery and routing
  - Health checks and monitoring

#### Lambda Platform
- **File**: `lambda_platform.tf`
- **Configuration**: `configs/lambda_services.yaml`
- **Purpose**: Serverless functions for event-driven processing
- **Features**:
  - API Gateway integration
  - EventBridge and SQS triggers
  - Custom domains and routing
  - VPC connectivity for database access

#### ECS Scheduler
- **File**: `ecs_scheduler.tf`
- **Purpose**: Runs scheduled tasks and batch jobs
- **Features**:
  - EventBridge scheduled rules
  - Lambda-triggered ECS tasks
  - Resource scheduling and scaling

### 3. Shared Infrastructure

#### Networking
- **VPC**: Uses default VPC with public subnets
- **Load Balancer**: Application Load Balancer with SSL termination
- **DNS**: Route53 hosted zones with custom domains

#### Security
- **Certificates**: ACM certificates for SSL/TLS
- **IAM**: Role-based access with least privilege
- **Secrets**: AWS Secrets Manager for sensitive data

## Configuration Management

### YAML-Driven Configuration

The infrastructure uses YAML files for declarative service configuration:

#### ECS Services (`configs/ecs_services.yaml`)
```yaml
whatsapp-personal-api:
  container_image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-personal-api:latest"
  container_port: 3000
  path_patterns:
    - "/ws"
    - "/whatsapp-api*"
  health_check_path: "/whatsapp-api/health"
  task_cpu: 256
  task_memory: 512
  environment_variables:
    NODE_ENV: "production"
  secrets:
    "API_KEY": "arn:aws:secretsmanager:..."
```

#### Lambda Services (`configs/lambda_services.yaml`)
```yaml
whatsapp-personal-lambda:
  image_repository_uri: "************.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-personal-lambda"
  timeout: 30
  memory_size: 512
  api_gateway:
    stage_name: "v1"
    routes:
      - path: "/whatsapp-api/{proxy+}"
        method: "ANY"
```

### Benefits of YAML Configuration
- **Separation of Concerns**: Infrastructure code separate from service configuration
- **Non-Technical Access**: Product teams can modify service settings
- **Version Control**: Configuration changes tracked in Git
- **Consistency**: Standardized service definitions across environments

## CI/CD Pipeline

### Terraform Cloud Integration

The infrastructure uses Terraform Cloud for automated deployment:

#### Workspace Configuration (`configs/workspaces.yaml`)
```yaml
aws-ezychat-uat-tf:
  name: aws-ezychat-uat
  description: Manage all AWS resources for EzyChat UAT environment
  working_directory: iac/aws-ezychat-uat-tf
  auto_apply: true
  project_id: aws-project
  trigger_prefixes:
    - iac/aws-ezychat-uat-tf
  vcs_repo:
    branch: main
    identifier: anchorsprint/company-master
```

#### Authentication & Authorization
- **AWS Provider Auth**: Uses OIDC for secure authentication
- **Role Assumption**: Cross-account role assumption for deployment
- **Permissions**: Least privilege IAM roles for Terraform execution

### Deployment Flow

1. **Code Changes**: Developer pushes changes to Git repository
2. **Trigger**: Terraform Cloud detects changes in configured paths
3. **Plan**: Terraform generates execution plan
4. **Apply**: Auto-apply enabled for UAT, manual approval for Production
5. **Validation**: Post-deployment validation and monitoring

### Environment Management

#### UAT Environment
- **Auto-apply**: Enabled for rapid iteration
- **Trigger Path**: `iac/aws-ezychat-uat-tf`
- **Purpose**: Development and testing

#### Production Environment
- **Auto-apply**: Disabled for safety
- **Trigger Path**: `iac/aws-ezychat-prod-tf`
- **Purpose**: Production workloads

## Terraform Modules

### Core Modules

#### `ecs_app_platform`
- **Purpose**: Creates ECS cluster with ALB and auto-scaling
- **Inputs**: VPC, subnets, service configurations
- **Outputs**: Cluster ARN, ALB DNS name, service endpoints

#### `lambda-app`
- **Purpose**: Creates Lambda function with API Gateway
- **Inputs**: Container image, environment variables, API configuration
- **Outputs**: Function ARN, API URL, custom domain

#### `alb`
- **Purpose**: Application Load Balancer with SSL termination
- **Inputs**: VPC, subnets, certificate ARN
- **Outputs**: ALB DNS name, target group ARNs

#### `acm_certificate`
- **Purpose**: SSL/TLS certificate provisioning
- **Inputs**: Domain name, Route53 zone
- **Outputs**: Certificate ARN, validation records

### Module Dependencies

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ acm_certificate │────│       alb       │────│ ecs_app_platform│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│  lambda-app     │──────────────┘
                        └─────────────────┘
```

## Security Considerations

### Network Security
- **VPC Isolation**: Resources deployed in isolated VPC
- **Security Groups**: Restrictive inbound/outbound rules
- **SSL/TLS**: End-to-end encryption for all traffic

### Identity & Access Management
- **Role-Based Access**: Separate roles for each service
- **Cross-Account Access**: Secure role assumption for deployment
- **Secrets Management**: AWS Secrets Manager for sensitive data

### Monitoring & Logging
- **CloudWatch**: Centralized logging and metrics
- **X-Ray Tracing**: Distributed tracing for Lambda functions
- **Container Insights**: ECS cluster monitoring

## Operations

### Local Development

Use the provided Makefile for local Terraform operations:

```bash
make init      # Initialize Terraform
make plan      # Generate execution plan
make validate  # Validate configuration
make apply     # Apply changes
```

### Adding New Services

#### ECS Service
1. Add service definition to `configs/ecs_services.yaml`
2. Commit changes to trigger deployment
3. Monitor deployment in Terraform Cloud

#### Lambda Function
1. Add function definition to `configs/lambda_services.yaml`
2. Configure triggers (API Gateway, EventBridge, SQS)
3. Commit changes to trigger deployment

### Troubleshooting

#### Common Issues
- **Certificate Validation**: Ensure DNS records are properly configured
- **Role Permissions**: Verify IAM roles have required permissions
- **VPC Configuration**: Check subnet and security group settings

#### Monitoring
- **Terraform Cloud**: Check workspace runs and logs
- **AWS CloudWatch**: Monitor application metrics and logs
- **Health Checks**: Verify service health endpoints

## Best Practices

### Configuration Management
- **Version Control**: All configuration in Git
- **Environment Parity**: Consistent configuration across environments
- **Secret Management**: Use AWS Secrets Manager for sensitive data

### Infrastructure as Code
- **Modular Design**: Reusable modules for common patterns
- **State Management**: Centralized state in Terraform Cloud
- **Change Management**: Automated planning and approval workflows

### Security
- **Least Privilege**: Minimal required permissions
- **Encryption**: Encrypt data in transit and at rest
- **Monitoring**: Comprehensive logging and alerting

## Future Enhancements

### Planned Improvements
- **Multi-Region Deployment**: Expand to multiple AWS regions
- **Container Registry**: Implement ECR lifecycle policies
- **Database Management**: Add RDS module for database resources
- **Monitoring**: Enhanced observability with custom metrics

### Scalability Considerations
- **Auto-scaling**: Implement predictive scaling
- **Cost Optimization**: Resource right-sizing and scheduling
- **Performance**: CDN optimization and caching strategies