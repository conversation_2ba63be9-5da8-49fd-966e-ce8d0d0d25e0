import { Request, Response, NextFunction } from 'express';

/**
 * Rate limiting middleware options
 */
export interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  message: string;
}

/**
 * Simple in-memory rate limiting middleware
 * In production, use redis-based rate limiting
 */
export function rateLimitMiddleware(options: RateLimitOptions) {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - options.windowMs;

    // Clean up old entries
    for (const [ip, data] of requests.entries()) {
      if (data.resetTime < windowStart) {
        requests.delete(ip);
      }
    }

    // Get or create request data
    let requestData = requests.get(key);
    if (!requestData || requestData.resetTime < windowStart) {
      requestData = { count: 0, resetTime: now + options.windowMs };
      requests.set(key, requestData);
    }

    // Check rate limit
    if (requestData.count >= options.maxRequests) {
      res.status(429).json({
        success: false,
        error: {
          message: options.message,
          code: 'RATE_LIMIT_EXCEEDED',
          statusCode: 429,
          retryAfter: Math.ceil((requestData.resetTime - now) / 1000)
        }
      });
      return;
    }

    // Increment counter
    requestData.count++;

    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': options.maxRequests.toString(),
      'X-RateLimit-Remaining': (options.maxRequests - requestData.count).toString(),
      'X-RateLimit-Reset': new Date(requestData.resetTime).toISOString()
    });

    next();
  };
}
