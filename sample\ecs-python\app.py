#!/usr/bin/env python3

from flask import Flask, jsonify
import os
import datetime
import socket

app = Flask(__name__)

# Configuration
PORT = int(os.environ.get('PORT', 5000))
HOST = os.environ.get('HOST', '0.0.0.0')
SERVICE_NAME = os.environ.get('SERVICE_NAME', 'ecs-python')
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'development')

@app.route('/health')
def health():
    """Health check endpoint for ECS/ALB"""
    return jsonify({
        "status": "healthy",
        "service": SERVICE_NAME,
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z"
    }), 200

@app.route('/python-test')
def python_test():
    """Main test endpoint for ECS CI/CD demonstration"""
    hostname = socket.gethostname()
    
    return jsonify({
        "message": "Hello World from Python Flask!",
        "service": SERVICE_NAME,
        "environment": ENVIRONMENT,
        "hostname": hostname,
        "version": "1.0.7",
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z",
        "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
        "status": "success"
    }), 200

@app.route('/')
def root():
    """Root endpoint with service information"""
    return jsonify({
        "service": SERVICE_NAME,
        "description": "EzyChat Python Flask Sample - ECS CI/CD Demo",
        "version": "1.0.1",
        "environment": ENVIRONMENT,
        "endpoints": {
            "/health": "Health check endpoint",
            "/python-test": "Main test endpoint with Hello World",
            "/": "This service information"
        },
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z"
    }), 200

@app.route('/status')
def status():
    """Detailed status endpoint"""
    return jsonify({
        "status": "running",
        "service": SERVICE_NAME,
        "version": "1.0.1",
        "environment": ENVIRONMENT,
        "hostname": socket.gethostname(),
        "uptime_seconds": int(datetime.datetime.now().timestamp()),
        "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
        "flask_version": "2.3.0",
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z"
    }), 200

@app.errorhandler(404)
def not_found(error):
    """Custom 404 handler"""
    return jsonify({
        "error": "Not Found",
        "message": "The requested endpoint does not exist",
        "service": SERVICE_NAME,
        "available_endpoints": ["/", "/health", "/python-test", "/status"],
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Custom 500 handler"""
    return jsonify({
        "error": "Internal Server Error",
        "message": "An internal error occurred",
        "service": SERVICE_NAME,
        "timestamp": datetime.datetime.utcnow().isoformat() + "Z"
    }), 500

if __name__ == '__main__':
    print(f"🚀 Starting {SERVICE_NAME} on {HOST}:{PORT}")
    print(f"🌍 Environment: {ENVIRONMENT}")
    print(f"📡 Available endpoints:")
    print(f"   GET /          - Service information")
    print(f"   GET /health    - Health check")
    print(f"   GET /python-test - Hello World test")
    print(f"   GET /status    - Detailed status")
    
    app.run(
        host=HOST,
        port=PORT,
        debug=(ENVIRONMENT == 'development')
    )