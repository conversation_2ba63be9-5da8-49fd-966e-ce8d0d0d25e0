import 'reflect-metadata';
import { TestDIContainer } from '../src/di/test-container';

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env['NODE_ENV'] = 'test';
  process.env['JWT_SECRET'] = 'test-secret-key-123-minimum-32-characters-required';
  process.env['DYNAMODB_TABLE_NAME'] = 'WhatsAppSessions-test';
  process.env['AWS_REGION'] = 'ap-southeast-1';
  process.env['DYNAMODB_ENDPOINT'] = ''; // Empty for tests (will use mock)
  process.env['AWS_ACCESS_KEY_ID'] = 'test-key-id';
  process.env['AWS_SECRET_ACCESS_KEY'] = 'test-secret-key';
  process.env['LOG_LEVEL'] = 'error'; // Reduce log noise in tests
  process.env['LOG_FORMAT'] = 'json';
  process.env['ENCRYPTION_KEY'] = 'test-encryption-key-32-chars-minimum-for-testing-env';
  process.env['MAX_CONCURRENT_SESSIONS'] = '100';
  process.env['QR_TOKEN_EXPIRY_SEC'] = '300';
  process.env['RECONNECT_MAX_ATTEMPTS'] = '5';
  process.env['CONNECTION_TIMEOUT_MS'] = '60000';

  // Relaxed rate limits for tests
  process.env['RATE_LIMIT_WINDOW_MS'] = '60000';
  process.env['RATE_LIMIT_MAX_REQUESTS'] = '10000';
  process.env['RATE_LIMIT_MESSAGES_PER_MIN'] = '1000';
  process.env['RATE_LIMIT_QR_GENERATION_PER_5MIN'] = '100';

  // Initialize test container
  TestDIContainer.initialize();
});

afterAll(async () => {
  // Cleanup after all tests
  TestDIContainer.destroy();
});

beforeEach(() => {
  // Reset test container for each test
  TestDIContainer.reset();
});

// Increase timeout for integration tests
jest.setTimeout(30000);
