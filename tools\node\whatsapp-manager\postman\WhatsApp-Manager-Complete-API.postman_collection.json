{"info": {"name": "WhatsApp Manager API - Complete Collection", "description": "Comprehensive API collection for WhatsApp Manager including Authentication, Session Management, Health Monitoring, QR Management, and Message Operations", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string", "description": "Base URL for the WhatsApp Manager API"}, {"key": "apiPrefix", "value": "/api", "type": "string", "description": "API prefix for all endpoints"}, {"key": "userId", "value": "test-user-123", "type": "string", "description": "Test user ID for session operations"}, {"key": "tokenId", "value": "", "type": "string", "description": "Auth token ID (auto-populated from responses)"}, {"key": "jwtToken", "value": "", "type": "string", "description": "JWT token (auto-populated from responses)"}, {"key": "sessionId", "value": "", "type": "string", "description": "Session ID (auto-populated from responses)"}, {"key": "phoneNumber", "value": "<EMAIL>", "type": "string", "description": "WhatsApp phone number for messaging"}, {"key": "webhookUrl", "value": "https://webhook.site/your-unique-url", "type": "string", "description": "Webhook URL for testing"}], "item": [{"name": "🏥 Health & Monitoring", "description": "Health checks, system monitoring, and metrics endpoints", "item": [{"name": "Simple Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 503', function () {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "", "pm.test('Response has status field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(['healthy', 'degraded', 'unhealthy']).to.include(jsonData.status);", "});", "", "pm.test('Response has timestamp', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('timestamp');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health"]}, "description": "Simple health check endpoint for load balancers. Returns basic health status."}, "response": []}, {"name": "Detailed Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 503', function () {", "    pm.expect([200, 503]).to.include(pm.response.code);", "});", "", "pm.test('Response has detailed health data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('status');", "    pm.expect(jsonData.data).to.have.property('components');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/detailed?components=database,memory,sessions&timeout=5000", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "detailed"], "query": [{"key": "components", "value": "database,memory,sessions", "description": "Comma-separated list of components to check"}, {"key": "timeout", "value": "5000", "description": "Timeout in milliseconds for health checks"}]}, "description": "Comprehensive health check with component details and configurable timeout."}, "response": []}, {"name": "Kubernetes Readiness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/readiness", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "readiness"]}, "description": "Kubernetes readiness probe endpoint. Checks if the service is ready to receive traffic."}, "response": []}, {"name": "Kubernetes Liveness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/liveness", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "liveness"]}, "description": "Kubernetes liveness probe endpoint. Checks if the service is alive and should not be restarted."}, "response": []}, {"name": "System Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/metrics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "metrics"]}, "description": "System metrics endpoint for monitoring and alerting. Rate limited to 60 requests per minute."}, "response": []}, {"name": "Monitoring Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/monitoring", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "monitoring"]}, "description": "Real-time monitoring data and alerts. Rate limited to 30 requests per minute."}, "response": []}, {"name": "System Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/monitoring/alerts", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "monitoring", "alerts"]}, "description": "Get system alerts and notifications. Rate limited to 60 requests per minute."}, "response": []}, {"name": "Trigger System Cleanup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dryRun\": false,\n  \"components\": [\"sessions\", \"tokens\", \"logs\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/cleanup", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "cleanup"]}, "description": "Trigger system cleanup operations. Admin endpoint with heavy rate limiting (5 requests per 10 minutes)."}, "response": []}, {"name": "Check Specific Component", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/health/component/database", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "health", "component", "database"]}, "description": "Check health of a specific component. Available components: database, memory, sessions, whatsapp."}, "response": []}]}, {"name": "🔑 Authentication", "description": "QR Token Generation and Authentication endpoints", "item": [{"name": "Generate QR Authentication Link", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('tokenId', response.data.tokenId);", "        pm.collectionVariables.set('jwtToken', response.data.token);", "        console.log('Token ID:', response.data.tokenId);", "        console.log('QR Auth URL:', response.data.qrAuthUrl);", "    }", "}", "", "pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Response contains required fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('tokenId');", "    pm.expect(jsonData.data).to.have.property('token');", "    pm.expect(jsonData.data).to.have.property('qrAuthUrl');", "    pm.expect(jsonData.data).to.have.property('expiresAt');", "    pm.expect(jsonData.data).to.have.property('userId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"expirySeconds\": 600,\n  \"maxActiveTokens\": 5,\n  \"metadata\": {\n    \"purpose\": \"qr_auth\",\n    \"clientId\": \"postman-test\",\n    \"deviceInfo\": \"Postman Collection\"\n  },\n  \"baseUrl\": \"https://app.ezychat.com\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link"]}, "description": "Generate a new QR authentication link with JWT token. Rate limited to 10 requests per 15 minutes."}, "response": []}, {"name": "Get QR Link Details", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Token details are correct', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.tokenId).to.equal(pm.collectionVariables.get('tokenId'));", "    pm.expect(jsonData.data.userId).to.equal(pm.collectionVariables.get('userId'));", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link/{{tokenId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link", "{{tokenId}}"]}, "description": "Get details of a specific QR authentication link. Rate limited to 50 requests per 15 minutes."}, "response": []}, {"name": "Validate QR Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Token is valid', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.isValid).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link/{{tokenId}}/validate", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link", "{{tokenId}}", "validate"]}, "description": "Validate a QR authentication token. Rate limited to 20 requests per 15 minutes."}, "response": []}, {"name": "Use QR Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Token marked as used', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.usedAt).to.not.be.null;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link/{{tokenId}}/use", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link", "{{tokenId}}", "use"]}, "description": "Mark a QR authentication token as used. Rate limited to 5 requests per 15 minutes."}, "response": []}, {"name": "Get User Tokens", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user tokens', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('tokens');", "    pm.expect(jsonData.data).to.have.property('totalCount');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/users/{{userId}}/tokens?includeExpired=false", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "users", "{{userId}}", "tokens"], "query": [{"key": "includeExpired", "value": "false", "description": "Include expired tokens in response"}]}, "description": "Get all tokens for a specific user. Rate limited to 30 requests per 15 minutes."}, "response": []}, {"name": "Revoke All User Tokens", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Tokens revoked successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('revokedTokens');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/users/{{userId}}/tokens", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "users", "{{userId}}", "tokens"]}, "description": "Revoke all tokens for a specific user. Rate limited to 5 requests per 15 minutes."}, "response": []}, {"name": "Delete Specific Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Token deleted successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link/{{tokenId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link", "{{tokenId}}"]}, "description": "Delete a specific QR authentication token. Rate limited to 10 requests per 15 minutes."}, "response": []}]}, {"name": "📱 Session Management", "description": "WhatsApp session lifecycle management endpoints", "item": [{"name": "Start WhatsApp Session", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201 || pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('sessionId', response.data.sessionId);", "        console.log('Session ID:', response.data.sessionId);", "        console.log('Session Status:', response.data.status);", "        if (response.data.qrCode) {", "            console.log('QR Code available for scanning');", "        }", "    }", "}", "", "pm.test('Status code is 200 or 201', function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Response contains session data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('sessionId');", "    pm.expect(jsonData.data).to.have.property('status');", "    pm.expect(jsonData.data).to.have.property('userId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"Postman Test Device\",\n  \"browserName\": \"Chrome\",\n  \"webhookUrl\": \"{{webhookUrl}}\",\n  \"autoReconnect\": true,\n  \"qrTimeout\": 300000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}, "description": "Start a new WhatsApp session for a user. Rate limited to 5 requests per minute."}, "response": []}, {"name": "Get Session Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Session data is present', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('sessionId');", "    pm.expect(jsonData.data).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}?includeHealth=true", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"], "query": [{"key": "includeHealth", "value": "true", "description": "Include health check data in response"}]}, "description": "Get status of a specific WhatsApp session with optional health check data."}, "response": []}, {"name": "Reconnect Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"force\": false,\n  \"preserveAuthState\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/reconnect", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "reconnect"]}, "description": "Reconnect an existing WhatsApp session. Rate limited to 10 requests per minute."}, "response": []}, {"name": "Terminate Session", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Manual termination via Postman\",\n  \"forceDelete\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}"]}, "description": "Terminate a WhatsApp session. Rate limited to 10 requests per minute."}, "response": []}, {"name": "List All Sessions", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sessions array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('sessions');", "    pm.expect(jsonData.data.sessions).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions?limit=10&offset=0&status=connected&sortBy=updatedAt&sortOrder=desc", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions"], "query": [{"key": "limit", "value": "10", "description": "Number of sessions to return"}, {"key": "offset", "value": "0", "description": "Number of sessions to skip"}, {"key": "status", "value": "connected", "description": "Filter by session status"}, {"key": "sortBy", "value": "updatedAt", "description": "Field to sort by"}, {"key": "sortOrder", "value": "desc", "description": "Sort order (asc/desc)"}]}, "description": "List all WhatsApp sessions with pagination and filtering options."}, "response": []}, {"name": "Get Session Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/statistics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "statistics"]}, "description": "Get comprehensive statistics about all WhatsApp sessions."}, "response": []}]}, {"name": "🔄 QR Code Management", "description": "QR code refresh and status management for WhatsApp sessions", "item": [{"name": "Refresh QR Code", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('QR code refreshed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('qrCode');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"force\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/refresh", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "refresh"]}, "description": "Refresh QR code for a pending WhatsApp session. Rate limited to 5 requests per 5 minutes."}, "response": []}, {"name": "Get QR Code Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/qr/status", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "qr", "status"]}, "description": "Get QR code status and validity for a WhatsApp session."}, "response": []}]}, {"name": "💬 Message Operations", "description": "Send and retrieve WhatsApp messages through active sessions", "item": [{"name": "Send Text Message", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Message sent successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('messageId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{phoneNumber}}\",\n  \"content\": \"Hello from WhatsApp Manager API! This is a test message sent via Postman.\",\n  \"type\": \"text\",\n  \"metadata\": {\n    \"source\": \"postman-collection\",\n    \"timestamp\": \"{{$timestamp}}\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages/send", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages", "send"]}, "description": "Send a text message through an active WhatsApp session. Rate limited to 60 messages per minute."}, "response": []}, {"name": "Get Messages", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Messages retrieved successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('messages');", "    pm.expect(jsonData.data.messages).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/{{userId}}/messages?limit=50&offset=0&type=text&hasMedia=false", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "{{userId}}", "messages"], "query": [{"key": "limit", "value": "50", "description": "Number of messages to return"}, {"key": "offset", "value": "0", "description": "Number of messages to skip"}, {"key": "type", "value": "text", "description": "Filter by message type"}, {"key": "hasMedia", "value": "false", "description": "Filter by media presence"}]}, "description": "Get messages for a WhatsApp session with filtering options. Rate limited to 100 requests per minute."}, "response": []}]}, {"name": "⚙️ Admin Operations", "description": "Administrative endpoints for system management", "item": [{"name": "Terminate All Sessions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"System maintenance\",\n  \"force\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/terminate-all", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "terminate-all"]}, "description": "Terminate all active WhatsApp sessions. Admin endpoint with heavy rate limiting (2 requests per 10 minutes)."}, "response": []}, {"name": "Get Auth Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/statistics", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "statistics"]}, "description": "Get comprehensive authentication statistics. Rate limited to 20 requests per 15 minutes."}, "response": []}, {"name": "Cleanup Expired Tokens", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"batchSize\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/cleanup", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "cleanup"]}, "description": "Cleanup expired authentication tokens. Rate limited to 5 requests per hour."}, "response": []}]}, {"name": "🚨 Error Testing", "description": "Test error scenarios and validation", "item": [{"name": "Generate QR Link - Invalid User ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error response for invalid user ID', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error.code).to.equal('VALIDATION_ERROR');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"\",\n  \"expirySeconds\": 600\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link"]}, "description": "Test validation error for empty user ID"}, "response": []}, {"name": "Get Non-existent Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404', function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test('Error response for non-existent token', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData.error.code).to.equal('TOKEN_NOT_FOUND');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{apiPrefix}}/auth/qr-link/non-existent-token-id", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "auth", "qr-link", "non-existent-token-id"]}, "description": "Test 404 error for non-existent token"}, "response": []}, {"name": "Start Session - Invalid Request", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"\",\n  \"qrTimeout\": -1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}{{apiPrefix}}/sessions/invalid-user-id", "host": ["{{baseUrl}}"], "path": ["{{apiPrefix}}", "sessions", "invalid-user-id"]}, "description": "Test validation error for invalid session start request"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default values if not already set", "if (!pm.collectionVariables.get('baseUrl')) {", "    pm.collectionVariables.set('baseUrl', 'http://localhost:3000');", "}", "if (!pm.collectionVariables.get('userId')) {", "    pm.collectionVariables.set('userId', 'test-user-123');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to check response time", "pm.test('Response time is less than 10000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "// Global test to check content type for JSON responses", "if (pm.response.headers.get('Content-Type') && pm.response.headers.get('Content-Type').includes('application/json')) {", "    pm.test('Response is valid JSON', function () {", "        pm.response.to.be.json;", "    });", "}"]}}]}