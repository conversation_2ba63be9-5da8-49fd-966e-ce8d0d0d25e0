import { v4 as uuidv4 } from 'uuid';

/**
 * QR Code status enumeration
 */
export type QRCodeStatus = 'pending' | 'scanned' | 'expired' | 'invalid';

/**
 * QR Code domain entity for WhatsApp authentication
 * Handles QR code lifecycle, expiry, and security
 */
export class QRCode {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly qrData: string,
    public readonly qrImageData: string, // Base64 encoded image
    public readonly status: QRCodeStatus,
    public readonly createdAt: Date,
    public readonly expiresAt: Date,
    public readonly scannedAt?: Date,
    public readonly metadata?: Record<string, any>
  ) {
    this.validateQRData(qrData);
    this.validateExpiry();
  }

  /**
   * Factory method to create a new QR code
   */
  static create(
    userId: string,
    qrData: string,
    qrImageData: string,
    expirySeconds: number = 300, // 5 minutes default
    metadata?: Record<string, any>
  ): QRCode {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expirySeconds * 1000));

    return new QRCode(
      uuidv4(),
      userId,
      qrData,
      qrImageData,
      'pending',
      now,
      expiresAt,
      undefined,
      metadata
    );
  }

  /**
   * Check if QR code is expired
   */
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Check if QR code is valid for scanning
   */
  isValidForScanning(): boolean {
    return this.status === 'pending' && !this.isExpired();
  }

  /**
   * Mark QR code as scanned
   */
  markAsScanned(): QRCode {
    if (!this.isValidForScanning()) {
      throw new Error('QR code is not valid for scanning');
    }

    return new QRCode(
      this.id,
      this.userId,
      this.qrData,
      this.qrImageData,
      'scanned',
      this.createdAt,
      this.expiresAt,
      new Date(),
      this.metadata
    );
  }

  /**
   * Mark QR code as expired
   */
  markAsExpired(): QRCode {
    return new QRCode(
      this.id,
      this.userId,
      this.qrData,
      this.qrImageData,
      'expired',
      this.createdAt,
      this.expiresAt,
      this.scannedAt,
      this.metadata
    );
  }

  /**
   * Get time remaining until expiry in seconds
   */
  getTimeToExpiry(): number {
    const now = new Date();
    const remaining = Math.max(0, this.expiresAt.getTime() - now.getTime());
    return Math.floor(remaining / 1000);
  }

  /**
   * Validate QR data format
   */
  private validateQRData(qrData: string): void {
    if (!qrData || qrData.trim().length === 0) {
      throw new Error('QR data cannot be empty');
    }

    // Real WhatsApp QR data validation
    // Baileys QR data is typically a long string (base64 or comma-separated values)
    if (qrData.length < 20) {
      throw new Error('Invalid QR data format');
    }
  }

  /**
   * Validate expiry time
   */
  private validateExpiry(): void {
    if (this.expiresAt <= this.createdAt) {
      throw new Error('Expiry time must be after creation time');
    }

    const maxExpiryHours = 24;
    const maxExpiry = new Date(this.createdAt.getTime() + (maxExpiryHours * 60 * 60 * 1000));
    if (this.expiresAt > maxExpiry) {
      throw new Error(`QR code expiry cannot exceed ${maxExpiryHours} hours`);
    }
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      userId: this.userId,
      qrData: this.qrData,
      qrImageData: this.qrImageData,
      status: this.status,
      createdAt: this.createdAt.toISOString(),
      expiresAt: this.expiresAt.toISOString(),
      scannedAt: this.scannedAt?.toISOString(),
      metadata: this.metadata
    };
  }

  /**
   * Deserialize from JSON
   */
  static fromJSON(data: Record<string, any>): QRCode {
    return new QRCode(
      data['id'],
      data['userId'],
      data['qrData'],
      data['qrImageData'],
      data['status'],
      new Date(data['createdAt']),
      new Date(data['expiresAt']),
      data['scannedAt'] ? new Date(data['scannedAt']) : undefined,
      data['metadata']
    );
  }

  /**
   * Clone with updates
   */
  clone(updates: Partial<Omit<QRCode, 'id' | 'userId' | 'createdAt'>>): QRCode {
    return new QRCode(
      this.id,
      this.userId,
      updates.qrData !== undefined ? updates.qrData : this.qrData,
      updates.qrImageData !== undefined ? updates.qrImageData : this.qrImageData,
      updates.status !== undefined ? updates.status : this.status,
      this.createdAt,
      updates.expiresAt !== undefined ? updates.expiresAt : this.expiresAt,
      updates.scannedAt !== undefined ? updates.scannedAt : this.scannedAt,
      updates.metadata !== undefined ? updates.metadata : this.metadata
    );
  }
}
