/**
 * Crypto polyfill for Node.js environments that don't have globalThis.crypto
 * Required for @whiskeysockets/baileys to work properly
 */

import { webcrypto } from 'crypto';

// Polyfill globalThis.crypto if it doesn't exist
if (!globalThis.crypto) {
  // @ts-ignore - Type<PERSON> doesn't know about webcrypto
  globalThis.crypto = webcrypto;
}

// Ensure crypto.subtle is available
if (!globalThis.crypto.subtle) {
  // @ts-ignore
  globalThis.crypto.subtle = webcrypto.subtle;
}

export {};
