name: Reusable - AWS OIDC Setup

on:
  workflow_call:
    inputs:
      aws-region:
        required: true
        type: string
        description: 'AWS region'
      ecr-account-id:
        required: true
        type: string
        description: 'ECR account ID for OIDC role'
      github-oidc-role:
        required: true
        type: string
        description: 'GitHub OIDC role name'
      target-account-id:
        required: false
        type: string
        description: 'Target account ID for cross-account role assumption'
      target-role:
        required: false
        type: string
        description: 'Target role name for cross-account assumption'
    outputs:
      caller-identity:
        description: 'AWS caller identity after role assumption'
        value: ${{ jobs.setup-aws.outputs.caller-identity }}

jobs:
  setup-aws:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    outputs:
      caller-identity: ${{ steps.final-identity.outputs.identity }}
    steps:
      - name: Check initial identity
        run: |
          echo "🔍 Checking initial GitHub Actions identity..."
          echo "Repository: ${{ github.repository }}"
          echo "Ref: ${{ github.ref }}"
          echo "Actor: ${{ github.actor }}"
          echo "Run ID: ${{ github.run_id }}"
          echo "Event: ${{ github.event_name }}"
          
          echo "🔍 Checking initial AWS STS caller identity..."
          aws sts get-caller-identity || echo "❌ No AWS credentials available initially"

      - name: Configure AWS OIDC role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ inputs.ecr-account-id }}:role/${{ inputs.github-oidc-role }}
          role-session-name: GitHubActions-OIDC-${{ github.run_id }}
          aws-region: ${{ inputs.aws-region }}

      - name: Assume target account role
        if: inputs.target-account-id && inputs.target-role
        run: |
          echo "🔍 Current identity before target role assumption:"
          aws sts get-caller-identity
          
          echo "🔄 Assuming target account role..."
          OUTPUT=$(aws sts assume-role --role-arn arn:aws:iam::${{ inputs.target-account-id }}:role/${{ inputs.target-role }} --role-session-name GitHubActions-Target-${{ github.run_id }})
          
          # Set new credentials
          echo "AWS_ACCESS_KEY_ID=$(echo $OUTPUT | jq -r .Credentials.AccessKeyId)" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $OUTPUT | jq -r .Credentials.SecretAccessKey)" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $OUTPUT | jq -r .Credentials.SessionToken)" >> $GITHUB_ENV
          
          echo "✅ Verifying new target account identity:"
          aws sts get-caller-identity

      - name: Get final identity
        id: final-identity
        run: |
          IDENTITY=$(aws sts get-caller-identity --output json)
          echo "identity=$IDENTITY" >> $GITHUB_OUTPUT
          echo "✅ Final AWS identity configured"
