import jwt from 'jsonwebtoken';
import { injectable } from 'tsyringe';
import { AuthToken, AuthTokenError } from '../../domain/value-objects/AuthToken';

/**
 * JWT payload interface for auth tokens
 */
export interface AuthTokenPayload {
  tokenId: string;
  userId: string;
  purpose: 'qr_auth';
  iat: number;
  exp: number;
  jti: string;
}

/**
 * Auth service configuration
 */
export interface AuthServiceConfig {
  jwtSecret: string;
  defaultExpirySeconds: number;
  issuer?: string;
  audience?: string;
}

/**
 * Service for JWT token operations and authentication logic
 */
@injectable()
export class AuthService {
  private readonly jwtSecret: string;
  private readonly defaultExpirySeconds: number;
  private readonly issuer: string;
  private readonly audience: string;

  constructor() {
    this.jwtSecret = this.getRequiredEnvVar('JWT_SECRET');
    this.defaultExpirySeconds = parseInt(process.env['QR_TOKEN_EXPIRY_SEC'] || '300', 10);
    this.issuer = process.env['JWT_ISSUER'] || 'ezychat-whatsapp-manager';
    this.audience = process.env['JWT_AUDIENCE'] || 'ezychat-client';

    this.validateConfig();
  }

  /**
   * Generate a new JWT token for QR authentication
   */
  generateQrAuthToken(userId: string, expirySeconds?: number): string {
    if (!userId || userId.trim().length === 0) {
      throw new AuthTokenError('User ID is required for token generation');
    }

    const expiry = expirySeconds !== undefined ? expirySeconds : this.defaultExpirySeconds;
    if (expiry <= 0 || expiry > 86400) {
      throw new AuthTokenError('Expiry seconds must be between 1 and 86400 (24 hours)');
    }

    const now = Math.floor(Date.now() / 1000);
    const tokenId = this.generateTokenId();

    const payload: AuthTokenPayload = {
      tokenId,
      userId: userId.trim(),
      purpose: 'qr_auth',
      iat: now,
      exp: now + expiry,
      jti: tokenId
    };

    const options: jwt.SignOptions = {
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    };

    try {
      return jwt.sign(payload, this.jwtSecret, options);
    } catch (error) {
      throw new AuthTokenError(`Failed to generate JWT token: ${error.message}`);
    }
  }

  /**
   * Verify and decode a JWT token
   */
  verifyToken(token: string): AuthTokenPayload {
    if (!token || token.trim().length === 0) {
      throw new AuthTokenError('Token is required for verification');
    }

    const options: jwt.VerifyOptions = {
      issuer: this.issuer,
      audience: this.audience,
      algorithms: ['HS256']
    };

    try {
      const decoded = jwt.verify(token.trim(), this.jwtSecret, options) as AuthTokenPayload;
      
      // Validate payload structure
      this.validateTokenPayload(decoded);
      
      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthTokenError('Token has expired', 'TOKEN_EXPIRED');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthTokenError(`Invalid token: ${error.message}`, 'TOKEN_INVALID');
      } else if (error instanceof AuthTokenError) {
        throw error;
      } else {
        throw new AuthTokenError(`Token verification failed: ${error.message}`);
      }
    }
  }

  /**
   * Create an AuthToken instance from JWT token
   */
  createAuthTokenFromJWT(jwtToken: string): AuthToken {
    const payload = this.verifyToken(jwtToken);
    
    const expiresAt = new Date(payload.exp * 1000);
    const createdAt = new Date(payload.iat * 1000);

    return AuthToken.create(
      payload.userId,
      jwtToken,
      Math.floor((expiresAt.getTime() - createdAt.getTime()) / 1000),
      {
        purpose: payload.purpose,
        jti: payload.jti
      }
    );
  }

  /**
   * Extract token ID from JWT without full verification
   */
  extractTokenId(token: string): string {
    try {
      const decoded = jwt.decode(token) as AuthTokenPayload;
      if (!decoded || !decoded.tokenId) {
        throw new AuthTokenError('Invalid token format');
      }
      return decoded.tokenId;
    } catch (error) {
      throw new AuthTokenError(`Failed to extract token ID: ${error.message}`);
    }
  }

  /**
   * Extract user ID from JWT without full verification
   */
  extractUserId(token: string): string {
    try {
      const decoded = jwt.decode(token) as AuthTokenPayload;
      if (!decoded || !decoded.userId) {
        throw new AuthTokenError('Invalid token format');
      }
      return decoded.userId;
    } catch (error) {
      throw new AuthTokenError(`Failed to extract user ID: ${error.message}`);
    }
  }

  /**
   * Check if token is expired without full verification
   */
  isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as AuthTokenPayload;
      if (!decoded || !decoded.exp) {
        return true;
      }
      return Date.now() >= decoded.exp * 1000;
    } catch (error) {
      return true;
    }
  }

  /**
   * Get token expiry date without full verification
   */
  getTokenExpiry(token: string): Date {
    try {
      const decoded = jwt.decode(token) as AuthTokenPayload;
      if (!decoded || !decoded.exp) {
        throw new AuthTokenError('Invalid token format');
      }
      return new Date(decoded.exp * 1000);
    } catch (error) {
      throw new AuthTokenError(`Failed to extract expiry: ${error.message}`);
    }
  }

  /**
   * Generate a unique token ID
   */
  private generateTokenId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `qr_${timestamp}_${random}`;
  }

  /**
   * Validate JWT payload structure
   */
  private validateTokenPayload(payload: any): void {
    if (!payload || typeof payload !== 'object') {
      throw new AuthTokenError('Invalid token payload');
    }

    const required = ['tokenId', 'userId', 'purpose', 'iat', 'exp', 'jti'];
    for (const field of required) {
      if (!payload[field]) {
        throw new AuthTokenError(`Missing required field: ${field}`);
      }
    }

    if (payload.purpose !== 'qr_auth') {
      throw new AuthTokenError('Invalid token purpose');
    }

    if (typeof payload.iat !== 'number' || typeof payload.exp !== 'number') {
      throw new AuthTokenError('Invalid token timestamps');
    }

    if (payload.exp <= payload.iat) {
      throw new AuthTokenError('Invalid token expiry');
    }
  }

  /**
   * Validate service configuration
   */
  private validateConfig(): void {
    // Validate JWT secret
    if (!this.jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required for authentication service');
    }

    if (this.jwtSecret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long for security. Current length: ' + this.jwtSecret.length);
    }

    // Validate JWT secret format (should be base64 or hex for security)
    if (!this.isValidSecretFormat(this.jwtSecret)) {
      console.warn('JWT_SECRET should be a base64 or hex encoded string for better security');
    }

    // Validate expiry configuration
    if (this.defaultExpirySeconds <= 0 || this.defaultExpirySeconds > 86400) {
      throw new Error(`QR_TOKEN_EXPIRY_SEC must be between 1 and 86400 seconds. Current value: ${this.defaultExpirySeconds}`);
    }

    // Validate issuer and audience
    if (!this.issuer || this.issuer.trim().length === 0) {
      throw new Error('JWT_ISSUER must be a non-empty string');
    }

    if (!this.audience || this.audience.trim().length === 0) {
      throw new Error('JWT_AUDIENCE must be a non-empty string');
    }
  }

  /**
   * Check if JWT secret has a valid format (base64 or hex)
   */
  private isValidSecretFormat(secret: string): boolean {
    // Check if it's base64 encoded
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (base64Regex.test(secret) && secret.length % 4 === 0) {
      return true;
    }

    // Check if it's hex encoded
    const hexRegex = /^[0-9a-fA-F]+$/;
    if (hexRegex.test(secret) && secret.length % 2 === 0) {
      return true;
    }

    return false;
  }

  /**
   * Get required environment variable
   */
  private getRequiredEnvVar(name: string): string {
    const value = process.env[name];
    if (!value) {
      throw new Error(`Required environment variable ${name} is not set`);
    }
    return value;
  }

  /**
   * Generate QR authentication URL
   */
  generateQrAuthUrl(token: string, baseUrl?: string): string {
    if (!token) {
      throw new AuthTokenError('Token is required for URL generation');
    }

    const base = baseUrl || process.env['QR_AUTH_BASE_URL'] || 'https://app.ezychat.com';
    return `${base}/auth/qr?token=${encodeURIComponent(token)}`;
  }

  /**
   * Create token metadata for storage
   */
  createTokenMetadata(additionalData?: Record<string, any>): Record<string, any> {
    return {
      createdBy: 'AuthService',
      version: '1.0',
      userAgent: process.env['USER_AGENT'] || 'EzyChat-WhatsApp-Manager',
      ...additionalData
    };
  }

  /**
   * Get service configuration (for debugging/monitoring)
   */
  getConfig(): Omit<AuthServiceConfig, 'jwtSecret'> {
    return {
      defaultExpirySeconds: this.defaultExpirySeconds,
      issuer: this.issuer,
      audience: this.audience
    };
  }
}
