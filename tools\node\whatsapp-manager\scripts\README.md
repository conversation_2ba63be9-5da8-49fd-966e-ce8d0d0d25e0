# Local Development Setup Scripts

This directory contains scripts for setting up local development environment.

## 🗄️ Database Setup

### Local DynamoDB Setup

For local development, you need to create DynamoDB tables manually since the application no longer handles table creation (this is now managed by IaC/Terraform in production).

#### Prerequisites

1. **DynamoDB Local** running (via Docker Compose)
   ```bash
   # Start DynamoDB Local
   docker-compose up dynamodb-local
   ```

2. **Environment Variables** configured
   ```bash
   # Required environment variables
   DYNAMODB_ENDPOINT=http://localhost:8000  # or http://dynamodb-local:8000 in Docker
   DYNAMODB_TABLE_NAME=WhatsAppSessions-dev
   AWS_REGION=ap-southeast-1
   AWS_ACCESS_KEY_ID=fakeMyKeyId
   AWS_SECRET_ACCESS_KEY=fakeSecretAccessKey
   ```

#### Setup Command

```bash
# Run the local setup script
npm run setup:local
```

This script will:
- ✅ Check if the table already exists
- 🏗️ Create the `WhatsAppSessions` table with proper schema
- ⏳ Wait for the table to become active
- 📊 Display setup summary

#### Manual Setup (Alternative)

If you prefer to set up manually or need to customize the table:

```bash
# Run the script directly with ts-node
npx ts-node scripts/setup-local-db.ts

# Or with debugging
DEBUG=* npx ts-node scripts/setup-local-db.ts
```

#### Table Schema

The script creates a table with the following structure:

```
Table: WhatsAppSessions-dev
├── Primary Key: PK (userId), SK (session#userId)
├── GSI: StatusIndex (GSI1PK: status#<status>, GSI1SK: createdAt)
├── TTL: Enabled on 'ttl' attribute
└── Billing: Pay-per-request
```

**Attributes:**
- `PK`: User ID (Hash Key)
- `SK`: Session identifier (Range Key)
- `GSI1PK`: Status-based grouping for queries
- `GSI1SK`: Creation timestamp for sorting
- `userId`, `sessionId`, `phoneNumber`, `authState`, `status`
- `createdAt`, `updatedAt`, `connectedAt`, `disconnectedAt`
- `ttl`: Auto-cleanup timestamp
- `deviceName`, `browserName`

## 🚀 Production Deployment

In production environments (UAT/PROD), DynamoDB tables are created and managed by **Terraform/IaC**:

```hcl
# Example Terraform configuration
resource "aws_dynamodb_table" "whatsapp_sessions" {
  name           = "WhatsAppSessions-${var.environment}"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "PK"
  range_key      = "SK"

  attribute {
    name = "PK"
    type = "S"
  }

  attribute {
    name = "SK"
    type = "S"
  }

  # ... additional configuration
}
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:8000
   ```
   **Solution**: Ensure DynamoDB Local is running
   ```bash
   docker-compose up dynamodb-local
   ```

2. **Table Already Exists**
   ```
   ResourceInUseException: Table already exists
   ```
   **Solution**: This is normal - the script will skip creation

3. **Invalid Credentials**
   ```
   UnrecognizedClientException: The security token included in the request is invalid
   ```
   **Solution**: Check your AWS credentials in environment variables

4. **Wrong Endpoint**
   ```
   NetworkingError: getaddrinfo ENOTFOUND dynamodb-local
   ```
   **Solution**: Use correct endpoint:
   - Docker: `http://dynamodb-local:8000`
   - Local: `http://localhost:8000`

### Verification

After setup, verify the table exists:

```bash
# Using AWS CLI with local endpoint
aws dynamodb list-tables --endpoint-url http://localhost:8000

# Using AWS CLI to describe table
aws dynamodb describe-table \
  --table-name WhatsAppSessions-dev \
  --endpoint-url http://localhost:8000
```

## 📝 Environment-Specific Setup

### Development
```bash
DYNAMODB_ENDPOINT=http://localhost:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-dev
```

### Testing
```bash
DYNAMODB_ENDPOINT=http://localhost:8000
DYNAMODB_TABLE_NAME=WhatsAppSessions-test
```

### Production
```bash
# No DYNAMODB_ENDPOINT (uses AWS DynamoDB)
DYNAMODB_TABLE_NAME=WhatsAppSessions-prod
```

## 🔄 Migration from Old Setup

If you're migrating from the old automatic table creation:

1. **Remove old data** (if needed):
   ```bash
   aws dynamodb delete-table --table-name WhatsAppSessions-dev --endpoint-url http://localhost:8000
   ```

2. **Run new setup**:
   ```bash
   npm run setup:local
   ```

3. **Update your workflow**:
   - Remove any manual table creation steps
   - Use `npm run setup:local` for new environments
   - Rely on Terraform for production deployments

---

**Note**: This separation of concerns ensures that:
- 🏠 **Local Development**: Quick setup with scripts
- 🏗️ **Production**: Proper IaC management with Terraform
- 🔒 **Security**: No table creation permissions needed in production
- 📈 **Scalability**: Infrastructure managed separately from application code
