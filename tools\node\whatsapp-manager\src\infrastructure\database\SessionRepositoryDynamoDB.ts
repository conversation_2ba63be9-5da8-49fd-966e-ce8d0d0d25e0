import { inject, injectable } from 'tsyringe';
import {
  DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import { 
  DynamoDBDocumentClient, 
  PutCommand, 
  GetCommand, 
  UpdateCommand, 
  DeleteCommand, 
  Query<PERSON>ommand, 
  Scan<PERSON>ommand,
  Batch<PERSON>rite<PERSON>ommand
} from '@aws-sdk/lib-dynamodb';

import { IConfigService } from '../../shared/config/interfaces';
import { ILoggerService } from '../../shared/logging/interfaces';
import {
  ISessionRepository,
  SessionAlreadyExistsError,
  SessionNotFoundError,
  DatabaseError
} from '../../domain/repositories/ISessionRepository';
import { SessionEntity, SessionStatus, AuthenticationState } from '../../domain/entities/SessionEntity';
import {
  SessionDynamoDBItem,
  SESSION_KEY_PATTERNS,
  SESSION_CONDITIONS,
  SESSION_UPDATE_EXPRESSIONS,
  SESSION_HELPERS
} from './schema/SessionTableSchema';

/**
 * DynamoDB implementation of the session repository
 */
@injectable()
export class SessionRepositoryDynamoDB implements ISessionRepository {
  private readonly client: DynamoDBDocumentClient;
  private readonly dynamoClient: DynamoDBClient;
  private readonly tableName: string;

  constructor(
    @inject('IConfigService') private configService: IConfigService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {
    // Initialize AWS DynamoDB client with local override support
    const dbConfig = this.configService.getDatabase();
    const clientConfig = {
      region: dbConfig.region,
      ...(dbConfig.endpoint && {
        endpoint: dbConfig.endpoint
      })
    };
    
    this.dynamoClient = new DynamoDBClient(clientConfig);
    this.client = DynamoDBDocumentClient.from(this.dynamoClient);
    this.tableName = dbConfig.tableName;

    this.logger.info('SessionRepositoryDynamoDB initialized', {
      tableName: this.tableName,
      region: dbConfig.region,
      endpoint: dbConfig.endpoint || 'default'
    });
  }

  /**
   * Initialize the repository (assumes table exists via IaC)
   */
  async initialize(): Promise<void> {
    // Table creation is now handled by IaC (Terraform)
    // This method is kept for interface compatibility but does minimal work
    this.logger.info('SessionRepositoryDynamoDB ready - table managed by IaC', {
      tableName: this.tableName
    });
  }

  /**
   * Create a new session (fails if already exists)
   */
  async createSession(session: SessionEntity): Promise<void> {
    // Table is assumed to exist (managed by IaC)
    const item = this.entityToItem(session);

    try {
      await this.client.send(new PutCommand({
        TableName: this.tableName,
        Item: item,
        ConditionExpression: SESSION_CONDITIONS.ITEM_NOT_EXISTS,
        ReturnValues: 'NONE'
      }));

      this.logger.info('Session created successfully', {
        userId: session.userId,
        sessionId: session.sessionId,
        operation: 'createSession'
      });
    } catch (error: any) {
      if (error.name === 'ConditionalCheckFailedException') {
        this.logger.warn('Session already exists', {
          userId: session.userId,
          operation: 'createSession'
        });
        throw new SessionAlreadyExistsError(`Session for user ${session.userId} already exists`);
      }

      this.logger.error('Failed to create session', {
        userId: session.userId,
        error: error.message,
        operation: 'createSession'
      });
      throw new DatabaseError('Failed to create session', error);
    }
  }

  /**
   * Update session status with optional metadata
   */
  async updateSessionStatus(
    userId: string, 
    status: SessionStatus, 
    metadata?: Partial<SessionEntity>
  ): Promise<void> {
    const updateExpression = ['SET #status = :status', '#updatedAt = :updatedAt', '#gsi1pk = :gsi1pk'];
    const expressionAttributeNames: any = {
      '#status': 'status',
      '#updatedAt': 'updatedAt',
      '#gsi1pk': 'GSI1PK'
    };
    const expressionAttributeValues: any = {
      ':status': status,
      ':updatedAt': new Date().toISOString(),
      ':gsi1pk': SESSION_KEY_PATTERNS.statusGSI1PK(status)
    };

    // Add optional metadata updates
    if (metadata?.connectedAt) {
      updateExpression.push('#connectedAt = :connectedAt');
      expressionAttributeNames['#connectedAt'] = 'connectedAt';
      expressionAttributeValues[':connectedAt'] = metadata.connectedAt.toISOString();
    }

    if (metadata?.phoneNumber) {
      updateExpression.push('#phoneNumber = :phoneNumber');
      expressionAttributeNames['#phoneNumber'] = 'phoneNumber';
      expressionAttributeValues[':phoneNumber'] = metadata.phoneNumber;
    }

    if (metadata?.disconnectedAt) {
      updateExpression.push('#disconnectedAt = :disconnectedAt');
      expressionAttributeNames['#disconnectedAt'] = 'disconnectedAt';
      expressionAttributeValues[':disconnectedAt'] = metadata.disconnectedAt.toISOString();
    }

    try {
      await this.client.send(new UpdateCommand({
        TableName: this.tableName,
        Key: { 
          PK: SESSION_KEY_PATTERNS.sessionPK(userId), 
          SK: SESSION_KEY_PATTERNS.sessionSK(userId) 
        },
        UpdateExpression: updateExpression.join(', '),
        ConditionExpression: SESSION_CONDITIONS.ITEM_EXISTS,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'NONE'
      }));

      this.logger.info('Session status updated', {
        userId,
        status,
        operation: 'updateSessionStatus'
      });
    } catch (error: any) {
      if (error.name === 'ConditionalCheckFailedException') {
        throw new SessionNotFoundError(`Session for user ${userId} not found`);
      }
      
      this.logger.error('Failed to update session status', {
        userId,
        status,
        error: error.message,
        operation: 'updateSessionStatus'
      });
      throw new DatabaseError('Failed to update session status', error);
    }
  }

  /**
   * Update authentication state
   */
  async updateAuthState(userId: string, authState: AuthenticationState): Promise<void> {
    try {
      await this.client.send(new UpdateCommand({
        TableName: this.tableName,
        Key: { 
          PK: SESSION_KEY_PATTERNS.sessionPK(userId), 
          SK: SESSION_KEY_PATTERNS.sessionSK(userId) 
        },
        UpdateExpression: SESSION_UPDATE_EXPRESSIONS.UPDATE_AUTH_STATE.UpdateExpression,
        ConditionExpression: SESSION_CONDITIONS.ITEM_EXISTS,
        ExpressionAttributeNames: SESSION_UPDATE_EXPRESSIONS.UPDATE_AUTH_STATE.ExpressionAttributeNames,
        ExpressionAttributeValues: {
          ':authState': SESSION_HELPERS.sanitizeAuthState(authState),
          ':updatedAt': new Date().toISOString()
        },
        ReturnValues: 'NONE'
      }));

      this.logger.debug('Auth state updated', {
        userId,
        operation: 'updateAuthState'
      });
    } catch (error: any) {
      if (error.name === 'ConditionalCheckFailedException') {
        throw new SessionNotFoundError(`Session for user ${userId} not found`);
      }
      throw new DatabaseError('Failed to update auth state', error);
    }
  }

  /**
   * Update connection information
   */
  async updateConnectionInfo(userId: string, phoneNumber: string, connectedAt: Date): Promise<void> {
    await this.updateSessionStatus(userId, 'connected', {
      phoneNumber,
      connectedAt
    });
  }

  /**
   * Find session by user ID
   */
  async findByUserId(userId: string): Promise<SessionEntity | null> {
    try {
      const result = await this.client.send(new GetCommand({
        TableName: this.tableName,
        Key: {
          PK: SESSION_KEY_PATTERNS.sessionPK(userId),
          SK: SESSION_KEY_PATTERNS.sessionSK(userId)
        }
      }));

      if (!result.Item) {
        this.logger.debug('No session found for user', { userId, operation: 'findByUserId' });
        return null;
      }

      return this.itemToEntity(result.Item as SessionDynamoDBItem);
    } catch (error) {
      this.logger.error('Failed to find session by user ID', {
        userId,
        error: (error as Error).message,
        operation: 'findByUserId'
      });
      throw new DatabaseError('Failed to find session', error as Error);
    }
  }

  /**
   * Find session by session ID
   */
  async findBySessionId(sessionId: string): Promise<SessionEntity | null> {
    try {
      const result = await this.client.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: '#sessionId = :sessionId',
        ExpressionAttributeNames: {
          '#sessionId': 'sessionId'
        },
        ExpressionAttributeValues: {
          ':sessionId': sessionId
        },
        Limit: 1
      }));

      if (!result.Items || result.Items.length === 0) {
        return null;
      }

      return this.itemToEntity(result.Items[0] as SessionDynamoDBItem);
    } catch (error) {
      this.logger.error('Failed to find session by session ID', {
        sessionId,
        error: (error as Error).message,
        operation: 'findBySessionId'
      });
      throw new DatabaseError('Failed to find session', error as Error);
    }
  }

  /**
   * Upsert session (create or update)
   */
  async upsertSession(session: SessionEntity): Promise<void> {
    const item = this.entityToItem(session);
    
    try {
      await this.client.send(new PutCommand({
        TableName: this.tableName,
        Item: item,
        ReturnValues: 'NONE'
      }));

      this.logger.info('Session upserted', {
        userId: session.userId,
        sessionId: session.sessionId,
        operation: 'upsertSession'
      });
    } catch (error) {
      this.logger.error('Failed to upsert session', {
        userId: session.userId,
        error: (error as Error).message,
        operation: 'upsertSession'
      });
      throw new DatabaseError('Failed to upsert session', error as Error);
    }
  }

  /**
   * Delete session by user ID
   */
  async delete(userId: string): Promise<void> {
    try {
      await this.client.send(new DeleteCommand({
        TableName: this.tableName,
        Key: { 
          PK: SESSION_KEY_PATTERNS.sessionPK(userId), 
          SK: SESSION_KEY_PATTERNS.sessionSK(userId) 
        }
      }));

      this.logger.info('Session deleted', {
        userId,
        operation: 'delete'
      });
    } catch (error) {
      this.logger.error('Failed to delete session', {
        userId,
        error: (error as Error).message,
        operation: 'delete'
      });
      throw new DatabaseError('Failed to delete session', error as Error);
    }
  }

  /**
   * Delete session by session ID
   */
  async deleteBySessionId(sessionId: string): Promise<void> {
    // First find the session to get the user ID
    const session = await this.findBySessionId(sessionId);
    if (session) {
      await this.delete(session.userId);
    }
  }

  /**
   * Convert entity to DynamoDB item
   */
  private entityToItem(session: SessionEntity): SessionDynamoDBItem {
    return {
      PK: SESSION_KEY_PATTERNS.sessionPK(session.userId),
      SK: SESSION_KEY_PATTERNS.sessionSK(session.userId),
      GSI1PK: SESSION_KEY_PATTERNS.statusGSI1PK(session.status),
      GSI1SK: SESSION_KEY_PATTERNS.statusGSI1SK(session.createdAt),
      userId: session.userId,
      sessionId: session.sessionId,
      phoneNumber: session.phoneNumber || undefined,
      authState: session.authState ? SESSION_HELPERS.sanitizeAuthState(session.authState) : undefined,
      status: session.status,
      createdAt: session.createdAt.toISOString(),
      updatedAt: session.updatedAt.toISOString(),
      connectedAt: session.connectedAt?.toISOString(),
      disconnectedAt: session.disconnectedAt?.toISOString(),
      ttl: session.ttl,
      deviceName: session.deviceName,
      browserName: session.browserName
    };
  }

  /**
   * Convert DynamoDB item to entity
   */
  private itemToEntity(item: SessionDynamoDBItem): SessionEntity {
    return new SessionEntity(
      item.userId,
      item.sessionId,
      item.phoneNumber || null,
      item.authState ? SESSION_HELPERS.parseAuthState(item.authState) : null,
      item.status,
      new Date(item.createdAt),
      new Date(item.updatedAt),
      item.connectedAt ? new Date(item.connectedAt) : undefined,
      item.disconnectedAt ? new Date(item.disconnectedAt) : undefined,
      item.ttl,
      item.deviceName,
      item.browserName
    );
  }

  // Table creation methods removed - now handled by IaC (Terraform)
  // If you need to create tables locally, use: npm run setup:local

  /**
   * Create session if it doesn't exist
   */
  async createIfNotExists(session: SessionEntity): Promise<{ created: boolean; existing?: SessionEntity }> {
    try {
      await this.createSession(session);
      return { created: true };
    } catch (error) {
      if (error instanceof SessionAlreadyExistsError) {
        const existing = await this.findByUserId(session.userId);
        return { created: false, existing: existing || undefined };
      }
      throw error;
    }
  }

  /**
   * Update session if it exists
   */
  async updateIfExists(userId: string, updates: Partial<SessionEntity>): Promise<boolean> {
    const updateExpression: string[] = ['SET #updatedAt = :updatedAt'];
    const expressionAttributeNames: Record<string, string> = {
      '#updatedAt': 'updatedAt'
    };
    const expressionAttributeValues: Record<string, any> = {
      ':updatedAt': new Date().toISOString()
    };

    // Build dynamic update expression
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'userId' && key !== 'sessionId' && value !== undefined) {
        const attributeName = `#${key}`;
        const valueName = `:${key}`;

        updateExpression.push(`${attributeName} = ${valueName}`);
        expressionAttributeNames[attributeName] = key;

        if (value instanceof Date) {
          expressionAttributeValues[valueName] = value.toISOString();
        } else if (key === 'authState') {
          expressionAttributeValues[valueName] = SESSION_HELPERS.sanitizeAuthState(value);
        } else {
          expressionAttributeValues[valueName] = value;
        }
      }
    });

    try {
      await this.client.send(new UpdateCommand({
        TableName: this.tableName,
        Key: {
          PK: SESSION_KEY_PATTERNS.sessionPK(userId),
          SK: SESSION_KEY_PATTERNS.sessionSK(userId)
        },
        UpdateExpression: updateExpression.join(', '),
        ConditionExpression: SESSION_CONDITIONS.ITEM_EXISTS,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'NONE'
      }));

      return true;
    } catch (error: any) {
      if (error.name === 'ConditionalCheckFailedException') {
        return false; // Session doesn't exist
      }
      throw new DatabaseError('Failed to update session', error);
    }
  }

  /**
   * Find all active sessions
   */
  async findAllActive(): Promise<SessionEntity[]> {
    return this.findByStatus('connected');
  }

  /**
   * Find sessions by status
   */
  async findByStatus(status: SessionStatus): Promise<SessionEntity[]> {
    try {
      const result = await this.client.send(new QueryCommand({
        TableName: this.tableName,
        IndexName: 'StatusIndex',
        KeyConditionExpression: 'GSI1PK = :statusPK',
        ExpressionAttributeValues: {
          ':statusPK': SESSION_KEY_PATTERNS.statusGSI1PK(status)
        },
        ScanIndexForward: false // Most recent first
      }));

      if (!result.Items) {
        return [];
      }

      return result.Items.map(item => this.itemToEntity(item as SessionDynamoDBItem));
    } catch (error) {
      this.logger.error('Failed to find sessions by status', {
        status,
        error: (error as Error).message,
        operation: 'findByStatus'
      });
      throw new DatabaseError('Failed to find sessions by status', error as Error);
    }
  }

  /**
   * Find expired sessions
   */
  async findExpiredSessions(): Promise<SessionEntity[]> {
    try {
      const currentTime = SESSION_HELPERS.getCurrentUnixTimestamp();
      const result = await this.client.send(new ScanCommand({
        TableName: this.tableName,
        FilterExpression: 'attribute_exists(#ttl) AND #ttl <= :currentTime',
        ExpressionAttributeNames: {
          '#ttl': 'ttl'
        },
        ExpressionAttributeValues: {
          ':currentTime': currentTime
        }
      }));

      if (!result.Items) {
        return [];
      }

      return result.Items.map(item => this.itemToEntity(item as SessionDynamoDBItem));
    } catch (error) {
      this.logger.error('Failed to find expired sessions', {
        error: (error as Error).message,
        operation: 'findExpiredSessions'
      });
      throw new DatabaseError('Failed to find expired sessions', error as Error);
    }
  }

  /**
   * Create multiple sessions
   */
  async createMany(sessions: SessionEntity[]): Promise<void> {
    if (sessions.length === 0) return;

    const batchSize = 25; // DynamoDB batch write limit
    const batches = [];

    for (let i = 0; i < sessions.length; i += batchSize) {
      batches.push(sessions.slice(i, i + batchSize));
    }

    try {
      for (const batch of batches) {
        const writeRequests = batch.map(session => ({
          PutRequest: {
            Item: this.entityToItem(session)
          }
        }));

        await this.client.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: writeRequests
          }
        }));
      }

      this.logger.info('Batch session creation completed', {
        totalSessions: sessions.length,
        batches: batches.length,
        operation: 'createMany'
      });
    } catch (error) {
      this.logger.error('Failed to create sessions in batch', {
        totalSessions: sessions.length,
        error: (error as Error).message,
        operation: 'createMany'
      });
      throw new DatabaseError('Failed to create sessions in batch', error as Error);
    }
  }

  /**
   * Delete multiple sessions
   */
  async deleteMany(userIds: string[]): Promise<void> {
    if (userIds.length === 0) return;

    const batchSize = 25; // DynamoDB batch write limit
    const batches = [];

    for (let i = 0; i < userIds.length; i += batchSize) {
      batches.push(userIds.slice(i, i + batchSize));
    }

    try {
      for (const batch of batches) {
        const writeRequests = batch.map(userId => ({
          DeleteRequest: {
            Key: {
              PK: SESSION_KEY_PATTERNS.sessionPK(userId),
              SK: SESSION_KEY_PATTERNS.sessionSK(userId)
            }
          }
        }));

        await this.client.send(new BatchWriteCommand({
          RequestItems: {
            [this.tableName]: writeRequests
          }
        }));
      }

      this.logger.info('Batch session deletion completed', {
        totalSessions: userIds.length,
        batches: batches.length,
        operation: 'deleteMany'
      });
    } catch (error) {
      this.logger.error('Failed to delete sessions in batch', {
        totalSessions: userIds.length,
        error: (error as Error).message,
        operation: 'deleteMany'
      });
      throw new DatabaseError('Failed to delete sessions in batch', error as Error);
    }
  }

  /**
   * Count total sessions
   */
  async count(): Promise<number> {
    try {
      const result = await this.client.send(new ScanCommand({
        TableName: this.tableName,
        Select: 'COUNT'
      }));

      return result.Count || 0;
    } catch (error) {
      this.logger.error('Failed to count sessions', {
        error: (error as Error).message,
        operation: 'count'
      });
      throw new DatabaseError('Failed to count sessions', error as Error);
    }
  }

  /**
   * Count sessions by status
   */
  async countByStatus(status: SessionStatus): Promise<number> {
    try {
      const result = await this.client.send(new QueryCommand({
        TableName: this.tableName,
        IndexName: 'StatusIndex',
        KeyConditionExpression: 'GSI1PK = :statusPK',
        ExpressionAttributeValues: {
          ':statusPK': SESSION_KEY_PATTERNS.statusGSI1PK(status)
        },
        Select: 'COUNT'
      }));

      return result.Count || 0;
    } catch (error) {
      this.logger.error('Failed to count sessions by status', {
        status,
        error: (error as Error).message,
        operation: 'countByStatus'
      });
      throw new DatabaseError('Failed to count sessions by status', error as Error);
    }
  }

  /**
   * Find all sessions
   */
  async findAllSessions(): Promise<SessionEntity[]> {
    try {
      const result = await this.client.send(new ScanCommand({
        TableName: this.tableName
      }));

      if (!result.Items) {
        return [];
      }

      return result.Items.map(item => this.itemToEntity(item as SessionDynamoDBItem));
    } catch (error) {
      this.logger.error('Failed to find all sessions', {
        error: (error as Error).message,
        operation: 'findAllSessions'
      });
      throw new DatabaseError('Failed to find all sessions', error as Error);
    }
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(userId: string, lastActivity: Date): Promise<void> {
    try {
      await this.client.send(new UpdateCommand({
        TableName: this.tableName,
        Key: {
          PK: SESSION_KEY_PATTERNS.sessionPK(userId),
          SK: SESSION_KEY_PATTERNS.sessionSK(userId)
        },
        UpdateExpression: 'SET #lastActivity = :lastActivity, #updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#lastActivity': 'lastActivity',
          '#updatedAt': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':lastActivity': lastActivity.toISOString(),
          ':updatedAt': new Date().toISOString()
        }
      }));
    } catch (error) {
      this.logger.error('Failed to update session activity', {
        userId,
        error: (error as Error).message,
        operation: 'updateSessionActivity'
      });
      throw new DatabaseError('Failed to update session activity', error as Error);
    }
  }

  /**
   * Save session (alias for upsertSession)
   */
  async saveSession(session: SessionEntity): Promise<void> {
    await this.upsertSession(session);
  }

  /**
   * Delete session (alias for delete)
   */
  async deleteSession(userId: string): Promise<void> {
    await this.delete(userId);
  }
}
