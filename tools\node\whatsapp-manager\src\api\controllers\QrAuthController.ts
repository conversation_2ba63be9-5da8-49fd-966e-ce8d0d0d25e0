import { Request, Response } from 'express';
import { injectable, inject } from 'tsyringe';
import { AuthService } from '../../infrastructure/services/AuthService';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';
import { AuthTokenError, AuthTokenNotFoundError, AuthTokenExpiredError, AuthTokenUsedError } from '../../domain/value-objects/AuthToken';
import { DI_TOKENS } from '../../di/tokens';

/**
 * Controller for QR authentication frontend routes
 * Handles the QR authentication URLs that users scan
 */
@injectable()
export class QrAuthController {
  constructor(
    @inject(DI_TOKENS.AuthService) private readonly authService: AuthService,
    @inject(DI_TOKENS.IAuthTokenRepository) private readonly authTokenRepository: IAuthTokenRepository
  ) {}

  /**
   * GET /auth/qr?token=...
   * Handle QR authentication from scanned QR codes
   */
  async handleQrAuth(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.query;

      if (!token || typeof token !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Invalid QR authentication request',
          error: {
            code: 'INVALID_TOKEN',
            message: 'Token parameter is required and must be a valid JWT string'
          }
        });
        return;
      }

      // Verify and decode the JWT token
      let decodedToken;
      try {
        decodedToken = this.authService.verifyToken(token);
      } catch (error) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired authentication token',
          error: {
            code: 'TOKEN_INVALID',
            message: 'The QR authentication token is invalid or has expired'
          }
        });
        return;
      }

      // Find the token in the database using the user ID and then match by JTI
      console.log('Looking for token with JTI:', decodedToken.jti, 'for user:', decodedToken.userId);
      const userTokens = await this.authTokenRepository.findByUserId(decodedToken.userId);
      console.log('Found', userTokens.length, 'tokens for user');

      // Find the token with matching JTI in metadata
      const authToken = userTokens.find(token => {
        if (token.metadata) {
          // metadata is already parsed as an object by AuthToken.fromDynamoDBItem
          if (typeof token.metadata === 'string') {
            try {
              const metadata = JSON.parse(token.metadata);
              return metadata.jti === decodedToken.jti;
            } catch (e) {
              return false;
            }
          } else {
            // metadata is already an object
            return token.metadata['jti'] === decodedToken.jti;
          }
        }
        return false;
      });
      console.log('Found token with matching JTI:', authToken ? 'YES' : 'NO');

      if (!authToken) {
        res.status(404).json({
          success: false,
          message: 'Authentication token not found',
          error: {
            code: 'TOKEN_NOT_FOUND',
            message: 'The authentication token was not found in the system'
          }
        });
        return;
      }

      // Check if token is expired
      if (authToken.isExpired()) {
        res.status(410).json({
          success: false,
          message: 'Authentication token has expired',
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'This QR code has expired. Please generate a new one.'
          }
        });
        return;
      }

      // Check if token has already been used
      if (authToken.isUsed) {
        res.status(409).json({
          success: false,
          message: 'Authentication token has already been used',
          error: {
            code: 'TOKEN_ALREADY_USED',
            message: 'This QR code has already been used for authentication.'
          }
        });
        return;
      }

      // Token is valid - return success response with authentication details
      res.status(200).json({
        success: true,
        message: 'QR authentication successful',
        data: {
          tokenId: authToken.tokenId,
          userId: authToken.userId,
          expiresAt: authToken.expiresAt.toISOString(),
          expiresInSeconds: authToken.getRemainingTimeSeconds(),
          createdAt: authToken.createdAt.toISOString(),
          metadata: authToken.metadata,
          instructions: {
            message: 'Authentication token is valid. Use the /use endpoint to mark it as used.',
            useEndpoint: `/api/auth/qr-link/${authToken.tokenId}/use`,
            method: 'POST'
          }
        }
      });

    } catch (error) {
      this.handleControllerError(res, error, 'QR authentication failed');
    }
  }

  /**
   * GET /auth/qr/:tokenId
   * Alternative endpoint for QR authentication by token ID
   */
  async handleQrAuthByTokenId(req: Request, res: Response): Promise<void> {
    try {
      const { tokenId } = req.params;

      if (!tokenId) {
        res.status(400).json({
          success: false,
          message: 'Token ID is required',
          error: {
            code: 'INVALID_TOKEN_ID',
            message: 'Token ID parameter is missing'
          }
        });
        return;
      }

      // Find the token in the database
      const authToken = await this.authTokenRepository.findByTokenId(tokenId);

      if (!authToken) {
        res.status(404).json({
          success: false,
          message: 'Authentication token not found',
          error: {
            code: 'TOKEN_NOT_FOUND',
            message: `Token with ID ${tokenId} not found`
          }
        });
        return;
      }

      // Check if token is expired
      if (authToken.isExpired()) {
        res.status(410).json({
          success: false,
          message: 'Authentication token has expired',
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'This authentication token has expired. Please generate a new one.'
          }
        });
        return;
      }

      // Check if token has already been used
      if (authToken.isUsed) {
        res.status(409).json({
          success: false,
          message: 'Authentication token has already been used',
          error: {
            code: 'TOKEN_ALREADY_USED',
            message: 'This authentication token has already been used.'
          }
        });
        return;
      }

      // Generate QR auth URL for this token
      const qrAuthUrl = this.authService.generateQrAuthUrl(authToken.token);

      // Token is valid - return success response
      res.status(200).json({
        success: true,
        message: 'Authentication token details retrieved successfully',
        data: {
          tokenId: authToken.tokenId,
          userId: authToken.userId,
          qrAuthUrl,
          expiresAt: authToken.expiresAt.toISOString(),
          expiresInSeconds: authToken.getRemainingTimeSeconds(),
          createdAt: authToken.createdAt.toISOString(),
          isExpired: authToken.isExpired(),
          isUsed: authToken.isUsed,
          usedAt: authToken.usedAt?.toISOString(),
          metadata: authToken.metadata,
          instructions: {
            message: 'Use this URL in a QR code or access it directly for authentication.',
            useEndpoint: `/api/auth/qr-link/${authToken.tokenId}/use`,
            method: 'POST'
          }
        }
      });

    } catch (error) {
      this.handleControllerError(res, error, 'Failed to get authentication token details');
    }
  }

  /**
   * POST /auth/qr/validate
   * Validate QR authentication token from request body
   */
  async validateQrAuthToken(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.body;

      if (!token || typeof token !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Token is required',
          error: {
            code: 'INVALID_TOKEN',
            message: 'Token must be provided in the request body'
          }
        });
        return;
      }

      // Verify and decode the JWT token
      try {
        this.authService.verifyToken(token);
      } catch (error) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired authentication token',
          error: {
            code: 'TOKEN_INVALID',
            message: 'The authentication token is invalid or has expired'
          }
        });
        return;
      }

      // Find the token in the database first
      const foundToken = await this.authTokenRepository.findByToken(token);
      if (!foundToken) {
        res.status(404).json({
          success: false,
          message: 'Authentication token not found',
          error: {
            code: 'TOKEN_NOT_FOUND',
            message: 'The authentication token was not found in the system'
          }
        });
        return;
      }

      // Validate the token using its UUID
      const authToken = await this.authTokenRepository.validateToken(foundToken.tokenId);

      res.status(200).json({
        success: true,
        message: 'Authentication token is valid',
        data: {
          tokenId: authToken.tokenId,
          userId: authToken.userId,
          expiresAt: authToken.expiresAt.toISOString(),
          expiresInSeconds: authToken.getRemainingTimeSeconds(),
          isValid: authToken.isValid(),
          metadata: authToken.metadata
        }
      });

    } catch (error) {
      this.handleControllerError(res, error, 'Token validation failed');
    }
  }

  /**
   * Handle controller errors with proper error responses
   */
  private handleControllerError(res: Response, error: any, defaultMessage: string): void {
    if (error instanceof AuthTokenNotFoundError) {
      res.status(404).json({
        success: false,
        message: 'Authentication token not found',
        error: {
          code: 'TOKEN_NOT_FOUND',
          message: error.message
        }
      });
    } else if (error instanceof AuthTokenExpiredError) {
      res.status(410).json({
        success: false,
        message: 'Authentication token has expired',
        error: {
          code: 'TOKEN_EXPIRED',
          message: error.message
        }
      });
    } else if (error instanceof AuthTokenUsedError) {
      res.status(409).json({
        success: false,
        message: 'Authentication token has already been used',
        error: {
          code: 'TOKEN_ALREADY_USED',
          message: error.message
        }
      });
    } else if (error instanceof AuthTokenError) {
      res.status(400).json({
        success: false,
        message: 'Authentication token error',
        error: {
          code: 'TOKEN_ERROR',
          message: error.message
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: defaultMessage,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred during QR authentication',
          details: process.env['NODE_ENV'] === 'development' ? error.stack : undefined
        }
      });
    }
  }
}
