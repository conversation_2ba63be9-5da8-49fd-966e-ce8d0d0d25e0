name: Reusable - ECS Config Update

on:
  workflow_call:
    inputs:
      service-name:
        required: true
        type: string
        description: "ECS service name (key in ecs_services.yaml)"
      image-uri:
        required: true
        type: string
        description: "Docker image URI to update in config"
      environment-name:
        required: true
        type: string
        description: "Environment name (uat/prod) for branch naming"
      config-path:
        required: false
        type: string
        description: "Path to ecs_services.yaml config file"
        default: "infra/iac/core/configs/ecs_services.yaml"
    outputs:
      pr-number:
        description: "Pull request number created"
        value: ${{ jobs.update-config.outputs.pr-number }}
      pr-url:
        description: "Pull request URL"
        value: ${{ jobs.update-config.outputs.pr-url }}

jobs:
  update-config:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    outputs:
      pr-number: ${{ steps.create-pr.outputs.result }}
      pr-url: ${{ steps.output-details.outputs.pr-url }}
      deployment-status: ${{ steps.output-details.outputs.deployment-status }}
      branch-name: ${{ steps.output-details.outputs.branch-name }}
      image-changed: ${{ steps.check-image.outputs.changed }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Setup Git and tools
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

          # Install yq for YAML processing
          sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
          sudo chmod +x /usr/local/bin/yq

      - name: Create feature branch
        run: |
          BRANCH_NAME="deploy/${{ inputs.service-name }}-${{ inputs.environment-name }}-$(date +%Y%m%d-%H%M%S)"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          git checkout -b $BRANCH_NAME

      - name: Check if image has changed
        id: check-image
        run: |
          echo "🔍 Checking if image has changed..."
          CURRENT_IMAGE=$(yq eval '.${{ inputs.service-name }}.container_image' ${{ inputs.config-path }})
          NEW_IMAGE="${{ inputs.image-uri }}"

          echo "📝 Current image: $CURRENT_IMAGE"
          echo "🆕 New image: $NEW_IMAGE"

          if [ "$CURRENT_IMAGE" = "$NEW_IMAGE" ]; then
            echo "⚠️ Image unchanged - skipping deployment"
            echo "changed=false" >> $GITHUB_OUTPUT
            echo "SKIP_REASON=Image unchanged: $NEW_IMAGE" >> $GITHUB_ENV
          else
            echo "✅ Image changed - proceeding with deployment"
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Update ECS service config
        if: steps.check-image.outputs.changed == 'true'
        run: |
          echo "🔄 Updating ${{ inputs.service-name }} image in ${{ inputs.config-path }}"

          # Use yq to update the container_image for the specific service
          yq eval '.${{ inputs.service-name }}.container_image = "${{ inputs.image-uri }}"' -i ${{ inputs.config-path }}

          echo "✅ Updated config for ${{ inputs.service-name }}:"
          yq eval '.${{ inputs.service-name }}' ${{ inputs.config-path }}

          echo "🔍 Verifying change:"
          echo "New image: $(yq eval '.${{ inputs.service-name }}.container_image' ${{ inputs.config-path }})"

      - name: Commit and push changes
        if: steps.check-image.outputs.changed == 'true'
        run: |
          git add ${{ inputs.config-path }}

          if git diff --staged --quiet; then
            echo "⚠️ No changes detected in config file"
            exit 0
          fi

          git commit -m "🚀 Deploy ${{ inputs.service-name }} to ${{ inputs.environment-name }}

          - Service: ${{ inputs.service-name }}
          - Image: ${{ inputs.image-uri }}
          - Environment: ${{ inputs.environment-name }}
          - Updated: $(date -u +'%Y-%m-%d %H:%M:%S UTC')

          Auto-generated by GitHub Actions"

          git push origin $BRANCH_NAME

      - name: Skip deployment notification
        if: steps.check-image.outputs.changed == 'false'
        run: |
          echo "⏭️ Deployment skipped: $SKIP_REASON"
          echo "No pull request will be created."

      - name: Create Pull Request
        if: steps.check-image.outputs.changed == 'true'
        id: create-pr
        uses: actions/github-script@v7
        with:
          script: |
            const { data: pullRequest } = await github.rest.pulls.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚀 Update ${{ inputs.service-name }} image for ${{ inputs.environment-name }} deployment`,
              head: '${{ env.BRANCH_NAME }}',
              base: context.payload.repository.default_branch,
              body: `## ECS Service Configuration Update

            **Service:** \`${{ inputs.service-name }}\`
            **Environment:** \`${{ inputs.environment-name }}\`
            **New Image:** \`${{ inputs.image-uri }}\`
            **Config File:** \`${{ inputs.config-path }}\`

            ### Changes Made
            - Updated \`container_image\` for \`${{ inputs.service-name }}\` service
            - New image tag will be deployed to \`${{ inputs.environment-name }}\`

            ### Deployment Flow
            1. ✅ **Config Updated** - This PR updates the service configuration
            2. 🔄 **Auto-Merge** - PR will be automatically merged
            3. 🚀 **Terraform Deploy** - Merge to main triggers Terraform deployment
            4. 📦 **ECS Update** - Terraform updates ECS service with new image

            > **Note:** This is a configuration-only update. The actual ECS deployment will be handled by Terraform when this PR is merged.

            ---
            *Auto-generated by GitHub Actions CI/CD Pipeline*`
            });
            return pullRequest.number;

      - name: Auto-merge Pull Request
        if: steps.check-image.outputs.changed == 'true' && steps.create-pr.outputs.result
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = ${{ steps.create-pr.outputs.result }};

            // Wait a moment for PR to be fully created
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Merge the pull request
            await github.rest.pulls.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: prNumber,
              merge_method: 'squash',
              commit_title: `🚀 Update ${{ inputs.service-name }} image for ${{ inputs.environment-name }}`,
              commit_message: `Automated config update\n\nService: ${{ inputs.service-name }}\nImage: ${{ inputs.image-uri }}\nEnvironment: ${{ inputs.environment-name }}`
            });

            console.log(`✅ Pull request #${prNumber} has been automatically merged`);

      - name: Clean up branch
        if: steps.check-image.outputs.changed == 'true' && steps.create-pr.outputs.result
        run: |
          git push origin --delete ${{ env.BRANCH_NAME }}
        continue-on-error: true

      - name: Output PR details
        id: output-details
        run: |
          if [ "${{ steps.check-image.outputs.changed }}" = "true" ]; then
            PR_NUMBER="${{ steps.create-pr.outputs.result }}"
            PR_URL="https://github.com/${{ github.repository }}/pull/${PR_NUMBER}"
            echo "✅ Pull Request created successfully!"
            echo "PR Number: ${PR_NUMBER}"
            echo "PR URL: ${PR_URL}"
            echo "Branch: ${{ env.BRANCH_NAME }}"
            echo "pr-url=${PR_URL}" >> $GITHUB_OUTPUT
            echo "deployment-status=deployed" >> $GITHUB_OUTPUT
            echo "branch-name=${{ env.BRANCH_NAME }}" >> $GITHUB_OUTPUT
          else
            echo "⏭️ Deployment skipped - no changes needed"
            echo "Current image is already: ${{ inputs.image-uri }}"
            echo "pr-url=" >> $GITHUB_OUTPUT
            echo "deployment-status=skipped" >> $GITHUB_OUTPUT
            echo "branch-name=" >> $GITHUB_OUTPUT
          fi

      - name: Generate deployment summary
        run: |
          echo "## 🚀 ECS Service Config Update" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ steps.check-image.outputs.changed }}" = "true" ]; then
            echo "### ✅ Configuration Updated" >> $GITHUB_STEP_SUMMARY
            echo "- **Service**: \`${{ inputs.service-name }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **Environment**: \`${{ inputs.environment-name }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **New Image**: \`${{ inputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **Branch**: \`${{ env.BRANCH_NAME }}\`" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🔗 Quick Links" >> $GITHUB_STEP_SUMMARY
            echo "- 📋 **[View Pull Request #${{ steps.create-pr.outputs.result }}](${{ steps.output-details.outputs.pr-url }})** - See what was changed" >> $GITHUB_STEP_SUMMARY
            echo "- 📝 **[View Config File](https://github.com/${{ github.repository }}/blob/main/${{ inputs.config-path }})** - ecs_services.yaml" >> $GITHUB_STEP_SUMMARY
            echo "- 🔄 **[View Actions](https://github.com/${{ github.repository }}/actions)** - Monitor Terraform deployment" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🎯 Next Steps" >> $GITHUB_STEP_SUMMARY
            echo "1. **PR Auto-Merged** ✅ - Configuration changes are now in main branch" >> $GITHUB_STEP_SUMMARY
            echo "2. **Terraform Deploy** 🔄 - Will be triggered automatically by the merge" >> $GITHUB_STEP_SUMMARY
            echo "3. **ECS Update** 📦 - Service will be updated with new image" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ⏭️ No Changes Needed" >> $GITHUB_STEP_SUMMARY
            echo "- **Service**: \`${{ inputs.service-name }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **Current Image**: \`${{ inputs.image-uri }}\`" >> $GITHUB_STEP_SUMMARY
            echo "- **Status**: Service already running the requested image version" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🔗 Quick Links" >> $GITHUB_STEP_SUMMARY
            echo "- 📝 **[View Config File](https://github.com/${{ github.repository }}/blob/main/${{ inputs.config-path }})** - ecs_services.yaml" >> $GITHUB_STEP_SUMMARY
            echo "- 🔄 **[View Actions](https://github.com/${{ github.repository }}/actions)** - Monitor other deployments" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            echo "### 🎯 Status" >> $GITHUB_STEP_SUMMARY
            echo "✅ **No action required** - Service is already up to date" >> $GITHUB_STEP_SUMMARY
          fi
