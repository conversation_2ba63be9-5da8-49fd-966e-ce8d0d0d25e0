variable "bucket_name" {
  description = "The name of the bucket."
}

variable "cloudfront_origin_access_identity" {
  default     = null
  description = "CloudFront Origin Access Identity for the bucket."
}

variable "website_configuration" {
  description = "Website configuration"
  type = object({
    index_document_suffix = optional(string, "index.html")
    error_document_key    = optional(string, "error.html")
  })
  default = null
}

variable "force_destroy" {
  description = "Object bucket will be force to destroy"
  default     = false
}
