import { inject, injectable } from 'tsyringe';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import {
  ISessionDomainService,
  SessionHealthScore,
  SessionAction
} from './ISessionDomainService';
import { SessionEntity, SessionStatus } from '../entities/SessionEntity';

/**
 * Session domain service implementation
 * Contains business logic that doesn't belong to a single entity
 */
@injectable()
export class SessionDomainService implements ISessionDomainService {
  private readonly defaultTtlHours: number;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private configService: IConfigService
  ) {
    this.defaultTtlHours = parseInt(this.configService.getOptional('SESSION_TTL_HOURS', '72'));
  }

  /**
   * Validate if a new session can be created for the user
   */
  async validateSessionCreation(userId: string): Promise<void> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('User ID is required and must be a string');
    }

    if (userId.length < 3 || userId.length > 100) {
      throw new Error('User ID must be between 3 and 100 characters');
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(userId)) {
      throw new Error('User ID can only contain alphanumeric characters, underscores, and hyphens');
    }

    this.logger.debug('Session creation validation passed', { userId });
  }

  /**
   * Calculate TTL timestamp for session expiration
   */
  calculateTTL(createdAt: Date, ttlHours?: number): number {
    const hours = ttlHours || this.defaultTtlHours;
    const expirationTime = createdAt.getTime() + (hours * 60 * 60 * 1000);
    return Math.floor(expirationTime / 1000); // Unix timestamp
  }

  /**
   * Check if an existing session can be reused
   */
  isSessionReusable(session: SessionEntity): boolean {
    // Session is reusable if it's not expired and has valid auth state
    if (session.isExpired()) {
      return false;
    }

    // Connected sessions are always reusable
    if (session.status === 'connected' && session.authState) {
      return true;
    }

    // QR pending sessions are reusable if not too old
    if (session.status === 'qr_pending') {
      const qrAge = Date.now() - session.updatedAt.getTime();
      const qrTimeoutMs = parseInt(this.configService.getOptional('QR_CODE_TIMEOUT_SECONDS', '60')) * 1000;
      return qrAge < qrTimeoutMs;
    }

    // Disconnected sessions with auth state can be reconnected
    if (session.status === 'disconnected' && session.authState) {
      return true;
    }

    return false;
  }

  /**
   * Determine if a new session can be created given existing sessions
   */
  canCreateNewSession(userId: string, existingSessions: SessionEntity[]): boolean {
    // Check if user already has a session
    const userSession = existingSessions.find(s => s.userId === userId);
    if (userSession && this.isSessionReusable(userSession)) {
      return false; // Should reuse existing session
    }

    // Check global session limit (this would need to be implemented with a global counter)
    // For now, we'll allow creation
    return true;
  }

  /**
   * Calculate session health score based on various factors
   */
  calculateSessionHealth(session: SessionEntity): SessionHealthScore {
    let score = 0;
    const factors = {
      uptime: 0,
      stability: 0,
      authState: 0,
      lastActivity: 0
    };
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Base score for existing session
    score += 20;

    // Status-based scoring
    switch (session.status) {
      case 'connected':
        score += 50;
        factors.stability = 50;
        break;
      case 'qr_pending':
        score += 30;
        factors.stability = 30;
        issues.push('Waiting for QR code scan');
        recommendations.push('Scan QR code to complete authentication');
        break;
      case 'connecting':
        score += 25;
        factors.stability = 25;
        issues.push('Connection in progress');
        break;
      case 'disconnected':
        score += 10;
        factors.stability = 10;
        issues.push('Session disconnected');
        recommendations.push('Reconnect session');
        break;
      case 'error':
        score += 5;
        factors.stability = 5;
        issues.push('Session in error state');
        recommendations.push('Terminate and recreate session');
        break;
      default:
        score += 15;
        factors.stability = 15;
    }

    // Uptime scoring
    if (session.connectedAt) {
      const uptime = Date.now() - session.connectedAt.getTime();
      const uptimeHours = uptime / (1000 * 60 * 60);
      
      if (uptimeHours > 24) {
        factors.uptime = 20;
        score += 20;
      } else if (uptimeHours > 1) {
        factors.uptime = 15;
        score += 15;
      } else {
        factors.uptime = 10;
        score += 10;
      }
    }

    // Auth state scoring
    if (session.authState) {
      factors.authState = 15;
      score += 15;
    } else {
      issues.push('No authentication state');
      recommendations.push('Complete authentication process');
    }

    // Last activity scoring
    const lastActivity = session.updatedAt.getTime();
    const hoursSinceActivity = (Date.now() - lastActivity) / (1000 * 60 * 60);
    
    if (hoursSinceActivity < 1) {
      factors.lastActivity = 15;
      score += 15;
    } else if (hoursSinceActivity < 24) {
      factors.lastActivity = 10;
      score += 10;
    } else if (hoursSinceActivity < 72) {
      factors.lastActivity = 5;
      score += 5;
      issues.push('Session inactive for over 24 hours');
    } else {
      issues.push('Session inactive for over 72 hours');
      recommendations.push('Consider terminating inactive session');
    }

    // Expiration check
    if (session.isExpired()) {
      score = Math.max(0, score - 30);
      issues.push('Session expired');
      recommendations.push('Terminate expired session');
    }

    return {
      score: Math.min(100, Math.max(0, score)),
      factors,
      issues,
      recommendations
    };
  }

  /**
   * Determine if a session should be automatically cleaned up
   */
  shouldCleanupSession(session: SessionEntity): boolean {
    // Clean up expired sessions
    if (session.isExpired()) {
      return true;
    }

    // Clean up sessions in error state for too long
    if (session.status === 'error') {
      const errorAge = Date.now() - session.updatedAt.getTime();
      const maxErrorAge = 24 * 60 * 60 * 1000; // 24 hours
      return errorAge > maxErrorAge;
    }

    // Clean up disconnected sessions without auth state
    if (session.status === 'disconnected' && !session.authState) {
      const disconnectAge = Date.now() - (session.disconnectedAt?.getTime() || session.updatedAt.getTime());
      const maxDisconnectAge = 6 * 60 * 60 * 1000; // 6 hours
      return disconnectAge > maxDisconnectAge;
    }

    // Clean up QR pending sessions that are too old
    if (session.status === 'qr_pending') {
      const qrAge = Date.now() - session.updatedAt.getTime();
      const maxQrAge = 10 * 60 * 1000; // 10 minutes
      return qrAge > maxQrAge;
    }

    return false;
  }

  /**
   * Get recommended action for a session based on its state
   */
  getRecommendedAction(session: SessionEntity): SessionAction {
    if (session.isExpired()) {
      return 'cleanup';
    }

    switch (session.status) {
      case 'connected':
        if (session.isActive()) {
          return 'maintain';
        } else {
          return 'monitor';
        }
      
      case 'qr_pending':
        const qrAge = Date.now() - session.updatedAt.getTime();
        const qrTimeoutMs = parseInt(this.configService.getOptional('QR_CODE_TIMEOUT_SECONDS', '60')) * 1000;
        return qrAge > qrTimeoutMs ? 'refresh_auth' : 'maintain';
      
      case 'connecting':
        return 'monitor';
      
      case 'disconnected':
        return session.canReconnect() ? 'reconnect' : 'cleanup';
      
      case 'error':
        return 'terminate';
      
      default:
        return 'monitor';
    }
  }

  /**
   * Validate session transition from one status to another
   */
  validateStatusTransition(currentStatus: SessionStatus, newStatus: SessionStatus): boolean {
    const validTransitions: Record<SessionStatus, SessionStatus[]> = {
      'initializing': ['qr_pending', 'connecting', 'error'],
      'qr_pending': ['connecting', 'connected', 'disconnected', 'error'],
      'connecting': ['connected', 'disconnected', 'error'],
      'connected': ['disconnected', 'error'],
      'disconnected': ['connecting', 'qr_pending', 'error'],
      'error': ['initializing', 'qr_pending', 'connecting', 'disconnected']
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * Calculate optimal reconnection delay based on failure history
   */
  calculateReconnectionDelay(failureCount: number): number {
    // Exponential backoff with jitter
    const baseDelay = parseInt(this.configService.getOptional('RECONNECT_DELAY_MS', '5000'));
    const maxDelay = 5 * 60 * 1000; // 5 minutes max
    
    const exponentialDelay = baseDelay * Math.pow(2, Math.min(failureCount, 6));
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    
    return Math.min(exponentialDelay + jitter, maxDelay);
  }
}
