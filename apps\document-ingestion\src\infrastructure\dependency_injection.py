"""
Dependency Injection Container for Document Ingestion Pipeline

This module provides a dependency injection container using the dependency-injector
library that wires up all dependencies according to clean architecture principles.
"""

from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

from ..application.use_cases import ProcessDocumentUseCase
from ..domain.interfaces import (
    IConfiguration,
    ICSVProcessor,
    IDocumentRepository,
    IEmbeddingService,
    IEventPublisher,
    IFileStorage,
    ILogger,
    IMetricsCollector,
    IProductEmbeddingRepository,
)
from ..domain.services import (
    DocumentValidationService,
    EmbeddingGenerationService,
    MultiTenantSecurityService,
    SearchableTextGenerator,
)
from .configuration import EnvironmentConfiguration
from .events import LoggingEventPublisher
from .logging import DomainLogger, setup_lambda_logging
from .metrics import create_metrics_collector
from .processors import EnhancedCSVProcessor
from .repositories import SupabaseDocumentRepository, SupabaseProductEmbeddingRepository
from .services import RetryableOpenAIEmbeddingService
from .storage import S3FileStorage


class ApplicationContainer(containers.DeclarativeContainer):
    """Dependency injection container using dependency-injector library"""

    # Configuration
    config = providers.Singleton(EnvironmentConfiguration)

    # Core Infrastructure
    logger = providers.Singleton(DomainLogger, config=config)

    metrics_collector = providers.Singleton(
        create_metrics_collector,
        logger=logger,
        use_cloudwatch=providers.Factory(lambda config: config.is_production(), config=config),
    )

    # External Services
    document_repository = providers.Singleton(
        SupabaseDocumentRepository, config=config, logger=logger
    )

    embedding_repository = providers.Singleton(
        SupabaseProductEmbeddingRepository, config=config, logger=logger
    )

    file_storage = providers.Singleton(S3FileStorage, logger=logger, metrics=metrics_collector)

    embedding_service = providers.Singleton(
        RetryableOpenAIEmbeddingService, config=config, logger=logger, metrics=metrics_collector
    )

    csv_processor = providers.Singleton(EnhancedCSVProcessor, config=config, logger=logger)

    event_publisher = providers.Singleton(LoggingEventPublisher, logger=logger)

    # Domain Services
    searchable_text_generator = providers.Singleton(SearchableTextGenerator)

    embedding_generation_service = providers.Singleton(
        EmbeddingGenerationService,
        embedding_service=embedding_service,
        text_generator=searchable_text_generator,
        logger=logger,
    )

    document_validation_service = providers.Singleton(DocumentValidationService, logger=logger)

    security_service = providers.Singleton(MultiTenantSecurityService, logger=logger)

    # Use Cases
    process_document_use_case = providers.Singleton(
        ProcessDocumentUseCase,
        document_repo=document_repository,
        embedding_repo=embedding_repository,
        file_storage=file_storage,
        csv_processor=csv_processor,
        embedding_service=embedding_generation_service,
        validation_service=document_validation_service,
        security_service=security_service,
        event_publisher=event_publisher,
        logger=logger,
        metrics=metrics_collector,
        config=config,
    )
