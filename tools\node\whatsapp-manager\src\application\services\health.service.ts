import { injectable, inject } from 'tsyringe';
import { IConfigService } from '../../shared/config/interfaces';
import { ILoggerService } from '../../shared/logging/interfaces';

export interface HealthCheck {
  status: 'ok' | 'warn' | 'error';
  responseTime?: number;
  message?: string;
  details?: object;
}

export interface HealthResponse {
  status: 'ok' | 'degraded' | 'down';
  environment: string;
  version: string;
  uptime: number;
  timestamp: string;
  checks: {
    database: HealthCheck;
    memory: HealthCheck;
    dependencies: HealthCheck[];
  };
}

export interface IHealthService {
  getHealth(): Promise<HealthResponse>;
  checkDatabase(): Promise<HealthCheck>;
  checkMemory(): HealthCheck;
}

@injectable()
export class HealthService implements IHealthService {
  private readonly startTime: number;

  constructor(
    @inject('IConfigService') private configService: IConfigService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {
    this.startTime = Date.now();
  }

  async getHealth(): Promise<HealthResponse> {
    const startTime = Date.now();
    
    try {
      // Run all health checks
      const [databaseCheck, memoryCheck] = await Promise.all([
        this.checkDatabase(),
        Promise.resolve(this.checkMemory()),
      ]);

      // Determine overall status
      const checks = [databaseCheck, memoryCheck];
      const overallStatus = this.determineOverallStatus(checks);

      const response: HealthResponse = {
        status: overallStatus,
        environment: this.configService.getEnvironment(),
        version: process.env['npm_package_version'] || '1.0.0',
        uptime: Math.floor((Date.now() - this.startTime) / 1000),
        timestamp: new Date().toISOString(),
        checks: {
          database: databaseCheck,
          memory: memoryCheck,
          dependencies: [], // Future: external service checks
        },
      };

      const responseTime = Date.now() - startTime;
      this.logger.debug('Health check completed', {
        status: overallStatus,
        responseTime,
        checks: response.checks,
      });

      return response;
    } catch (error) {
      this.logger.error('Health check failed', { error });
      
      return {
        status: 'down',
        environment: this.configService.getEnvironment(),
        version: process.env['npm_package_version'] || '1.0.0',
        uptime: Math.floor((Date.now() - this.startTime) / 1000),
        timestamp: new Date().toISOString(),
        checks: {
          database: { status: 'error', message: 'Health check failed' },
          memory: { status: 'error', message: 'Health check failed' },
          dependencies: [],
        },
      };
    }
  }

  async checkDatabase(): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      // For now, just check if configuration is valid
      // In future iterations, this will ping DynamoDB
      const dbConfig = this.configService.getDatabase();
      
      if (!dbConfig.tableName || !dbConfig.region) {
        return {
          status: 'error',
          message: 'Database configuration invalid',
          responseTime: Date.now() - startTime,
        };
      }

      return {
        status: 'ok',
        message: 'Database configuration valid',
        responseTime: Date.now() - startTime,
        details: {
          tableName: dbConfig.tableName,
          region: dbConfig.region,
          endpoint: dbConfig.endpoint || 'AWS DynamoDB',
        },
      };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Database check failed',
        responseTime: Date.now() - startTime,
      };
    }
  }

  checkMemory(): HealthCheck {
    const memoryUsage = process.memoryUsage();
    const totalMemoryMB = Math.round(memoryUsage.rss / 1024 / 1024);
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    
    // Warning if heap usage is over 80%
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    
    let status: 'ok' | 'warn' | 'error' = 'ok';
    let message = 'Memory usage normal';
    
    if (heapUsagePercent > 90) {
      status = 'error';
      message = 'Memory usage critical';
    } else if (heapUsagePercent > 80) {
      status = 'warn';
      message = 'Memory usage high';
    }

    return {
      status,
      message,
      details: {
        rss: `${totalMemoryMB}MB`,
        heapUsed: `${heapUsedMB}MB`,
        heapTotal: `${heapTotalMB}MB`,
        heapUsagePercent: `${Math.round(heapUsagePercent)}%`,
      },
    };
  }

  private determineOverallStatus(checks: HealthCheck[]): 'ok' | 'degraded' | 'down' {
    const hasError = checks.some(check => check.status === 'error');
    const hasWarning = checks.some(check => check.status === 'warn');
    
    if (hasError) {
      return 'down';
    }
    if (hasWarning) {
      return 'degraded';
    }
    return 'ok';
  }
}
