import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

/**
 * Rate limiter configuration options
 */
export interface RateLimiterOptions {
  windowMs: number;
  max: number;
  message?: any;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
}

/**
 * Create a rate limiter middleware with custom configuration
 */
export function rateLimiter(options: RateLimiterOptions) {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: options.message || {
      success: false,
      message: 'Too many requests, please try again later',
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded'
      }
    },
    standardHeaders: options.standardHeaders ?? true,
    legacyHeaders: options.legacyHeaders ?? false,
    skipSuccessfulRequests: options.skipSuccessfulRequests ?? false,
    skipFailedRequests: options.skipFailedRequests ?? false,
    keyGenerator: options.keyGenerator || ((req: Request) => {
      return req.ip || req.connection.remoteAddress || 'unknown';
    }),
    handler: (_req: Request, res: Response) => {
      res.status(429).json(options.message || {
        success: false,
        message: 'Too many requests, please try again later',
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Rate limit exceeded'
        }
      });
    }
  });
}

/**
 * Default rate limiter for general API endpoints
 */
export const defaultRateLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // 100 requests per window
});

/**
 * Strict rate limiter for sensitive endpoints
 */
export const strictRateLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10 // 10 requests per window
});

/**
 * Lenient rate limiter for read-only endpoints
 */
export const lenientRateLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200 // 200 requests per window
});
