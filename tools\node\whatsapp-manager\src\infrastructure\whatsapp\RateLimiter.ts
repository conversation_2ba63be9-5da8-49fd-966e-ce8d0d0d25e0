import { inject, injectable } from 'tsyringe';

import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';

/**
 * Rate limit configuration
 */
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (identifier: string) => string;
}

/**
 * Rate limit result
 */
export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalHits: number;
}

/**
 * Rate limit entry
 */
interface RateLimitEntry {
  count: number;
  resetTime: Date;
  firstHit: Date;
}

/**
 * Rate limiter for WhatsApp operations
 * Implements sliding window rate limiting with per-user and global limits
 */
@injectable()
export class RateLimiter {
  private readonly userLimits = new Map<string, RateLimitEntry>();
  private readonly globalLimits = new Map<string, RateLimitEntry>();
  private readonly cleanupInterval: NodeJS.Timeout;

  // Default configurations
  private readonly defaultConfigs: { [key: string]: RateLimitConfig } = {
    messages: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60, // 60 messages per minute per user
      skipSuccessfulRequests: false,
      skipFailedRequests: true
    },
    qr_generation: {
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 5, // 5 QR generations per 5 minutes per user
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    },
    session_creation: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 10, // 10 session creations per hour per user
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    },
    global_messages: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 1000, // 1000 messages per minute globally
      skipSuccessfulRequests: false,
      skipFailedRequests: true
    }
  };

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    // Override default configs with environment variables
    this.defaultConfigs['messages'].maxRequests = this.config.getOptional('RATE_LIMIT_MESSAGES_PER_MIN', 60);
    this.defaultConfigs['global_messages'].maxRequests = this.config.getOptional('RATE_LIMIT_GLOBAL_MESSAGES_PER_MIN', 1000);
    this.defaultConfigs['session_creation'].maxRequests = this.config.getOptional('RATE_LIMIT_SESSION_CREATION_PER_HOUR', 10);
    this.defaultConfigs['qr_generation'].maxRequests = this.config.getOptional('RATE_LIMIT_QR_GENERATION_PER_5MIN', 5);

    this.logger.debug('Rate limiter initialized with configs', {
      messages: this.defaultConfigs['messages'],
      global_messages: this.defaultConfigs['global_messages'],
      session_creation: this.defaultConfigs['session_creation'],
      qr_generation: this.defaultConfigs['qr_generation']
    });

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Check if request is allowed for user
   */
  async checkUserLimit(
    userId: string,
    operation: string,
    config?: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    const finalConfig = { ...this.defaultConfigs[operation], ...config };
    
    if (!finalConfig) {
      throw new Error(`Unknown operation: ${operation}`);
    }

    const key = this.generateKey(userId, operation);
    const now = new Date();

    let entry = this.userLimits.get(key);

    // Create new entry if doesn't exist or window has expired
    if (!entry || now.getTime() >= entry.resetTime.getTime()) {
      entry = {
        count: 0,
        resetTime: new Date(now.getTime() + finalConfig.windowMs),
        firstHit: now
      };
      this.userLimits.set(key, entry);
    }

    // Check if limit exceeded
    const allowed = entry.count < finalConfig.maxRequests;
    
    if (allowed) {
      entry.count++;
    }

    const result: RateLimitResult = {
      allowed,
      remaining: Math.max(0, finalConfig.maxRequests - entry.count),
      resetTime: entry.resetTime,
      totalHits: entry.count
    };

    if (!allowed) {
      this.logger.warn('User rate limit exceeded', {
        userId,
        operation,
        count: entry.count,
        maxRequests: finalConfig.maxRequests,
        resetTime: entry.resetTime
      });
    }

    return result;
  }

  /**
   * Check if request is allowed globally
   */
  async checkGlobalLimit(
    operation: string,
    config?: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    const globalConfigKey = `global_${operation}`;
    const baseGlobalConfig = this.defaultConfigs[globalConfigKey];

    if (!baseGlobalConfig) {
      // If no global config exists for this operation, allow the request
      this.logger.debug('No global rate limit config found, allowing request', {
        operation,
        globalConfigKey
      });
      return {
        allowed: true,
        remaining: Infinity,
        resetTime: new Date(Date.now() + 60000),
        totalHits: 0
      };
    }

    const finalConfig = { ...baseGlobalConfig, ...config };

    const key = `global_${operation}`;
    const now = new Date();

    let entry = this.globalLimits.get(key);

    // Create new entry if doesn't exist or window has expired
    if (!entry || now.getTime() >= entry.resetTime.getTime()) {
      entry = {
        count: 0,
        resetTime: new Date(now.getTime() + finalConfig.windowMs),
        firstHit: now
      };
      this.globalLimits.set(key, entry);
    }

    // Check if limit exceeded
    const allowed = entry.count < finalConfig.maxRequests;
    
    if (allowed) {
      entry.count++;
    }

    const result: RateLimitResult = {
      allowed,
      remaining: Math.max(0, finalConfig.maxRequests - entry.count),
      resetTime: entry.resetTime,
      totalHits: entry.count
    };

    if (!allowed) {
      this.logger.warn('Global rate limit exceeded', {
        operation,
        count: entry.count,
        maxRequests: finalConfig.maxRequests,
        resetTime: entry.resetTime
      });
    }

    return result;
  }

  /**
   * Check both user and global limits
   */
  async checkLimits(
    userId: string,
    operation: string,
    userConfig?: Partial<RateLimitConfig>,
    globalConfig?: Partial<RateLimitConfig>
  ): Promise<{
    userLimit: RateLimitResult;
    globalLimit: RateLimitResult;
    allowed: boolean;
  }> {
    const [userLimit, globalLimit] = await Promise.all([
      this.checkUserLimit(userId, operation, userConfig),
      this.checkGlobalLimit(operation, globalConfig)
    ]);

    const allowed = userLimit.allowed && globalLimit.allowed;

    this.logger.debug('Rate limit check completed', {
      userId,
      operation,
      userLimit: {
        allowed: userLimit.allowed,
        remaining: userLimit.remaining,
        totalHits: userLimit.totalHits
      },
      globalLimit: {
        allowed: globalLimit.allowed,
        remaining: globalLimit.remaining,
        totalHits: globalLimit.totalHits
      },
      finalAllowed: allowed
    });

    return {
      userLimit,
      globalLimit,
      allowed
    };
  }

  /**
   * Record a failed request (if configured to skip failed requests)
   */
  recordFailure(userId: string, operation: string): void {
    const config = this.defaultConfigs[operation];
    if (!config || !config.skipFailedRequests) {
      return;
    }

    const key = this.generateKey(userId, operation);
    const entry = this.userLimits.get(key);
    
    if (entry && entry.count > 0) {
      entry.count--;
      this.logger.debug('Recorded failed request, decremented count', {
        userId,
        operation,
        newCount: entry.count
      });
    }
  }

  /**
   * Get current usage for user
   */
  getUserUsage(userId: string, operation: string): {
    count: number;
    remaining: number;
    resetTime: Date;
    windowMs: number;
  } | null {
    const config = this.defaultConfigs[operation];
    if (!config) {
      return null;
    }

    const key = this.generateKey(userId, operation);
    const entry = this.userLimits.get(key);
    const now = new Date();

    if (!entry || now.getTime() >= entry.resetTime.getTime()) {
      return {
        count: 0,
        remaining: config.maxRequests,
        resetTime: new Date(now.getTime() + config.windowMs),
        windowMs: config.windowMs
      };
    }

    return {
      count: entry.count,
      remaining: Math.max(0, config.maxRequests - entry.count),
      resetTime: entry.resetTime,
      windowMs: config.windowMs
    };
  }

  /**
   * Reset limits for user
   */
  resetUserLimits(userId: string, operation?: string): void {
    if (operation) {
      const key = this.generateKey(userId, operation);
      this.userLimits.delete(key);
      this.logger.info('Reset user rate limit', { userId, operation });
    } else {
      // Reset all limits for user
      const keysToDelete = Array.from(this.userLimits.keys())
        .filter(key => key.startsWith(`${userId}:`));
      
      keysToDelete.forEach(key => this.userLimits.delete(key));
      this.logger.info('Reset all user rate limits', { userId, operationsReset: keysToDelete.length });
    }
  }

  /**
   * Reset global limits
   */
  resetGlobalLimits(operation?: string): void {
    if (operation) {
      const key = `global_${operation}`;
      this.globalLimits.delete(key);
      this.logger.info('Reset global rate limit', { operation });
    } else {
      this.globalLimits.clear();
      this.logger.info('Reset all global rate limits');
    }
  }

  /**
   * Generate cache key
   */
  private generateKey(userId: string, operation: string): string {
    return `${userId}:${operation}`;
  }

  /**
   * Cleanup expired entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    let userCleaned = 0;
    let globalCleaned = 0;

    // Cleanup user limits
    for (const [key, entry] of this.userLimits.entries()) {
      if (now.getTime() >= entry.resetTime.getTime()) {
        this.userLimits.delete(key);
        userCleaned++;
      }
    }

    // Cleanup global limits
    for (const [key, entry] of this.globalLimits.entries()) {
      if (now.getTime() >= entry.resetTime.getTime()) {
        this.globalLimits.delete(key);
        globalCleaned++;
      }
    }

    if (userCleaned > 0 || globalCleaned > 0) {
      this.logger.debug('Cleaned up expired rate limit entries', {
        userCleaned,
        globalCleaned,
        remainingUser: this.userLimits.size,
        remainingGlobal: this.globalLimits.size
      });
    }
  }

  /**
   * Get statistics
   */
  getStatistics(): {
    userLimitsCount: number;
    globalLimitsCount: number;
    configurations: { [key: string]: RateLimitConfig };
  } {
    return {
      userLimitsCount: this.userLimits.size,
      globalLimitsCount: this.globalLimits.size,
      configurations: { ...this.defaultConfigs }
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.userLimits.clear();
    this.globalLimits.clear();
    this.logger.info('RateLimiter destroyed');
  }
}
