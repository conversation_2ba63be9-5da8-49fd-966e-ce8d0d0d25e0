# Lambda Services Configuration
# This file defines all Lambda services deployed across environments.
# Environment-specific settings are handled via Terraform variables.

sample-lambda:
  # Container configuration
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-nodejs-sample:11
  # Environment variables (AWS_REGION is automatically set by Lambda)
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"
  # API Gateway path patterns (like ECS) - simple and intuitive
  api_gateway:
    path_patterns:
      - "/sample" # Exact match for /sample
      - "/sample/*" # Matches /sample/* (any HTTP method)
  # Tags
  tags:
    Service: "Sample Lambda"
    Team: "Platform"
    ECRAccount: "************"

document-ingestion:
  # Container configuration
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest

  # Environment variables
  environment_variables:
    PYTHON_ENV: "production"
    LOG_LEVEL: "info"

  # API Gateway path patterns - unique paths to avoid conflicts
  api_gateway:
    path_patterns:
      - "/document-ingestion"      # Exact match for /document-ingestion
      - "/document-ingestion/*"    # Matches /document-ingestion/* (any HTTP method)

  # Lambda configuration
  timeout: 300      # 5 minutes for document processing
  memory_size: 1024 # Higher memory for document processing

  # Tags
  tags:
    Service: "Document Ingestion Lambda"
    Team: "Platform"
    ECRAccount: "************"
