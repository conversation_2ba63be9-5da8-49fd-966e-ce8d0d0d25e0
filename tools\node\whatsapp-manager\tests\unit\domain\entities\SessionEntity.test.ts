import { SessionEntity, SessionStatus, AuthenticationState } from '../../../../src/domain/entities/SessionEntity';

describe('SessionEntity', () => {
  const validUserId = 'test-user-123';
  const validSessionId = 'session-123456789';
  const mockAuthState: AuthenticationState = {
    creds: { test: 'creds' } as any,
    keys: { test: 'keys' } as any
  };

  describe('constructor', () => {
    it('should create a valid session entity', () => {
      const now = new Date();
      const session = new SessionEntity(
        validUserId,
        validSessionId,
        null,
        null,
        'initializing',
        now,
        now
      );

      expect(session.userId).toBe(validUserId);
      expect(session.sessionId).toBe(validSessionId);
      expect(session.phoneNumber).toBeNull();
      expect(session.authState).toBeNull();
      expect(session.status).toBe('initializing');
      expect(session.createdAt).toBe(now);
      expect(session.updatedAt).toBe(now);
    });

    it('should throw error for invalid user ID', () => {
      const now = new Date();
      
      expect(() => new SessionEntity('', validSessionId, null, null, 'initializing', now, now))
        .toThrow('User ID must be a non-empty string');
      
      expect(() => new SessionEntity('ab', validSessionId, null, null, 'initializing', now, now))
        .toThrow('User ID must be between 3 and 100 characters');
      
      expect(() => new SessionEntity('user@invalid', validSessionId, null, null, 'initializing', now, now))
        .toThrow('User ID can only contain alphanumeric characters, underscores, and hyphens');
    });

    it('should throw error for invalid session ID', () => {
      const now = new Date();
      
      expect(() => new SessionEntity(validUserId, '', null, null, 'initializing', now, now))
        .toThrow('Session ID must be a non-empty string');
      
      expect(() => new SessionEntity(validUserId, '1234567', null, null, 'initializing', now, now))
        .toThrow('Session ID must be at least 8 characters');
    });
  });

  describe('create factory method', () => {
    it('should create a new session with default values', () => {
      const session = SessionEntity.create(validUserId);

      expect(session.userId).toBe(validUserId);
      expect(session.sessionId).toBeDefined();
      expect(session.sessionId.length).toBeGreaterThanOrEqual(8);
      expect(session.phoneNumber).toBeNull();
      expect(session.authState).toBeNull();
      expect(session.status).toBe('initializing');
      expect(session.createdAt).toBeInstanceOf(Date);
      expect(session.updatedAt).toBeInstanceOf(Date);
      expect(session.ttl).toBeDefined();
      expect(session.deviceName).toBe('WhatsApp Manager');
      expect(session.browserName).toBe('Chrome');
    });

    it('should create a new session with custom device and browser names', () => {
      const deviceName = 'Custom Device';
      const browserName = 'Custom Browser';
      const session = SessionEntity.create(validUserId, deviceName, browserName);

      expect(session.deviceName).toBe(deviceName);
      expect(session.browserName).toBe(browserName);
    });

    it('should set TTL to 72 hours from creation', () => {
      const session = SessionEntity.create(validUserId);
      const expectedTtl = Math.floor((session.createdAt.getTime() + (72 * 60 * 60 * 1000)) / 1000);
      
      expect(session.ttl).toBe(expectedTtl);
    });
  });

  describe('markAsConnected', () => {
    it('should mark session as connected with phone number', () => {
      const session = SessionEntity.create(validUserId);
      const phoneNumber = '+1234567890';
      const beforeConnect = new Date();
      
      session.markAsConnected(phoneNumber);
      
      expect(session.phoneNumber).toBe(phoneNumber);
      expect(session.status).toBe('connected');
      expect(session.connectedAt).toBeInstanceOf(Date);
      expect(session.connectedAt!.getTime()).toBeGreaterThanOrEqual(beforeConnect.getTime());
      expect(session.disconnectedAt).toBeUndefined();
      expect(session.updatedAt.getTime()).toBeGreaterThanOrEqual(beforeConnect.getTime());
    });

    it('should throw error for invalid phone number', () => {
      const session = SessionEntity.create(validUserId);
      
      expect(() => session.markAsConnected('')).toThrow('Phone number must be a non-empty string');
      expect(() => session.markAsConnected('invalid')).toThrow('Invalid phone number format');
    });
  });

  describe('markAsDisconnected', () => {
    it('should mark session as disconnected', () => {
      const session = SessionEntity.create(validUserId);
      session.markAsConnected('+1234567890');
      const beforeDisconnect = new Date();
      
      session.markAsDisconnected();
      
      expect(session.status).toBe('disconnected');
      expect(session.disconnectedAt).toBeInstanceOf(Date);
      expect(session.disconnectedAt!.getTime()).toBeGreaterThanOrEqual(beforeDisconnect.getTime());
      expect(session.updatedAt.getTime()).toBeGreaterThanOrEqual(beforeDisconnect.getTime());
    });
  });

  describe('updateAuthState', () => {
    it('should update authentication state', () => {
      const session = SessionEntity.create(validUserId);
      const beforeUpdate = new Date();
      
      session.updateAuthState(mockAuthState);
      
      expect(session.authState).toBe(mockAuthState);
      expect(session.updatedAt.getTime()).toBeGreaterThanOrEqual(beforeUpdate.getTime());
    });
  });

  describe('updateStatus', () => {
    it('should update session status', () => {
      const session = SessionEntity.create(validUserId);
      const beforeUpdate = new Date();
      
      session.updateStatus('qr_pending');
      
      expect(session.status).toBe('qr_pending');
      expect(session.updatedAt.getTime()).toBeGreaterThanOrEqual(beforeUpdate.getTime());
    });
  });

  describe('isExpired', () => {
    it('should return false for session without TTL', () => {
      const now = new Date();
      const session = new SessionEntity(
        validUserId,
        validSessionId,
        null,
        null,
        'initializing',
        now,
        now
      );
      
      expect(session.isExpired()).toBe(false);
    });

    it('should return false for non-expired session', () => {
      const session = SessionEntity.create(validUserId);
      expect(session.isExpired()).toBe(false);
    });

    it('should return true for expired session', () => {
      const now = new Date();
      const expiredTtl = Math.floor((now.getTime() - 1000) / 1000); // 1 second ago
      const session = new SessionEntity(
        validUserId,
        validSessionId,
        null,
        null,
        'initializing',
        now,
        now,
        undefined,
        undefined,
        expiredTtl
      );
      
      expect(session.isExpired()).toBe(true);
    });
  });

  describe('isActive', () => {
    it('should return true for connected non-expired session', () => {
      const session = SessionEntity.create(validUserId);
      session.markAsConnected('+1234567890');
      
      expect(session.isActive()).toBe(true);
    });

    it('should return false for disconnected session', () => {
      const session = SessionEntity.create(validUserId);
      session.updateStatus('disconnected');
      
      expect(session.isActive()).toBe(false);
    });

    it('should return false for expired session', () => {
      const now = new Date();
      const expiredTtl = Math.floor((now.getTime() - 1000) / 1000);
      const session = new SessionEntity(
        validUserId,
        validSessionId,
        '+1234567890',
        mockAuthState,
        'connected',
        now,
        now,
        now,
        undefined,
        expiredTtl
      );
      
      expect(session.isActive()).toBe(false);
    });
  });

  describe('canReconnect', () => {
    it('should return true for session with auth state and not expired', () => {
      const session = SessionEntity.create(validUserId);
      session.updateAuthState(mockAuthState);
      
      expect(session.canReconnect()).toBe(true);
    });

    it('should return false for session without auth state', () => {
      const session = SessionEntity.create(validUserId);
      
      expect(session.canReconnect()).toBe(false);
    });

    it('should return false for expired session', () => {
      const now = new Date();
      const expiredTtl = Math.floor((now.getTime() - 1000) / 1000);
      const session = new SessionEntity(
        validUserId,
        validSessionId,
        null,
        mockAuthState,
        'disconnected',
        now,
        now,
        undefined,
        undefined,
        expiredTtl
      );
      
      expect(session.canReconnect()).toBe(false);
    });
  });

  describe('getSessionDuration', () => {
    it('should return null for session without connection time', () => {
      const session = SessionEntity.create(validUserId);
      
      expect(session.getSessionDuration()).toBeNull();
    });

    it('should return duration for connected session', () => {
      const session = SessionEntity.create(validUserId);
      const connectTime = new Date(Date.now() - 5000); // 5 seconds ago
      session.connectedAt = connectTime;
      
      const duration = session.getSessionDuration();
      expect(duration).toBeGreaterThanOrEqual(4000); // At least 4 seconds
      expect(duration).toBeLessThan(10000); // Less than 10 seconds
    });

    it('should return duration between connect and disconnect times', () => {
      const session = SessionEntity.create(validUserId);
      const connectTime = new Date(Date.now() - 10000); // 10 seconds ago
      const disconnectTime = new Date(Date.now() - 5000); // 5 seconds ago
      session.connectedAt = connectTime;
      session.disconnectedAt = disconnectTime;
      
      const duration = session.getSessionDuration();
      expect(duration).toBe(5000); // Exactly 5 seconds
    });
  });

  describe('toJSON and fromJSON', () => {
    it('should serialize and deserialize correctly', () => {
      const session = SessionEntity.create(validUserId, 'Test Device', 'Test Browser');
      session.markAsConnected('+1234567890');
      session.updateAuthState(mockAuthState);
      
      const json = session.toJSON();
      const deserialized = SessionEntity.fromJSON(json);
      
      expect(deserialized.userId).toBe(session.userId);
      expect(deserialized.sessionId).toBe(session.sessionId);
      expect(deserialized.phoneNumber).toBe(session.phoneNumber);
      expect(deserialized.status).toBe(session.status);
      expect(deserialized.createdAt.getTime()).toBe(session.createdAt.getTime());
      expect(deserialized.updatedAt.getTime()).toBe(session.updatedAt.getTime());
      expect(deserialized.connectedAt?.getTime()).toBe(session.connectedAt?.getTime());
      expect(deserialized.ttl).toBe(session.ttl);
      expect(deserialized.deviceName).toBe(session.deviceName);
      expect(deserialized.browserName).toBe(session.browserName);
    });
  });

  describe('clone', () => {
    it('should create a clone with updated properties', () => {
      const session = SessionEntity.create(validUserId);
      const newPhoneNumber = '+9876543210';
      const newStatus: SessionStatus = 'connected';
      
      const cloned = session.clone({
        phoneNumber: newPhoneNumber,
        status: newStatus
      });
      
      expect(cloned.userId).toBe(session.userId);
      expect(cloned.sessionId).toBe(session.sessionId);
      expect(cloned.phoneNumber).toBe(newPhoneNumber);
      expect(cloned.status).toBe(newStatus);
      expect(cloned.createdAt).toBe(session.createdAt);
      expect(cloned.updatedAt.getTime()).toBeGreaterThanOrEqual(session.updatedAt.getTime());
    });
  });
});
