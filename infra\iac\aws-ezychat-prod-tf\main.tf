module "core" {
  source = "../core"

  env = "prod"

  # Network Configuration for Production Environment
  # Using 10.1.x.x range for Production (separate from UAT)
  vpc_cidr             = "********/16" # Prod: ********/16 (65,536 IPs)
  public_subnet_count  = 2             # 2 public subnets for ALB
  private_subnet_count = 2             # 2 private subnets for future use

  # NAT Gateway Configuration
  enable_nat_gateway   = false    # Prod: Disabled for cost savings (not needed with public subnets)
  nat_gateway_strategy = "single" # Not used when NAT Gateway is disabled
  use_private_subnets  = false    # Prod: Use public subnets for cost savings

  # Domain Naming:
  # Prod: {api_subdomain}.{domain_name} (e.g., api.ezychat.ai)
  # UAT: {api_subdomain}.uat.{domain_name} (e.g., api.uat.ezychat.ai)

  # Cost Optimization:
  # Both UAT and Prod use the same cost-optimized setup
  # - No NAT Gateway (~$45/month savings)
  # - ECS tasks in public subnets with direct internet access
  # - Private subnets reserved for future high-security requirements

  tags = {
    env     = "prod"
    project = "ezychat"
  }

  providers = {
    aws        = aws
    aws.master = aws.master
  }
}

# Pass through all outputs from core module
output "core" {
  value       = module.core
  description = "All outputs from the core module"
}
