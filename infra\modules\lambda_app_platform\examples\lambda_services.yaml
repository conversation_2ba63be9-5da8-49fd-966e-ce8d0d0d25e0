# Sample Lambda Services Configuration
# This file demonstrates the YAML schema for configuring Lambda functions

# Container-based Lambda example
whatsapp-personal-lambda:
  # Container configuration
  image_repository_uri: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-personal-lambda:latest"
  timeout: 30
  memory_size: 512
  publish: true  # Auto-publish new versions (default: true)
  
  # Environment variables
  environment_variables:
    NODE_ENV: "production"
    AWS_REGION: "ap-southeast-1"
    API_VERSION: "v1"
    LOG_LEVEL: "info"
  
  # Secrets from AWS Secrets Manager
  secrets:
    DATABASE_URL: "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:lambda/database-url"
    API_KEY: "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:lambda/api-key"
  
  # API Gateway path patterns (simplified like ECS)
  api_gateway:
    path_patterns:
      - "/whatsapp-api/*"  # Matches /whatsapp-api and /whatsapp-api/* (any HTTP method)
  
  # Custom tags
  tags:
    Service: "WhatsApp API"
    Team: "Backend"

# Zip-based Lambda example
notification-service:
  # Zip-based Lambda configuration
  filename: "notification-service.zip"
  source_code_hash: "placeholder-hash"
  handler: "index.handler"
  runtime: "nodejs18.x"
  timeout: 15
  memory_size: 256
  
  # Environment variables
  environment_variables:
    SERVICE_NAME: "notification-service"
    ENVIRONMENT: "production"
  
  # VPC configuration (optional)
  vpc_config:
    subnet_ids: 
      - "subnet-12345678"
      - "subnet-87654321"
    security_group_ids:
      - "sg-abcdef123"
  
  # Custom IAM policy for SNS access
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "sns:Publish",
            "sns:CreateTopic",
            "sns:ListTopics"
          ],
          "Resource": "*"
        },
        {
          "Effect": "Allow",
          "Action": [
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents"
          ],
          "Resource": "arn:aws:logs:*:*:*"
        }
      ]
    }
  
  # API Gateway routes
  api_gateway:
    routes:
      - path: "/notifications"
        method: "POST"
      - path: "/notifications/{id}"
        method: "GET"
  
  # Custom tags
  tags:
    Service: "Notification Service"
    Team: "Platform"

# Python-based Lambda example
data-processor:
  # Zip-based Python Lambda
  filename: "data-processor.zip"
  handler: "app.lambda_handler"
  runtime: "python3.9"
  timeout: 60
  memory_size: 1024
  
  # Environment variables
  environment_variables:
    PYTHONPATH: "/var/runtime"
    ENVIRONMENT: "production"
    BATCH_SIZE: "100"
  
  # Secrets
  secrets:
    DB_PASSWORD: "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:db-password"
  
  # Custom IAM policy for S3 and DynamoDB access
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject"
          ],
          "Resource": "arn:aws:s3:::data-processing-bucket/*"
        },
        {
          "Effect": "Allow",
          "Action": [
            "dynamodb:GetItem",
            "dynamodb:PutItem",
            "dynamodb:UpdateItem",
            "dynamodb:DeleteItem",
            "dynamodb:Query",
            "dynamodb:Scan"
          ],
          "Resource": "arn:aws:dynamodb:*:*:table/ProcessingResults"
        }
      ]
    }
  
  # API Gateway routes
  api_gateway:
    routes:
      - path: "/data/process"
        method: "POST"
      - path: "/data/status/{jobId}"
        method: "GET"
  
  # Custom tags
  tags:
    Service: "Data Processor"
    Team: "Analytics"

# Simple health check Lambda
health-check:
  # Minimal configuration
  filename: "health-check.zip"
  handler: "index.handler"
  runtime: "nodejs18.x"
  timeout: 5
  memory_size: 128
  publish: false  # Disable auto-publish for development
  
  # Simple environment variable
  environment_variables:
    SERVICE_VERSION: "1.0.0"
  
  # Single API route
  api_gateway:
    routes:
      - path: "/health"
        method: "GET"
  
  # Tags
  tags:
    Service: "Health Check"
    Team: "Platform"
