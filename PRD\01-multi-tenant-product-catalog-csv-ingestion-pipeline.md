# 🧾 Product Requirements Document (PRD)

## 📘 Document Info

**Feature Name:** Multi-Tenant Product Catalog CSV Ingestion Pipeline
**Author:** Backend Development Team
**Date Created:** 2025-01-27
**Last Updated:** 2025-08-08
**Version:** v2.0
**Epic:** EzyChat Multi-Tenant RAG Implementation
**Component:** Backend/Data Pipeline
**Priority:** High
**Labels:** `multi-tenant`, `rag`, `vector-embeddings`, `aws-lambda`, `python3.12`, `backend`, `clean-architecture`

---

## 🔥 1. Executive Summary

Implement a serverless, multi-tenant CSV product catalog ingestion pipeline that enables businesses to upload product catalogs with complete tenant isolation, automatic vector embedding generation, and seamless integration with the EzyChat RAG-powered WhatsApp sales assistant. This pipeline processes CSV files uploaded to S3, generates vector embeddings using OpenAI, and stores data in Supabase with pgvector for fast semantic search capabilities.

## 🎯 2. Goals and Objectives

| Goal | Description |
|------|-------------|
| **Multi-Tenant Support** | Enable independent product catalog management for multiple business tenants with complete data isolation |
| **Scalable Processing** | Support high-volume product catalog processing (10,000+ products per tenant within 5 minutes) |
| **RAG Integration** | Seamless connection with existing WhatsApp chatbot RAG system for enhanced product search capabilities |
| **Operational Excellence** | Achieve >99% success rate for valid CSV files with comprehensive monitoring and error handling |

## 👤 3. User Personas

| Persona | Role | Pain Point | How This Helps |
|---------|------|------------|----------------|
| **Sarah, Business Owner** | Tenant Admin | Manual product catalog updates are time-consuming and error-prone | Automated CSV upload with instant processing and validation |
| **Mike, Sales Manager** | End User | Cannot quickly find accurate product information during customer conversations | Fast semantic search through vector embeddings enables instant product discovery |
| **Alex, DevOps Engineer** | System Admin | Needs reliable, scalable infrastructure for multiple tenants | Clean architecture with monitoring, logging, and automated deployment |

## 🧭 4. Scope

### ✅ In Scope
- Multi-tenant CSV product catalog ingestion pipeline
- S3-triggered Lambda processing with tenant isolation
- Vector embedding generation using OpenAI text-embedding-3-small
- Supabase pgvector storage with Row Level Security (RLS)
- Clean Architecture implementation with dependency injection
- Comprehensive error handling and retry mechanisms
- Structured logging and monitoring integration
- Unit and integration testing coverage
- CI/CD pipeline with Terraform infrastructure as code

### ❌ Out of Scope
- Real-time CSV validation API (future enhancement)
- Support for additional file formats (JSON, XML) - Phase 2
- Advanced product categorization using ML - Phase 2
- GraphQL API for product search - Phase 2
- Mobile app integration - separate epic
- Payment processing integration - separate epic

## 🧩 5. Requirements

### 🖥️ Functional Requirements

| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| **FR1** | Automatic processing trigger when CSV files uploaded to `s3://bucket/{user_id}/uploaded/` | High | S3 event-driven Lambda invocation |
| **FR2** | Multi-tenant data storage in Supabase with complete tenant isolation | High | Uses `user_id` partitioning with RLS |
| **FR3** | Vector embedding generation using OpenAI text-embedding-3-small | High | Configurable dimensions (512/1536) |
| **FR4** | Vector storage in Supabase pgvector with tenant metadata | High | Enables filtered semantic searches |
| **FR5** | Real-time processing status tracking per tenant | Medium | Status: uploaded/processing/completed/failed |
| **FR6** | CSV format support with configurable column mapping | Medium | Standard CSV with header row validation |
| **FR7** | Data validation and cleaning with quality scoring | Medium | Handles missing values and data normalization |
| **FR8** | Batch processing for large files (1000+ rows) | High | Memory optimization for Lambda constraints |
| **FR9** | Comprehensive error handling with rollback mechanisms | High | Detailed logging with tenant context |
| **FR10** | File lifecycle management across S3 paths | High | uploaded → processing → completed/failed |

### ⚙️ Non-Functional Requirements

| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| **NFR1** | Process 10,000+ products per tenant within 5 minutes | High | Performance benchmark |
| **NFR2** | Lambda function execution within 15-minute timeout | High | AWS Lambda constraint |
| **NFR3** | Memory utilization under 3008MB Lambda constraint | High | Requires batch processing optimization |
| **NFR4** | Vector search latency under 100ms for product queries | High | 95th percentile target |
| **NFR5** | Tenant ID extraction and validation from S3 keys | High | Security requirement |
| **NFR6** | Row Level Security (RLS) implementation in Supabase | High | Data isolation enforcement |
| **NFR7** | Comprehensive audit logging for all operations | Medium | Compliance and debugging |
| **NFR8** | Horizontal scaling for concurrent tenant processing | Medium | Multi-tenant scalability |
| **NFR9** | Retry mechanisms for transient failures | Medium | Exponential backoff strategy |
| **NFR10** | Monitoring and alerting for pipeline health | Medium | CloudWatch integration |

## 📐 6. UX/UI Design

**Note**: This is a backend pipeline with no direct user interface. Integration points:

- **S3 Upload Interface**: Existing tenant dashboard for CSV file uploads
- **Status Monitoring**: Integration with tenant admin panel for processing status
- **Error Reporting**: Structured error messages displayed in tenant interface
- **WhatsApp Integration**: Seamless product search through existing chatbot interface

**Key Integration Points**: [Tenant Dashboard], [Admin Panel], [WhatsApp Bot Interface]

## 🔗 7. Dependencies

| Dependency | Owner | Status | Notes |
|------------|-------|--------|-------|
| **AWS S3 Bucket Setup** | DevOps Team | Not Started | Tenant-scoped bucket structure |
| **Supabase Database Schema** | Backend Team | In Progress | Tables, RLS policies, pgvector setup |
| **OpenAI API Access** | Backend Team | Complete | API key and rate limit configuration |
| **Terraform Infrastructure** | DevOps Team | Not Started | AWS resources provisioning |
| **WhatsApp Bot Integration** | Frontend Team | In Progress | Vector search API endpoints |
| **Tenant Authentication System** | Security Team | Complete | User ID extraction for multi-tenancy |

## 🧪 8. Acceptance Criteria

Define what success looks like for the multi-tenant CSV ingestion pipeline:

**Core Functionality**:
- [ ] When a tenant uploads CSV to `{user_id}/uploaded/`, processing automatically triggers
- [ ] CSV data is validated, cleaned, and stored with complete tenant isolation
- [ ] Vector embeddings are generated and stored in Supabase pgvector
- [ ] Processing status is tracked and accessible per tenant
- [ ] Failed processing scenarios trigger appropriate error handling and rollback

**Performance & Scale**:
- [ ] Pipeline processes 10,000 products within 5 minutes per tenant
- [ ] Multiple tenants can process files concurrently without interference
- [ ] Memory usage stays under Lambda 3008MB constraint
- [ ] Vector search queries return results in under 100ms

**Security & Isolation**:
- [ ] Tenants cannot access other tenants' data at any pipeline stage
- [ ] All operations are logged with tenant context for audit trails
- [ ] RLS policies prevent cross-tenant data contamination
- [ ] API keys and credentials are securely managed

## 🧪 9. Test Plan (QA)

| Test Case | Expected Result | Priority |
|-----------|-----------------|----------|
| **Upload CSV to tenant path** | Processing automatically triggers, file moves to processing folder | High |
| **Process valid 10K product CSV** | All products stored in Supabase with embeddings within 5 minutes | High |
| **Multi-tenant concurrent processing** | No cross-tenant data contamination, isolated processing | High |
| **Invalid CSV format upload** | Validation errors logged, file moved to failed folder | Medium |
| **Lambda timeout scenario** | Graceful handling, partial processing rollback | Medium |
| **OpenAI API rate limit hit** | Exponential backoff retry, eventual success | Medium |
| **Supabase connection failure** | Error logged, retry mechanism activated | Medium |
| **Large file memory optimization** | Processing completes under 3008MB Lambda limit | High |
| **Tenant ID extraction validation** | Only valid tenant IDs processed, invalid ones rejected | High |
| **Vector search performance** | Product queries return results under 100ms | Medium |

## 📌 10. Open Questions

- [ ] Should we support incremental CSV updates (append/update existing products)?
- [ ] What's the maximum CSV file size we should support per tenant?
- [ ] Should we implement real-time progress updates during processing?
- [ ] Do we need support for custom CSV column mappings per tenant?
- [ ] Should failed processing attempts have automatic retry limits?
- [ ] What level of data quality scoring should trigger processing warnings?

## 🏗️ 11. Technical Overview

### 11.1 Architecture Diagram/Project Structure

**System Architecture**:
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   S3 Bucket     │    │   Lambda Function │    │   Supabase DB       │
│                 │    │                  │    │                     │
│ /{user_id}/     │───▶│  CSV Processor   │───▶│  Products Table     │
│   uploaded/     │    │                  │    │  Embeddings Table   │
│   processing/   │    │  Clean Arch      │    │  (pgvector + RLS)   │
│   completed/    │    │  Python 3.12     │    │                     │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   OpenAI API     │
                       │                  │
                       │ text-embedding-  │
                       │   3-small        │
                       └──────────────────┘
```

**Clean Architecture Structure**:
```
src/
├── domain/           # Business entities and rules
│   ├── entities.py   # Document, ProductData, EmbeddingVector
│   ├── interfaces.py # Repository and service interfaces
│   └── services.py   # Domain services
├── application/      # Use cases and orchestration
│   └── use_cases.py  # ProcessDocumentUseCase
├── infrastructure/   # External dependencies
│   ├── configuration.py
│   ├── logging.py
│   ├── metrics.py
│   └── dependency_injection.py
└── presentation/     # Controllers and handlers
    └── lambda_handler.py
```

### 11.2 Key Technical Notes

- **Runtime Environment**: Python 3.12 with AWS Lambda base image for containerization
- **Multi-Tenant Architecture**: Tenant isolation through S3 path prefixes and Supabase RLS policies
- **Vector Storage**: Supabase pgvector extension with configurable embedding dimensions (512/1536)
- **Processing Optimization**: Batch processing for OpenAI API efficiency and Lambda memory constraints
- **Error Handling**: Comprehensive retry mechanisms with exponential backoff for transient failures
- **Monitoring**: Structured JSON logging with CloudWatch integration for observability
- **Security**: Row Level Security (RLS) enforcement and secure credential management
- **Scalability**: Horizontal scaling design supporting concurrent tenant processing
- **Infrastructure**: Terraform-managed AWS resources with environment-specific configurations
- **Testing**: >90% test coverage with unit, integration, and performance testing strategies

### 11.3 Data Flow

1. **CSV Upload**: Tenant uploads product catalog CSV to `s3://bucket/{user_id}/uploaded/filename.csv`
2. **Event Trigger**: S3 object creation event automatically invokes Lambda function
3. **Tenant Validation**: Lambda extracts and validates `user_id` from S3 object key path
4. **File Processing**: CSV is moved to `processing/` folder and parsed using Pandas
5. **Data Validation**: Product data is validated, cleaned, and quality-scored
6. **Embedding Generation**: OpenAI text-embedding-3-small generates vector embeddings in batches
7. **Data Storage**: Products and embeddings stored in Supabase with tenant isolation via RLS
8. **Status Update**: Processing status updated and file moved to `completed/` or `failed/` folder
9. **Integration**: Vector embeddings become available for WhatsApp chatbot semantic search
10. **Monitoring**: All operations logged with tenant context for audit and debugging

---

## 📋 Implementation Checklist

### Phase 1: Infrastructure Setup (Weeks 1-2)
- [ ] Terraform configuration for AWS resources (S3, Lambda, IAM roles)
- [ ] Supabase database schema with products and embeddings tables
- [ ] Row Level Security (RLS) policies for tenant isolation
- [ ] pgvector extension setup and configuration
- [ ] Environment configuration management (.env.example)

### Phase 2: Core Pipeline (Weeks 2-3)
- [ ] Domain layer entities and interfaces implementation
- [ ] Lambda handler with S3 event processing
- [ ] CSV parsing and validation logic with Pandas
- [ ] OpenAI API integration with batch processing
- [ ] Supabase repository with tenant-aware operations
- [ ] Comprehensive error handling and retry mechanisms

### Phase 3: Testing & Quality (Week 4)
- [ ] Unit tests for domain and application layers (>90% coverage)
- [ ] Integration tests with real AWS services
- [ ] Performance testing for 10K product processing
- [ ] Multi-tenant isolation verification tests
- [ ] Security testing and vulnerability assessment

### Phase 4: Deployment & Monitoring (Week 4)
- [ ] CI/CD pipeline with automated testing and deployment
- [ ] CloudWatch monitoring, metrics, and alerting setup
- [ ] Documentation and deployment guides
- [ ] Production environment configuration and security review

---

**Document Version**: 2.0
**Last Updated**: 2025-08-08
**Next Review**: 2025-08-15
**Approval Status**: Ready for Implementation