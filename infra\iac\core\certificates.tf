# Create wildcard ACM certificate for ALB (in the current region)
# Covers all subdomains: *.{domain} (e.g., api.uat.{domain}, api.prod.{domain}, etc.)
module "alb_certificate" {
  source = "../../modules/acm_certificate"

  domain_name               = local.certificate_domain_name
  route53_zone_id           = data.aws_route53_zone.main_domain.zone_id
  subject_alternative_names = ["*.uat.${var.domain_name}"]

  tags = merge(local.this.tags, {
    Name = "${replace(replace(var.domain_name, "*", "wildcard"), ".", "-")}-certificate"
  })

  providers = {
    aws.certificate_region = aws
    aws.route53_region     = aws.master
  }
}

# Output the certificate ARNs
output "alb_certificate_arn" {
  value       = module.alb_certificate.certificate_arn
  description = "The ARN of the ALB certificate"
}
