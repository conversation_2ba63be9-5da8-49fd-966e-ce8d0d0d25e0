{"name": "lambda-nodejs-sample", "version": "1.0.0", "description": "Sample Node.js Lambda function for ECR deployment", "main": "index.js", "scripts": {"start": "node index.js", "build": "echo 'Build completed'", "test": "echo 'No tests specified, but passing for CI/CD pipeline'", "clean": "rm -rf node_modules"}, "keywords": ["lambda", "nodejs", "aws", "ecr", "container"], "author": "ezychat team", "license": "MIT", "engines": {"node": ">=18.0.0"}}