import { inject, injectable } from 'tsyringe';
import { EventEmitter } from 'events';
import {
  makeWASocket,
  DisconnectReason,
  ConnectionState,
  WASocket,
  fetchLatestBaileysVersion
} from '@whiskeysockets/baileys';

import { BaileysAuthStateAdapter } from './BaileysAuthStateAdapter';
import { QRCodeManager } from './QRCodeManager';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { SessionStatus } from '../../domain/entities/SessionEntity';

/**
 * Connection state interface
 */
export interface ConnectionInfo {
  userId: string;
  socket: WASocket;
  status: SessionStatus;
  phoneNumber?: string;
  qrCode?: string;
  lastActivity: Date;
  reconnectAttempts: number;
  connectionStartTime: Date;
  isConnecting: boolean;
}

/**
 * Connection options interface
 */
export interface ConnectionOptions {
  printQRInTerminal?: boolean;
  browser?: [string, string, string];
  syncFullHistory?: boolean;
  markOnlineOnConnect?: boolean;
  defaultQueryTimeoutMs?: number;
  connectTimeoutMs?: number;
  keepAliveIntervalMs?: number;
  retryRequestDelayMs?: number;
  maxMsgRetryCount?: number;
  fireInitQueries?: boolean;
  emitOwnEvents?: boolean;
  generateHighQualityLinkPreview?: boolean;
  patchMessageBeforeSending?: (message: any) => any;
}

/**
 * Baileys connection manager for real WhatsApp socket connections
 * Handles connection lifecycle, reconnection, and event management
 */
@injectable()
export class BaileysConnectionManager extends EventEmitter {
  private readonly connections = new Map<string, ConnectionInfo>();
  private readonly maxConcurrentConnections: number;
  private readonly connectionTimeoutMs: number;
  private readonly maxReconnectAttempts: number;
  private readonly reconnectDelayMs: number;
  private readonly keepAliveInterval: NodeJS.Timeout;

  constructor(
    @inject('BaileysAuthStateAdapter') private authStateAdapter: BaileysAuthStateAdapter,
    @inject('QRCodeManager') private qrCodeManager: QRCodeManager,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    super();
    this.maxConcurrentConnections = this.config.getOptional('MAX_CONCURRENT_SESSIONS', 100);
    this.connectionTimeoutMs = this.config.getOptional('CONNECTION_TIMEOUT_MS', 60000);
    this.maxReconnectAttempts = this.config.getOptional('RECONNECT_MAX_ATTEMPTS', 5);
    this.reconnectDelayMs = this.config.getOptional('RECONNECT_DELAY_MS', 5000);

    // Start keep-alive interval
    this.keepAliveInterval = setInterval(() => {
      this.performKeepAlive();
    }, 30000); // Every 30 seconds
  }

  /**
   * Create a new WhatsApp connection
   */
  async createConnection(
    userId: string,
    options?: ConnectionOptions
  ): Promise<ConnectionInfo> {
    try {
      // Check connection limits
      if (this.connections.size >= this.maxConcurrentConnections) {
        throw new Error(`Maximum concurrent connections limit reached: ${this.maxConcurrentConnections}`);
      }

      // Check if connection already exists
      if (this.connections.has(userId)) {
        const existing = this.connections.get(userId)!;
        this.logger.warn('Connection already exists', { 
          userId, 
          status: existing.status 
        });
        return existing;
      }

      this.logger.info('Creating new WhatsApp connection', { userId });

      // Get latest Baileys version
      const { version, isLatest } = await fetchLatestBaileysVersion();
      this.logger.debug('Using Baileys version', { version, isLatest });

      // Get auth state
      const { state, saveCreds } = await this.authStateAdapter.createBaileysAuthState(userId);

      // Default connection options
      const defaultOptions: ConnectionOptions = {
        printQRInTerminal: false,
        browser: ['WhatsApp Manager', 'Chrome', '1.0.0'],
        syncFullHistory: false,
        markOnlineOnConnect: true,
        defaultQueryTimeoutMs: 60000,
        connectTimeoutMs: this.connectionTimeoutMs,
        keepAliveIntervalMs: 30000,
        retryRequestDelayMs: 1000,
        maxMsgRetryCount: 5,
        fireInitQueries: true,
        emitOwnEvents: true,
        generateHighQualityLinkPreview: true
      };

      const finalOptions = { ...defaultOptions, ...options };

      // Create WASocket
      const socket = makeWASocket({
        version,
        auth: {
          creds: state.creds,
          keys: state.keys
        },
        printQRInTerminal: finalOptions.printQRInTerminal,
        browser: finalOptions.browser,
        syncFullHistory: finalOptions.syncFullHistory,
        markOnlineOnConnect: finalOptions.markOnlineOnConnect,
        defaultQueryTimeoutMs: finalOptions.defaultQueryTimeoutMs,
        connectTimeoutMs: finalOptions.connectTimeoutMs,
        keepAliveIntervalMs: finalOptions.keepAliveIntervalMs,
        retryRequestDelayMs: finalOptions.retryRequestDelayMs,
        maxMsgRetryCount: finalOptions.maxMsgRetryCount,
        fireInitQueries: finalOptions.fireInitQueries,
        emitOwnEvents: finalOptions.emitOwnEvents,
        generateHighQualityLinkPreview: finalOptions.generateHighQualityLinkPreview,
        logger: this.logger as any
      });

      // Create connection info
      const connectionInfo: ConnectionInfo = {
        userId,
        socket,
        status: 'initializing',
        lastActivity: new Date(),
        reconnectAttempts: 0,
        connectionStartTime: new Date(),
        isConnecting: true
      };

      // Store connection
      this.connections.set(userId, connectionInfo);

      // Set up event handlers
      this.setupEventHandlers(userId, socket, saveCreds);

      this.logger.info('WhatsApp connection created', {
        userId,
        status: connectionInfo.status,
        version
      });

      // Note: Connection events will be handled asynchronously
      // QR code will be available via status polling or WebSocket events
      this.logger.info('Connection setup complete, waiting for async events', {
        userId,
        status: connectionInfo.status
      });

      return connectionInfo;
    } catch (error) {
      this.logger.error('Failed to create WhatsApp connection', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Get connection info
   */
  getConnection(userId: string): ConnectionInfo | null {
    return this.connections.get(userId) || null;
  }

  /**
   * Close connection
   */
  async closeConnection(userId: string): Promise<void> {
    const connection = this.connections.get(userId);
    if (!connection) {
      this.logger.warn('Connection not found for closing', { userId });
      return;
    }

    try {
      this.logger.info('Closing WhatsApp connection', { userId });

      // Update status
      connection.status = 'disconnected';
      connection.isConnecting = false;

      // Close socket
      if (connection.socket) {
        connection.socket.end(undefined);
      }

      // Remove from connections
      this.connections.delete(userId);

      this.logger.info('WhatsApp connection closed', { userId });
    } catch (error) {
      this.logger.error('Failed to close connection', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Setup event handlers for socket
   */
  private setupEventHandlers(
    userId: string,
    socket: WASocket,
    saveCreds: () => Promise<void>
  ): void {
    // Connection updates
    socket.ev.on('connection.update', async (update: Partial<ConnectionState>) => {
      this.logger.info('Baileys connection.update event received', {
        userId,
        connectionState: update.connection,
        hasQR: !!update.qr,
        hasLastDisconnect: !!update.lastDisconnect,
        receivedPendingNotifications: update.receivedPendingNotifications
      });
      await this.handleConnectionUpdate(userId, update, saveCreds);
    });

    // Credentials update
    socket.ev.on('creds.update', async () => {
      try {
        await saveCreds();
        this.logger.debug('Credentials updated', { userId });
      } catch (error) {
        this.logger.error('Failed to save credentials', {
          userId,
          error: (error as Error).message
        });
      }
    });

    // Messages
    socket.ev.on('messages.upsert', (messageUpdate) => {
      this.handleMessagesUpsert(userId, messageUpdate);
    });

    // Message updates (delivery, read receipts)
    socket.ev.on('messages.update', (messageUpdates) => {
      this.handleMessageUpdates(userId, messageUpdates);
    });

    // Presence updates
    socket.ev.on('presence.update', (presenceUpdate) => {
      this.handlePresenceUpdate(userId, presenceUpdate);
    });

    // Chats
    socket.ev.on('chats.upsert', (chats) => {
      this.handleChatsUpsert(userId, chats);
    });

    // Contacts
    socket.ev.on('contacts.upsert', (contacts) => {
      this.handleContactsUpsert(userId, contacts);
    });
  }

  /**
   * Handle connection state updates
   */
  private async handleConnectionUpdate(
    userId: string,
    update: Partial<ConnectionState>,
    saveCreds: () => Promise<void>
  ): Promise<void> {
    const connection = this.connections.get(userId);
    if (!connection) return;

    try {
      const { connection: connectionState, lastDisconnect, qr, receivedPendingNotifications: _receivedPendingNotifications } = update;

      this.logger.debug('Connection update received', {
        userId,
        connectionState,
        hasQR: !!qr,
        hasLastDisconnect: !!lastDisconnect,
        disconnectReason: (lastDisconnect?.error as any)?.output?.statusCode || lastDisconnect?.error?.message
      });

      // Update last activity
      connection.lastActivity = new Date();

      // Handle connection state changes first (priority order matters)
      if (connectionState === 'open') {
        // Connection established
        connection.status = 'connected';
        connection.isConnecting = false;
        connection.reconnectAttempts = 0;
        
        // Extract phone number
        const phoneNumber = connection.socket.user?.id?.split(':')[0];
        if (phoneNumber) {
          connection.phoneNumber = phoneNumber;
        }

        this.logger.info('WhatsApp connection established', {
          userId,
          phoneNumber: connection.phoneNumber,
          connectionTime: Date.now() - connection.connectionStartTime.getTime()
        });

        // Save credentials
        await saveCreds();

        // Emit connected event
        this.emitConnectionEvent(userId, 'connected', { 
          phoneNumber: connection.phoneNumber 
        });
      }

      if (connectionState === 'connecting') {
        connection.status = 'connecting';
        connection.isConnecting = true;
        
        this.logger.info('WhatsApp connection in progress', { userId });
        
        // Emit connecting event
        this.emitConnectionEvent(userId, 'connecting', {});
      }

      if (connectionState === 'close') {
        // Connection closed
        connection.isConnecting = false;
        
        const shouldReconnect = this.shouldReconnect(lastDisconnect);
        const disconnectReason = this.getDisconnectReason(lastDisconnect);
        
        this.logger.info('WhatsApp connection closed', {
          userId,
          reason: disconnectReason,
          shouldReconnect,
          reconnectAttempts: connection.reconnectAttempts
        });

        if (shouldReconnect && connection.reconnectAttempts < this.maxReconnectAttempts) {
          // Attempt reconnection
          await this.attemptReconnection(userId);
        } else {
          // Mark as disconnected
          connection.status = 'disconnected';
          
          // Emit disconnected event
          this.emitConnectionEvent(userId, 'disconnected', { 
            reason: disconnectReason 
          });
        }
      }

      // Handle QR code generation after connection state updates
      if (qr && connection.status !== 'connected') {
        // Generate QR code only if not already connected
        const qrCodeEntity = await this.qrCodeManager.generateQRCode(userId, qr);
        connection.qrCode = qrCodeEntity.qrImageData;
        connection.status = 'qr_pending';
        connection.isConnecting = false;
        
        this.logger.info('QR code generated for connection', {
          userId,
          qrId: qrCodeEntity.id,
          expiresAt: qrCodeEntity.expiresAt
        });

        // Emit QR event (will be handled by WhatsAppService)
        this.emitConnectionEvent(userId, 'qr', { qrCode: qrCodeEntity.qrImageData });
      }
    } catch (error) {
      this.logger.error('Failed to handle connection update', {
        userId,
        error: (error as Error).message
      });
      
      // Emit error event
      this.emitConnectionEvent(userId, 'error', { 
        error: error as Error 
      });
    }
  }

  /**
   * Handle messages upsert event
   */
  private handleMessagesUpsert(userId: string, messageUpdate: any): void {
    try {
      const { messages, type } = messageUpdate;

      this.logger.debug('Messages upsert received', {
        userId,
        messageCount: messages?.length || 0,
        type
      });

      // Emit message events (will be handled by MessageProcessor)
      this.emitConnectionEvent(userId, 'messages.upsert', { messages, type });
    } catch (error) {
      this.logger.error('Failed to handle messages upsert', {
        userId,
        error: (error as Error).message
      });
    }
  }

  /**
   * Handle message updates (delivery, read receipts)
   */
  private handleMessageUpdates(userId: string, messageUpdates: any[]): void {
    try {
      this.logger.debug('Message updates received', {
        userId,
        updateCount: messageUpdates.length
      });

      // Emit message update events
      this.emitConnectionEvent(userId, 'messages.update', { updates: messageUpdates });
    } catch (error) {
      this.logger.error('Failed to handle message updates', {
        userId,
        error: (error as Error).message
      });
    }
  }

  /**
   * Handle presence updates
   */
  private handlePresenceUpdate(userId: string, presenceUpdate: any): void {
    try {
      this.logger.debug('Presence update received', {
        userId,
        presence: presenceUpdate
      });

      // Emit presence event
      this.emitConnectionEvent(userId, 'presence.update', presenceUpdate);
    } catch (error) {
      this.logger.error('Failed to handle presence update', {
        userId,
        error: (error as Error).message
      });
    }
  }

  /**
   * Handle chats upsert
   */
  private handleChatsUpsert(userId: string, chats: any[]): void {
    try {
      this.logger.debug('Chats upsert received', {
        userId,
        chatCount: chats.length
      });

      // Emit chats event
      this.emitConnectionEvent(userId, 'chats.upsert', { chats });
    } catch (error) {
      this.logger.error('Failed to handle chats upsert', {
        userId,
        error: (error as Error).message
      });
    }
  }

  /**
   * Handle contacts upsert
   */
  private handleContactsUpsert(userId: string, contacts: any[]): void {
    try {
      this.logger.debug('Contacts upsert received', {
        userId,
        contactCount: contacts.length
      });

      // Emit contacts event
      this.emitConnectionEvent(userId, 'contacts.upsert', { contacts });
    } catch (error) {
      this.logger.error('Failed to handle contacts upsert', {
        userId,
        error: (error as Error).message
      });
    }
  }

  /**
   * Determine if should reconnect based on disconnect reason
   */
  private shouldReconnect(lastDisconnect: any): boolean {
    if (!lastDisconnect?.error) return true;

    const statusCode = lastDisconnect.error?.output?.statusCode || lastDisconnect.error?.message;

    // Don't reconnect for these reasons
    const noReconnectCodes = [
      DisconnectReason.loggedOut,
      DisconnectReason.badSession,
      DisconnectReason.multideviceMismatch
    ];

    return !noReconnectCodes.includes(statusCode);
  }

  /**
   * Get human-readable disconnect reason
   */
  private getDisconnectReason(lastDisconnect: any): string {
    if (!lastDisconnect?.error) return 'unknown';

    const statusCode = lastDisconnect.error?.output?.statusCode || lastDisconnect.error?.message;

    switch (statusCode) {
      case DisconnectReason.badSession:
        return 'bad_session';
      case DisconnectReason.connectionClosed:
        return 'connection_closed';
      case DisconnectReason.connectionLost:
        return 'connection_lost';
      case DisconnectReason.connectionReplaced:
        return 'connection_replaced';
      case DisconnectReason.loggedOut:
        return 'logged_out';
      case DisconnectReason.multideviceMismatch:
        return 'multidevice_mismatch';
      case DisconnectReason.restartRequired:
        return 'restart_required';
      case DisconnectReason.timedOut:
        return 'timed_out';
      default:
        return `unknown_${statusCode}`;
    }
  }

  /**
   * Attempt reconnection with exponential backoff
   */
  private async attemptReconnection(userId: string): Promise<void> {
    const connection = this.connections.get(userId);
    if (!connection) return;

    try {
      connection.reconnectAttempts++;

      // Calculate delay with exponential backoff
      const delay = Math.min(
        this.reconnectDelayMs * Math.pow(2, connection.reconnectAttempts - 1),
        30000 // Max 30 seconds
      );

      this.logger.info('Attempting reconnection', {
        userId,
        attempt: connection.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
        delayMs: delay
      });

      // Wait before reconnecting
      await new Promise(resolve => setTimeout(resolve, delay));

      // Create new connection
      await this.createConnection(userId);
    } catch (error) {
      this.logger.error('Reconnection attempt failed', {
        userId,
        attempt: connection.reconnectAttempts,
        error: (error as Error).message
      });

      // If max attempts reached, mark as disconnected
      if (connection.reconnectAttempts >= this.maxReconnectAttempts) {
        connection.status = 'disconnected';
        this.emitConnectionEvent(userId, 'disconnected', {
          reason: 'max_reconnect_attempts_reached'
        });
      }
    }
  }


  /**
   * Emit connection event (to be handled by WhatsAppService)
   */
  private emitConnectionEvent(userId: string, event: string, data: any): void {
    this.logger.debug('Connection event emitted', {
      userId,
      event,
      data: typeof data === 'object' ? Object.keys(data) : data
    });
    
    // Emit event that WhatsAppService can listen to
    this.emit('connection.update', {
      userId,
      event,
      data,
      timestamp: new Date()
    });
  }

  /**
   * Perform keep-alive check on all connections
   */
  private performKeepAlive(): void {
    const now = new Date();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    const connectingTimeout = 2 * 60 * 1000; // 2 minutes timeout for connecting state

    for (const [userId, connection] of this.connections.entries()) {
      const timeSinceActivity = now.getTime() - connection.lastActivity.getTime();
      const timeSinceConnectionStart = now.getTime() - connection.connectionStartTime.getTime();

      // Handle stuck connecting connections
      if (connection.status === 'connecting' && timeSinceConnectionStart > connectingTimeout) {
        this.logger.warn('Connection stuck in connecting state, forcing reconnection', {
          userId,
          timeSinceConnectionStart,
          status: connection.status
        });

        // Force close and reconnect
        this.attemptReconnection(userId).catch(error => {
          this.logger.error('Failed to reconnect stuck connection', {
            userId,
            error: (error as Error).message
          });
        });
        continue;
      }

      // Handle stale connected connections
      if (timeSinceActivity > staleThreshold && connection.status === 'connected') {
        this.logger.warn('Connection appears stale', {
          userId,
          timeSinceActivity,
          status: connection.status
        });

        // Update last activity to current time to prevent spam
        connection.lastActivity = now;

        // Mock ping - not available in mock implementation
        this.logger.debug('Connection health check completed', { userId });
      }
    }
  }

  /**
   * Get all active connections
   */
  getAllConnections(): Map<string, ConnectionInfo> {
    return new Map(this.connections);
  }

  /**
   * Get connection statistics
   */
  getStatistics(): {
    totalConnections: number;
    connectedCount: number;
    connectingCount: number;
    disconnectedCount: number;
    qrPendingCount: number;
    averageReconnectAttempts: number;
  } {
    let connected = 0;
    let connecting = 0;
    let disconnected = 0;
    let qrPending = 0;
    let totalReconnectAttempts = 0;

    for (const connection of this.connections.values()) {
      switch (connection.status) {
        case 'connected':
          connected++;
          break;
        case 'connecting':
          connecting++;
          break;
        case 'disconnected':
          disconnected++;
          break;
        case 'qr_pending':
          qrPending++;
          break;
      }
      totalReconnectAttempts += connection.reconnectAttempts;
    }

    return {
      totalConnections: this.connections.size,
      connectedCount: connected,
      connectingCount: connecting,
      disconnectedCount: disconnected,
      qrPendingCount: qrPending,
      averageReconnectAttempts: this.connections.size > 0
        ? totalReconnectAttempts / this.connections.size
        : 0
    };
  }

  /**
   * Send message through connection
   */
  async sendMessage(userId: string, to: string, message: any): Promise<any> {
    const connection = this.connections.get(userId);
    if (!connection) {
      throw new Error(`Connection not found for user: ${userId}`);
    }

    if (connection.status !== 'connected') {
      throw new Error(`Connection not ready for user: ${userId}, status: ${connection.status}`);
    }

    try {
      const result = await connection.socket.sendMessage(to, message);

      // Update last activity
      connection.lastActivity = new Date();

      this.logger.debug('Message sent', {
        userId,
        to,
        messageType: message.type || 'unknown'
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to send message', {
        userId,
        to,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    this.logger.info('Destroying BaileysConnectionManager');

    // Clear keep-alive interval
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
    }

    // Close all connections
    const closePromises = Array.from(this.connections.keys()).map(userId =>
      this.closeConnection(userId)
    );

    await Promise.allSettled(closePromises);

    this.connections.clear();
    this.logger.info('BaileysConnectionManager destroyed');
  }
}
