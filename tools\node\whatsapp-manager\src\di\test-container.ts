import 'reflect-metadata';
import { container } from 'tsyringe';

// Core services
import { ConfigService } from '../shared/config/config.service';
import { LoggerService } from '../shared/logging/logger.service';
import { HealthService } from '../application/services/health.service';
import { <PERSON>rro<PERSON><PERSON>and<PERSON> } from '../shared/errors/error-handler';

// Domain services
import { SessionDomainService } from '../domain/services/SessionDomainService';

// Infrastructure services - Use Mock Repository for tests
import { MockSessionRepository } from '../infrastructure/database/MockSessionRepository';
import { BaileysAuthStateAdapter } from '../infrastructure/whatsapp/BaileysAuthStateAdapter';
import { WhatsAppService } from '../infrastructure/whatsapp/WhatsAppService';
import { MockBaileysConnectionManager } from '../infrastructure/whatsapp/MockBaileysConnectionManager';
import { QRCodeManager } from '../infrastructure/whatsapp/QRCodeManager';
import { MessageProcessor } from '../infrastructure/whatsapp/MessageProcessor';
import { RateLimiter } from '../infrastructure/whatsapp/RateLimiter';
import { BaileysEventEmitter } from '../infrastructure/whatsapp/BaileysEventEmitter';
import { ConnectionPool } from '../infrastructure/whatsapp/ConnectionPool';
import { MockHealthChecker } from '../infrastructure/whatsapp/MockHealthChecker';
import { WebhookManager } from '../infrastructure/whatsapp/WebhookManager';

// Use cases
import { StartSessionUseCase } from '../application/use-cases/StartSessionUseCase';
import { GetSessionStatusUseCase } from '../application/use-cases/GetSessionStatusUseCase';
import { TerminateSessionUseCase } from '../application/use-cases/TerminateSessionUseCase';
import { ListSessionsUseCase } from '../application/use-cases/ListSessionsUseCase';
import { RefreshQRCodeUseCase } from '../application/use-cases/RefreshQRCodeUseCase';
import { ProcessMessageUseCase } from '../application/use-cases/ProcessMessageUseCase';
import { HealthCheckUseCase } from '../application/use-cases/HealthCheckUseCase';

// Application services
import { SessionCleanupService } from '../application/services/SessionCleanupService';
import { MonitoringService } from '../application/services/MonitoringService';

// Controllers
import { SessionController } from '../api/controllers/session.controller';
import { HealthController } from '../api/controllers/health.controller';

/**
 * Test-specific DI Container with mock implementations
 */
export class TestDIContainer {
  private static isInitialized = false;

  static initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Clear any existing registrations
    container.clearInstances();

    // Core services
    container.registerSingleton('IConfigService', ConfigService);
    container.registerSingleton('ILoggerService', LoggerService);
    container.registerSingleton('HealthService', HealthService);
    container.registerSingleton('ErrorHandler', ErrorHandler);

    // Domain services
    container.registerSingleton('SessionDomainService', SessionDomainService);

    // Infrastructure services - Use Mock Repository
    container.registerSingleton('ISessionRepository', MockSessionRepository);
    container.registerSingleton('BaileysAuthStateAdapter', BaileysAuthStateAdapter);
    container.registerSingleton('IWhatsAppService', WhatsAppService);
    container.registerSingleton('BaileysConnectionManager', MockBaileysConnectionManager);
    container.registerSingleton('QRCodeManager', QRCodeManager);
    container.registerSingleton('MessageProcessor', MessageProcessor);
    container.registerSingleton('RateLimiter', RateLimiter);
    container.registerSingleton('BaileysEventEmitter', BaileysEventEmitter);
    container.registerSingleton('ConnectionPool', ConnectionPool);
    container.registerSingleton('HealthChecker', MockHealthChecker);
    container.registerSingleton('WebhookManager', WebhookManager);

    // Use cases
    container.registerSingleton('StartSessionUseCase', StartSessionUseCase);
    container.registerSingleton('GetSessionStatusUseCase', GetSessionStatusUseCase);
    container.registerSingleton('TerminateSessionUseCase', TerminateSessionUseCase);
    container.registerSingleton('ListSessionsUseCase', ListSessionsUseCase);
    container.registerSingleton('RefreshQRCodeUseCase', RefreshQRCodeUseCase);
    container.registerSingleton('ProcessMessageUseCase', ProcessMessageUseCase);
    container.registerSingleton('HealthCheckUseCase', HealthCheckUseCase);

    // Application services
    container.registerSingleton('SessionCleanupService', SessionCleanupService);
    container.registerSingleton('MonitoringService', MonitoringService);

    // Controllers
    container.registerSingleton('SessionController', SessionController);
    container.registerSingleton('HealthController', HealthController);

    this.isInitialized = true;
  }

  static getContainer(): typeof container {
    return container;
  }

  static resolve<T>(token: string): T {
    return container.resolve<T>(token);
  }

  static destroy(): void {
    container.clearInstances();
    this.isInitialized = false;
  }

  static reset(): void {
    this.destroy();
    this.initialize();
  }
}
