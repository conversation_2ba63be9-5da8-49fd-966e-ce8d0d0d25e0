terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0"
      configuration_aliases = [
        aws.route53_region
      ]
    }
  }
}

locals {
  name_prefix = "${var.env}-${var.app_name}"

  # Process lambda services configuration
  lambda_services = var.lambda_services_config != null ? var.lambda_services_config : {}

  # Create simplified path patterns for API Gateway (like ECS)
  api_routes = flatten([
    for service_name, config in local.lambda_services : [
      for path_pattern in try(config.api_gateway.path_patterns, []) : {
        service_name = service_name
        path_pattern = path_pattern
        # Convert path pattern to API Gateway format
        is_wildcard = endswith(path_pattern, "/*")
        base_path   = endswith(path_pattern, "/*") ? trimsuffix(path_pattern, "/*") : path_pattern
        api_path    = endswith(path_pattern, "/*") ? "${trimsuffix(path_pattern, "/*")}/{proxy+}" : path_pattern
      }
    ]
  ])

  # Convert routes list to map for resource creation
  routes_map = {
    for route in local.api_routes :
    "${route.service_name}-${replace(replace(replace(route.path_pattern, "/", "_"), "*", "star"), "+", "plus")}" => route
  }

  # Create sanitized statement IDs for Lambda permissions
  lambda_permissions = {
    for key, route in local.routes_map :
    key => {
      service_name = route.service_name
      statement_id = "AllowAPIGateway-${replace(replace(replace(key, "*", "star"), "+", "plus"), "-", "_")}"
    }
  }
}

# Create unique suffix for API Gateway to avoid conflicts
resource "random_id" "api_suffix" {
  byte_length = 4
}

# Create API Gateway
resource "aws_api_gateway_rest_api" "this" {
  name        = "${local.name_prefix}-lambda-api-${random_id.api_suffix.hex}"
  description = "API Gateway for Lambda functions in ${var.env} environment"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-lambda-api"
    }
  )
}

# Create API Gateway deployment
resource "aws_api_gateway_deployment" "this" {
  depends_on = [
    aws_api_gateway_method.routes,
    aws_api_gateway_integration.routes
  ]

  rest_api_id = aws_api_gateway_rest_api.this.id

  # Force redeployment when configuration changes
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_rest_api.this.body,
      local.routes_map,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Create API Gateway stage
resource "aws_api_gateway_stage" "this" {
  deployment_id = aws_api_gateway_deployment.this.id
  rest_api_id   = aws_api_gateway_rest_api.this.id
  stage_name    = var.api_stage_name

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-${var.api_stage_name}-stage"
    }
  )
}

# Create Lambda functions
resource "aws_lambda_function" "services" {
  for_each = local.lambda_services

  function_name = "${local.name_prefix}-${each.key}"
  role          = aws_iam_role.lambda_execution[each.key].arn

  # Support both container and zip-based deployments
  dynamic "image_config" {
    for_each = try(each.value.image_repository_uri, null) != null ? [1] : []
    content {
      entry_point = try(each.value.entry_point, null)
      command     = try(each.value.command, null)
    }
  }

  # Container-based Lambda
  image_uri    = try(each.value.image_repository_uri, null)
  package_type = try(each.value.image_repository_uri, null) != null ? "Image" : "Zip"

  # Zip-based Lambda (when not using container)
  filename         = try(each.value.image_repository_uri, null) == null ? try(each.value.filename, "placeholder.zip") : null
  source_code_hash = try(each.value.image_repository_uri, null) == null ? try(each.value.source_code_hash, null) : null
  handler          = try(each.value.image_repository_uri, null) == null ? try(each.value.handler, "index.handler") : null
  runtime          = try(each.value.image_repository_uri, null) == null ? try(each.value.runtime, "nodejs18.x") : null

  timeout     = try(each.value.timeout, 30)
  memory_size = try(each.value.memory_size, 512)

  # Auto-publish new versions (configurable, default: true)
  publish = try(each.value.publish, true)

  # Environment variables
  dynamic "environment" {
    for_each = try(each.value.environment_variables, null) != null || try(each.value.secrets, null) != null ? [1] : []
    content {
      variables = merge(
        try(each.value.environment_variables, {}),
        try(each.value.secrets, {})
      )
    }
  }

  # VPC configuration
  dynamic "vpc_config" {
    for_each = try(each.value.vpc_config, null) != null ? [each.value.vpc_config] : []
    content {
      subnet_ids         = vpc_config.value.subnet_ids
      security_group_ids = vpc_config.value.security_group_ids
    }
  }

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}"
    }
  )
}

# Create CloudWatch Log Groups for Lambda functions
resource "aws_cloudwatch_log_group" "lambda_logs" {
  for_each = local.lambda_services

  name              = "/aws/lambda/${local.name_prefix}-${each.key}"
  retention_in_days = try(each.value.log_retention_days, 14)

  tags = merge(
    var.tags,
    try(each.value.tags, {}),
    {
      Name = "${local.name_prefix}-${each.key}-logs"
    }
  )
}

# Create IAM roles for Lambda functions
resource "aws_iam_role" "lambda_execution" {
  for_each = local.lambda_services

  name = "${local.name_prefix}-${each.key}-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-${each.key}-lambda-role"
    }
  )
}

# Attach basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_basic" {
  for_each = local.lambda_services

  role       = aws_iam_role.lambda_execution[each.key].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Attach VPC execution policy if VPC is configured
resource "aws_iam_role_policy_attachment" "lambda_vpc" {
  for_each = {
    for k, v in local.lambda_services : k => v
    if try(v.vpc_config, null) != null
  }

  role       = aws_iam_role.lambda_execution[each.key].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

# Create custom IAM policies for Lambda functions
resource "aws_iam_role_policy" "lambda_custom" {
  for_each = {
    for k, v in local.lambda_services : k => v
    if try(v.iam_policy, null) != null
  }

  name = "${local.name_prefix}-${each.key}-custom-policy"
  role = aws_iam_role.lambda_execution[each.key].id

  policy = each.value.iam_policy
}

# Create base path resources (e.g., /sample for both exact and wildcard paths)
# Use toset to ensure unique base paths only
resource "aws_api_gateway_resource" "base_paths" {
  for_each = toset(distinct([
    for route in local.api_routes : route.base_path
  ]))

  rest_api_id = aws_api_gateway_rest_api.this.id
  parent_id   = aws_api_gateway_rest_api.this.root_resource_id
  path_part   = trimprefix(each.value, "/")
}

# Create proxy resources only for wildcard paths (e.g., {proxy+} under /sample)
resource "aws_api_gateway_resource" "proxy_paths" {
  for_each = {
    for route in local.api_routes : route.api_path => route
    if route.is_wildcard
  }

  rest_api_id = aws_api_gateway_rest_api.this.id
  parent_id   = aws_api_gateway_resource.base_paths[each.value.base_path].id
  path_part   = "{proxy+}"
}

# Create API Gateway methods (always ANY method for simplicity)
resource "aws_api_gateway_method" "routes" {
  for_each = local.routes_map

  rest_api_id   = aws_api_gateway_rest_api.this.id
  resource_id   = each.value.is_wildcard ? aws_api_gateway_resource.proxy_paths[each.value.api_path].id : aws_api_gateway_resource.base_paths[each.value.base_path].id
  http_method   = "ANY"
  authorization = "NONE"
}

# Create API Gateway integrations
resource "aws_api_gateway_integration" "routes" {
  for_each = local.routes_map

  rest_api_id = aws_api_gateway_rest_api.this.id
  resource_id = each.value.is_wildcard ? aws_api_gateway_resource.proxy_paths[each.value.api_path].id : aws_api_gateway_resource.base_paths[each.value.base_path].id
  http_method = aws_api_gateway_method.routes[each.key].http_method

  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.services[each.value.service_name].invoke_arn
}

# Grant API Gateway permission to invoke Lambda functions
resource "aws_lambda_permission" "api_gateway" {
  for_each = local.lambda_permissions

  statement_id  = each.value.statement_id
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.services[each.value.service_name].function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.this.execution_arn}/*/*"
}

# Create custom domain name if domain is provided
resource "aws_api_gateway_domain_name" "this" {
  count = var.domain_name != null ? 1 : 0

  domain_name              = var.domain_name
  regional_certificate_arn = var.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-api-domain"
    }
  )
}

# Create base path mapping for custom domain
resource "aws_api_gateway_base_path_mapping" "this" {
  count = var.domain_name != null ? 1 : 0

  api_id      = aws_api_gateway_rest_api.this.id
  stage_name  = aws_api_gateway_stage.this.stage_name
  domain_name = aws_api_gateway_domain_name.this[0].domain_name
}

# Create Route 53 record for custom domain
resource "aws_route53_record" "api_domain" {
  count = var.domain_name != null && var.route53_zone_id != null ? 1 : 0

  provider = aws.route53_region
  zone_id  = var.route53_zone_id
  name     = var.domain_name
  type     = "A"

  alias {
    name                   = aws_api_gateway_domain_name.this[0].regional_domain_name
    zone_id                = aws_api_gateway_domain_name.this[0].regional_zone_id
    evaluate_target_health = true
  }
}
