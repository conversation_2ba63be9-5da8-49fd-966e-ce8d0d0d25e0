import { AuthToken } from '../value-objects/AuthToken';

/**
 * Repository interface for AuthToken persistence operations
 * Provides abstraction over the underlying storage mechanism
 */
export interface IAuthTokenRepository {
  /**
   * Create a new auth token
   * @param token - The auth token to create
   * @throws AuthTokenError if token already exists
   */
  create(token: AuthToken): Promise<void>;

  /**
   * Find auth token by token ID
   * @param tokenId - The token ID to search for
   * @returns The auth token if found, null otherwise
   */
  findByTokenId(tokenId: string): Promise<AuthToken | null>;

  /**
   * Find auth token by JWT token string
   * @param tokenString - The JWT token string to search for
   * @returns The auth token if found, null otherwise
   */
  findByToken(tokenString: string): Promise<AuthToken | null>;

  /**
   * Find auth token by JTI (JWT ID)
   * @param jti - The JWT ID to search for
   * @returns The auth token if found, null otherwise
   */
  findByJti(jti: string): Promise<AuthToken | null>;

  /**
   * Find all active tokens for a user
   * @param userId - The user ID to search for
   * @param includeExpired - Whether to include expired tokens
   * @returns Array of auth tokens for the user
   */
  findByUserId(userId: string, includeExpired?: boolean): Promise<AuthToken[]>;

  /**
   * Update an existing auth token
   * @param token - The updated auth token
   * @throws AuthTokenNotFoundError if token doesn't exist
   */
  update(token: AuthToken): Promise<void>;

  /**
   * Mark a token as used
   * @param tokenId - The token ID to mark as used
   * @returns The updated auth token
   * @throws AuthTokenNotFoundError if token doesn't exist
   * @throws AuthTokenExpiredError if token is expired
   * @throws AuthTokenUsedError if token is already used
   */
  markAsUsed(tokenId: string): Promise<AuthToken>;

  /**
   * Delete an auth token
   * @param tokenId - The token ID to delete
   * @returns True if token was deleted, false if not found
   */
  delete(tokenId: string): Promise<boolean>;

  /**
   * Delete all tokens for a user
   * @param userId - The user ID whose tokens to delete
   * @returns Number of tokens deleted
   */
  deleteByUserId(userId: string): Promise<number>;

  /**
   * Delete expired tokens (cleanup operation)
   * @param batchSize - Maximum number of tokens to delete in one operation
   * @returns Number of tokens deleted
   */
  deleteExpiredTokens(batchSize?: number): Promise<number>;

  /**
   * Count active tokens for a user
   * @param userId - The user ID to count tokens for
   * @returns Number of active (non-expired, non-used) tokens
   */
  countActiveTokensByUserId(userId: string): Promise<number>;

  /**
   * Count total tokens in the system
   * @param includeExpired - Whether to include expired tokens
   * @returns Total number of tokens
   */
  countTotalTokens(includeExpired?: boolean): Promise<number>;

  /**
   * Find tokens that will expire within the specified time
   * @param withinSeconds - Time window in seconds
   * @returns Array of tokens expiring within the time window
   */
  findExpiringTokens(withinSeconds: number): Promise<AuthToken[]>;

  /**
   * Batch create multiple tokens
   * @param tokens - Array of tokens to create
   * @throws AuthTokenError if any token already exists
   */
  batchCreate(tokens: AuthToken[]): Promise<void>;

  /**
   * Batch delete multiple tokens
   * @param tokenIds - Array of token IDs to delete
   * @returns Number of tokens actually deleted
   */
  batchDelete(tokenIds: string[]): Promise<number>;

  /**
   * Check if a token exists
   * @param tokenId - The token ID to check
   * @returns True if token exists, false otherwise
   */
  exists(tokenId: string): Promise<boolean>;

  /**
   * Get token statistics
   * @returns Statistics about tokens in the system
   */
  getStatistics(): Promise<{
    totalTokens: number;
    activeTokens: number;
    expiredTokens: number;
    usedTokens: number;
    tokensCreatedToday: number;
    tokensUsedToday: number;
  }>;

  /**
   * Validate token and return if valid for use
   * @param tokenId - The token ID to validate
   * @returns The valid token if found and usable
   * @throws AuthTokenNotFoundError if token doesn't exist
   * @throws AuthTokenExpiredError if token is expired
   * @throws AuthTokenUsedError if token is already used
   */
  validateToken(tokenId: string): Promise<AuthToken>;

  /**
   * Clean up old tokens based on retention policy
   * @param retentionDays - Number of days to retain tokens
   * @returns Number of tokens cleaned up
   */
  cleanupOldTokens(retentionDays: number): Promise<number>;
}

/**
 * Query options for finding tokens
 */
export interface FindTokensOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'expiresAt' | 'usedAt';
  sortOrder?: 'asc' | 'desc';
  includeExpired?: boolean;
  includeUsed?: boolean;
}

/**
 * Extended repository interface with advanced query capabilities
 */
export interface IAuthTokenRepositoryExtended extends IAuthTokenRepository {
  /**
   * Find tokens with advanced filtering options
   * @param userId - Optional user ID filter
   * @param options - Query options
   * @returns Array of tokens matching the criteria
   */
  findTokens(userId?: string, options?: FindTokensOptions): Promise<AuthToken[]>;

  /**
   * Find tokens created within a date range
   * @param startDate - Start date (inclusive)
   * @param endDate - End date (inclusive)
   * @param userId - Optional user ID filter
   * @returns Array of tokens created within the date range
   */
  findTokensByDateRange(startDate: Date, endDate: Date, userId?: string): Promise<AuthToken[]>;

  /**
   * Get usage analytics for tokens
   * @param startDate - Start date for analytics
   * @param endDate - End date for analytics
   * @returns Analytics data
   */
  getUsageAnalytics(startDate: Date, endDate: Date): Promise<{
    tokensCreated: number;
    tokensUsed: number;
    tokensExpired: number;
    uniqueUsers: number;
    averageTokenLifetime: number;
    peakUsageHour: number;
  }>;
}
