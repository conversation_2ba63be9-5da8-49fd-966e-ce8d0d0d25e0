import { v4 as uuidv4 } from 'uuid';

/**
 * Message type enumeration
 */
export type MessageType = 
  | 'text' 
  | 'image' 
  | 'audio' 
  | 'video' 
  | 'document' 
  | 'location' 
  | 'contact' 
  | 'sticker'
  | 'reaction'
  | 'system';

/**
 * Message status enumeration
 */
export type MessageStatus = 'pending' | 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Message direction enumeration
 */
export type MessageDirection = 'inbound' | 'outbound';

/**
 * Message filter interface for querying messages
 */
export interface MessageFilter {
  userId?: string;
  sessionId?: string;
  from?: string;
  to?: string;
  type?: MessageType;
  direction?: MessageDirection;
  status?: MessageStatus;
  hasMedia?: boolean;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

/**
 * Media information interface
 */
export interface MediaInfo {
  mimeType: string;
  fileName?: string;
  fileSize?: number;
  url?: string;
  caption?: string;
  thumbnail?: string;
}

/**
 * Location information interface
 */
export interface LocationInfo {
  latitude: number;
  longitude: number;
  name?: string;
  address?: string;
}

/**
 * Contact information interface
 */
export interface ContactInfo {
  displayName: string;
  phoneNumber?: string;
  email?: string;
  organization?: string;
}

/**
 * WhatsApp Message domain entity
 * Represents a message in the WhatsApp conversation
 */
export class WhatsAppMessage {
  constructor(
    public readonly id: string,
    public readonly sessionId: string,
    public readonly userId: string,
    public readonly from: string,
    public readonly to: string,
    public readonly type: MessageType,
    public readonly direction: MessageDirection,
    public readonly content: string,
    public readonly status: MessageStatus,
    public readonly timestamp: Date,
    public readonly messageId?: string, // WhatsApp message ID
    public readonly quotedMessageId?: string,
    public readonly mediaInfo?: MediaInfo,
    public readonly locationInfo?: LocationInfo,
    public readonly contactInfo?: ContactInfo,
    public readonly metadata?: Record<string, any>,
    public readonly deliveredAt?: Date,
    public readonly readAt?: Date
  ) {
    this.validateMessage();
  }

  /**
   * Factory method to create a new inbound message
   */
  static createInbound(
    sessionId: string,
    userId: string,
    from: string,
    to: string,
    type: MessageType,
    content: string,
    messageId?: string,
    quotedMessageId?: string,
    mediaInfo?: MediaInfo,
    locationInfo?: LocationInfo,
    contactInfo?: ContactInfo,
    metadata?: Record<string, any>
  ): WhatsAppMessage {
    return new WhatsAppMessage(
      uuidv4(),
      sessionId,
      userId,
      from,
      to,
      type,
      'inbound',
      content,
      'delivered', // Inbound messages are considered delivered when received
      new Date(),
      messageId,
      quotedMessageId,
      mediaInfo,
      locationInfo,
      contactInfo,
      metadata
    );
  }

  /**
   * Factory method to create a new outbound message
   */
  static createOutbound(
    sessionId: string,
    userId: string,
    from: string,
    to: string,
    type: MessageType,
    content: string,
    quotedMessageId?: string,
    mediaInfo?: MediaInfo,
    locationInfo?: LocationInfo,
    contactInfo?: ContactInfo,
    metadata?: Record<string, any>
  ): WhatsAppMessage {
    return new WhatsAppMessage(
      uuidv4(),
      sessionId,
      userId,
      from,
      to,
      type,
      'outbound',
      content,
      'pending',
      new Date(),
      undefined, // Will be set when message is sent
      quotedMessageId,
      mediaInfo,
      locationInfo,
      contactInfo,
      metadata
    );
  }

  /**
   * Mark message as sent
   */
  markAsSent(messageId: string): WhatsAppMessage {
    if (this.direction !== 'outbound') {
      throw new Error('Only outbound messages can be marked as sent');
    }

    return this.clone({
      status: 'sent',
      messageId
    });
  }

  /**
   * Mark message as delivered
   */
  markAsDelivered(): WhatsAppMessage {
    if (this.direction !== 'outbound') {
      throw new Error('Only outbound messages can be marked as delivered');
    }

    return this.clone({
      status: 'delivered',
      deliveredAt: new Date()
    });
  }

  /**
   * Mark message as read
   */
  markAsRead(): WhatsAppMessage {
    if (this.direction !== 'outbound') {
      throw new Error('Only outbound messages can be marked as read');
    }

    return this.clone({
      status: 'read',
      readAt: new Date()
    });
  }

  /**
   * Mark message as failed
   */
  markAsFailed(): WhatsAppMessage {
    if (this.direction !== 'outbound') {
      throw new Error('Only outbound messages can be marked as failed');
    }

    return this.clone({
      status: 'failed'
    });
  }

  /**
   * Check if message has media
   */
  hasMedia(): boolean {
    return this.mediaInfo !== undefined;
  }

  /**
   * Check if message is a reply
   */
  isReply(): boolean {
    return this.quotedMessageId !== undefined;
  }

  /**
   * Get message age in milliseconds
   */
  getAge(): number {
    return Date.now() - this.timestamp.getTime();
  }

  /**
   * Validate message data
   */
  private validateMessage(): void {
    if (!this.sessionId || this.sessionId.trim().length === 0) {
      throw new Error('Session ID is required');
    }

    if (!this.userId || this.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!this.from || this.from.trim().length === 0) {
      throw new Error('From field is required');
    }

    if (!this.to || this.to.trim().length === 0) {
      throw new Error('To field is required');
    }

    if (!this.content && !this.hasMedia() && this.type !== 'system') {
      throw new Error('Content is required for non-media and non-system messages');
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\d{10,15}@(s\.whatsapp\.net|c\.us)$/;
    if (this.direction === 'inbound' && !phoneRegex.test(this.from)) {
      // Allow some flexibility for group messages and other formats
      if (!this.from.includes('@')) {
        throw new Error('Invalid from phone number format');
      }
    }
  }

  /**
   * Serialize to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      sessionId: this.sessionId,
      userId: this.userId,
      from: this.from,
      to: this.to,
      type: this.type,
      direction: this.direction,
      content: this.content,
      status: this.status,
      timestamp: this.timestamp.toISOString(),
      messageId: this.messageId,
      quotedMessageId: this.quotedMessageId,
      mediaInfo: this.mediaInfo,
      locationInfo: this.locationInfo,
      contactInfo: this.contactInfo,
      metadata: this.metadata,
      deliveredAt: this.deliveredAt?.toISOString(),
      readAt: this.readAt?.toISOString()
    };
  }

  /**
   * Deserialize from JSON
   */
  static fromJSON(data: Record<string, any>): WhatsAppMessage {
    return new WhatsAppMessage(
      data['id'],
      data['sessionId'],
      data['userId'],
      data['from'],
      data['to'],
      data['type'],
      data['direction'],
      data['content'],
      data['status'],
      new Date(data['timestamp']),
      data['messageId'],
      data['quotedMessageId'],
      data['mediaInfo'],
      data['locationInfo'],
      data['contactInfo'],
      data['metadata'],
      data['deliveredAt'] ? new Date(data['deliveredAt']) : undefined,
      data['readAt'] ? new Date(data['readAt']) : undefined
    );
  }

  /**
   * Clone with updates
   */
  clone(updates: Partial<Omit<WhatsAppMessage, 'id' | 'sessionId' | 'userId' | 'timestamp'>>): WhatsAppMessage {
    return new WhatsAppMessage(
      this.id,
      this.sessionId,
      this.userId,
      updates.from !== undefined ? updates.from : this.from,
      updates.to !== undefined ? updates.to : this.to,
      updates.type !== undefined ? updates.type : this.type,
      updates.direction !== undefined ? updates.direction : this.direction,
      updates.content !== undefined ? updates.content : this.content,
      updates.status !== undefined ? updates.status : this.status,
      this.timestamp,
      updates.messageId !== undefined ? updates.messageId : this.messageId,
      updates.quotedMessageId !== undefined ? updates.quotedMessageId : this.quotedMessageId,
      updates.mediaInfo !== undefined ? updates.mediaInfo : this.mediaInfo,
      updates.locationInfo !== undefined ? updates.locationInfo : this.locationInfo,
      updates.contactInfo !== undefined ? updates.contactInfo : this.contactInfo,
      updates.metadata !== undefined ? updates.metadata : this.metadata,
      updates.deliveredAt !== undefined ? updates.deliveredAt : this.deliveredAt,
      updates.readAt !== undefined ? updates.readAt : this.readAt
    );
  }
}
