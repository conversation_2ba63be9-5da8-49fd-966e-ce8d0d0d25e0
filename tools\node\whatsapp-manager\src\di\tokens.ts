/**
 * Dependency injection tokens for type-safe service resolution
 */

export const DI_TOKENS = {
  // Core services
  IConfigService: Symbol.for('IConfigService'),
  ILoggerService: Symbol.for('ILoggerService'),

  // Application services
  IHealthService: Symbol.for('IHealthService'),

  // Infrastructure services
  IDatabaseService: Symbol.for('IDatabaseService'),
  DynamoDBClient: Symbol.for('DynamoDBClient'),

  // Repositories
  ISessionRepository: Symbol.for('ISessionRepository'),
  IAuthTokenRepository: Symbol.for('IAuthTokenRepository'),

  // Services
  WhatsAppService: Symbol.for('WhatsAppService'),
  AuthService: Symbol.for('AuthService'),

  // Use Cases
  CreateSessionUseCase: Symbol.for('CreateSessionUseCase'),
  GetSessionUseCase: Symbol.for('GetSessionUseCase'),
  DeleteSessionUseCase: Symbol.for('DeleteSessionUseCase'),
  RefreshQrUseCase: Symbol.for('RefreshQrUseCase'),
  GetSessionStatusUseCase: Symbol.for('GetSessionStatusUseCase'),
  GenerateQrLinkUseCase: Symbol.for('GenerateQrLinkUseCase'),

  // Controllers
  HealthController: Symbol.for('HealthController'),
  SessionController: Symbol.for('SessionController'),
  AuthController: Symbol.for('AuthController'),

  // Middleware
  ErrorHandler: Symbol.for('ErrorHandler'),
} as const;

export type DITokens = typeof DI_TOKENS;
