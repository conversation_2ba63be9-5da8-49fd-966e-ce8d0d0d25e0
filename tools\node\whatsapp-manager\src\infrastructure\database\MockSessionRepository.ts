import { injectable } from 'tsyringe';
import { SessionEntity, SessionStatus, AuthenticationState } from '../../domain/entities/SessionEntity';
import { ISessionRepository } from '../../domain/repositories/ISessionRepository';

/**
 * Mock implementation of SessionRepository for testing
 * Stores sessions in memory instead of DynamoDB
 */
@injectable()
export class MockSessionRepository implements ISessionRepository {
  private sessions = new Map<string, SessionEntity>();
  private sessionsByUserId = new Map<string, SessionEntity>();

  async createSession(session: SessionEntity): Promise<void> {
    this.sessions.set(session.sessionId, session);
    this.sessionsByUserId.set(session.userId, session);
  }

  async saveSession(session: SessionEntity): Promise<void> {
    this.sessions.set(session.sessionId, session);
    this.sessionsByUserId.set(session.userId, session);
  }

  async updateSessionStatus(userId: string, status: SessionStatus, metadata?: Partial<SessionEntity>): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      session.status = status;
      if (metadata) {
        Object.assign(session, metadata);
      }
      session.updatedAt = new Date();
    }
  }

  async updateAuthState(userId: string, authState: AuthenticationState): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      session.authState = authState;
      session.updatedAt = new Date();
    }
  }

  async updateConnectionInfo(userId: string, phoneNumber: string, connectedAt: Date): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      session.phoneNumber = phoneNumber;
      session.connectedAt = connectedAt;
      session.status = 'connected';
      session.updatedAt = new Date();
    }
  }

  async updateSessionActivity(userId: string, lastActivity: Date): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      // Update the session's updatedAt to reflect the last activity
      session.updatedAt = lastActivity;
    }
  }

  async deleteSession(userId: string): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      this.sessions.delete(session.sessionId);
      this.sessionsByUserId.delete(userId);
    }
  }

  async findBySessionId(sessionId: string): Promise<SessionEntity | null> {
    return this.sessions.get(sessionId) || null;
  }

  async findByUserId(userId: string): Promise<SessionEntity | null> {
    return this.sessionsByUserId.get(userId) || null;
  }

  async upsertSession(session: SessionEntity): Promise<void> {
    this.sessions.set(session.sessionId, session);
    this.sessionsByUserId.set(session.userId, session);
  }

  async delete(userId: string): Promise<void> {
    const session = this.sessionsByUserId.get(userId);
    if (session) {
      this.sessions.delete(session.sessionId);
      this.sessionsByUserId.delete(userId);
    }
  }

  async deleteBySessionId(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.sessions.delete(sessionId);
      this.sessionsByUserId.delete(session.userId);
    }
  }

  async createIfNotExists(session: SessionEntity): Promise<{ created: boolean; existing?: SessionEntity }> {
    const existing = this.sessionsByUserId.get(session.userId);
    if (existing) {
      return { created: false, existing };
    }
    await this.createSession(session);
    return { created: true };
  }

  async updateIfExists(userId: string, updates: Partial<SessionEntity>): Promise<boolean> {
    const session = this.sessionsByUserId.get(userId);
    if (!session) {
      return false;
    }
    Object.assign(session, updates);
    session.updatedAt = new Date();
    return true;
  }

  async findAllActive(): Promise<SessionEntity[]> {
    return Array.from(this.sessions.values()).filter(session =>
      session.status === 'connected' || session.status === 'qr_pending'
    );
  }

  async findByStatus(status: SessionStatus): Promise<SessionEntity[]> {
    return Array.from(this.sessions.values()).filter(session =>
      session.status === status
    );
  }

  async findExpiredSessions(): Promise<SessionEntity[]> {
    const now = Date.now();
    return Array.from(this.sessions.values()).filter(session =>
      session.ttl && session.ttl * 1000 < now
    );
  }

  async createMany(sessions: SessionEntity[]): Promise<void> {
    for (const session of sessions) {
      await this.createSession(session);
    }
  }

  async deleteMany(userIds: string[]): Promise<void> {
    for (const userId of userIds) {
      await this.delete(userId);
    }
  }

  async count(): Promise<number> {
    return this.sessions.size;
  }

  async countByStatus(status: SessionStatus): Promise<number> {
    return Array.from(this.sessions.values()).filter(session =>
      session.status === status
    ).length;
  }

  // Legacy methods for backward compatibility
  async findAllSessions(): Promise<SessionEntity[]> {
    return Array.from(this.sessions.values());
  }

  async findActiveSessions(): Promise<SessionEntity[]> {
    return this.findAllActive();
  }

  async findSessionsByStatus(status: string): Promise<SessionEntity[]> {
    return this.findByStatus(status as SessionStatus);
  }

  async countSessions(): Promise<number> {
    return this.count();
  }

  async countActiveConnections(): Promise<number> {
    return this.countByStatus('connected');
  }

  async cleanup(): Promise<number> {
    const expiredSessions = await this.findExpiredSessions();
    for (const session of expiredSessions) {
      await this.deleteBySessionId(session.sessionId);
    }
    return expiredSessions.length;
  }

  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details?: any }> {
    return {
      status: 'healthy',
      details: {
        totalSessions: this.sessions.size,
        activeSessions: await this.countActiveConnections()
      }
    };
  }

  // Test helper methods
  clear(): void {
    this.sessions.clear();
    this.sessionsByUserId.clear();
  }

  getAll(): SessionEntity[] {
    return Array.from(this.sessions.values());
  }
}
