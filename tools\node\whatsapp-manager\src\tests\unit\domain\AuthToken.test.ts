import { AuthToken } from '../../../domain/value-objects/AuthToken';

describe('AuthToken', () => {
  const validUserId = 'user123';
  const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test';
  const validExpirySeconds = 300;

  describe('create', () => {
    it('should create a valid auth token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);

      expect(token.userId).toBe(validUserId);
      expect(token.token).toBe(validToken);
      expect(token.isUsed).toBe(false);
      expect(token.tokenId).toBeDefined();
      expect(token.createdAt).toBeInstanceOf(Date);
      expect(token.expiresAt).toBeInstanceOf(Date);
      expect(token.expiresAt.getTime()).toBeGreaterThan(token.createdAt.getTime());
    });

    it('should throw error for empty user ID', () => {
      expect(() => {
        AuthToken.create('', validToken, validExpirySeconds);
      }).toThrow('User ID is required');
    });

    it('should throw error for empty token', () => {
      expect(() => {
        AuthToken.create(validUserId, '', validExpirySeconds);
      }).toThrow('Token is required');
    });

    it('should throw error for invalid expiry seconds', () => {
      expect(() => {
        AuthToken.create(validUserId, validToken, 0);
      }).toThrow('Expiry seconds must be between 1 and 86400');

      expect(() => {
        AuthToken.create(validUserId, validToken, 86401);
      }).toThrow('Expiry seconds must be between 1 and 86400');
    });

    it('should create token with metadata', () => {
      const metadata = { purpose: 'qr_auth', clientId: 'test-client' };
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds, metadata);

      expect(token.metadata).toEqual(metadata);
    });
  });

  describe('fromStorage', () => {
    it('should create token from storage data', () => {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 300000);
      
      const storageData = {
        tokenId: 'token123',
        userId: validUserId,
        token: validToken,
        expiresAt: expiresAt.toISOString(),
        createdAt: now.toISOString(),
        isUsed: false
      };

      const token = AuthToken.fromStorage(storageData);

      expect(token.tokenId).toBe('token123');
      expect(token.userId).toBe(validUserId);
      expect(token.token).toBe(validToken);
      expect(token.isUsed).toBe(false);
    });

    it('should handle Date objects in storage data', () => {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 300000);
      
      const storageData = {
        tokenId: 'token123',
        userId: validUserId,
        token: validToken,
        expiresAt: expiresAt,
        createdAt: now,
        isUsed: false
      };

      const token = AuthToken.fromStorage(storageData);

      expect(token.expiresAt).toEqual(expiresAt);
      expect(token.createdAt).toEqual(now);
    });
  });

  describe('isExpired', () => {
    it('should return false for non-expired token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      expect(token.isExpired()).toBe(false);
    });

    it('should return true for expired token', () => {
      // Create token that expires in the past
      const pastDate = new Date(Date.now() - 1000);
      const token = new AuthToken(
        'token123',
        validUserId,
        validToken,
        pastDate,
        new Date(Date.now() - 2000)
      );
      expect(token.isExpired()).toBe(true);
    });
  });

  describe('isValid', () => {
    it('should return true for valid unused non-expired token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      expect(token.isValid()).toBe(true);
    });

    it('should return false for expired token', () => {
      const pastDate = new Date(Date.now() - 1000);
      const token = new AuthToken(
        'token123',
        validUserId,
        validToken,
        pastDate,
        new Date(Date.now() - 2000)
      );
      expect(token.isValid()).toBe(false);
    });

    it('should return false for used token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const usedToken = token.markAsUsed();
      expect(usedToken.isValid()).toBe(false);
    });
  });

  describe('markAsUsed', () => {
    it('should mark token as used', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const usedToken = token.markAsUsed();

      expect(usedToken.isUsed).toBe(true);
      expect(usedToken.usedAt).toBeInstanceOf(Date);
      expect(usedToken.tokenId).toBe(token.tokenId);
    });

    it('should throw error when marking already used token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const usedToken = token.markAsUsed();

      expect(() => {
        usedToken.markAsUsed();
      }).toThrow('Token is already used');
    });

    it('should throw error when marking expired token', () => {
      const pastDate = new Date(Date.now() - 1000);
      const token = new AuthToken(
        'token123',
        validUserId,
        validToken,
        pastDate,
        new Date(Date.now() - 2000)
      );

      expect(() => {
        token.markAsUsed();
      }).toThrow('Cannot use expired token');
    });
  });

  describe('getRemainingTimeSeconds', () => {
    it('should return remaining time for non-expired token', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const remaining = token.getRemainingTimeSeconds();

      expect(remaining).toBeGreaterThan(0);
      expect(remaining).toBeLessThanOrEqual(validExpirySeconds);
    });

    it('should return 0 for expired token', () => {
      const pastDate = new Date(Date.now() - 1000);
      const token = new AuthToken(
        'token123',
        validUserId,
        validToken,
        pastDate,
        new Date(Date.now() - 2000)
      );

      expect(token.getRemainingTimeSeconds()).toBe(0);
    });
  });

  describe('getTTL', () => {
    it('should return TTL as Unix timestamp', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const ttl = token.getTTL();

      expect(ttl).toBeGreaterThan(Math.floor(Date.now() / 1000));
      expect(typeof ttl).toBe('number');
    });
  });

  describe('toJSON', () => {
    it('should convert token to JSON format', () => {
      const metadata = { purpose: 'qr_auth' };
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds, metadata);
      const json = token.toJSON();

      expect(json['tokenId']).toBe(token.tokenId);
      expect(json['userId']).toBe(validUserId);
      expect(json['token']).toBe(validToken);
      expect(json['isUsed']).toBe(false);
      expect(json['metadata']).toEqual(metadata);
      expect(json['ttl']).toBe(token.getTTL());
    });
  });

  describe('toDynamoDBItem', () => {
    it('should convert token to DynamoDB item format', () => {
      const token = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const item = token.toDynamoDBItem();

      expect(item['PK']).toBe(`AUTH_TOKEN#${token.tokenId}`);
      expect(item['SK']).toBe(`USER#${validUserId}`);
      expect(item['GSI1PK']).toBe(`USER#${validUserId}`);
      expect(item['GSI1SK']).toBe(`TOKEN#${token.createdAt.toISOString()}`);
      expect(item['tokenId']).toBe(token.tokenId);
      expect(item['userId']).toBe(validUserId);
      expect(item['ttl']).toBe(token.getTTL());
    });
  });

  describe('fromDynamoDBItem', () => {
    it('should create token from DynamoDB item', () => {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 300000);
      
      const item = {
        tokenId: 'token123',
        userId: validUserId,
        token: validToken,
        expiresAt: expiresAt.toISOString(),
        createdAt: now.toISOString(),
        isUsed: false,
        metadata: JSON.stringify({ purpose: 'qr_auth' })
      };

      const token = AuthToken.fromDynamoDBItem(item);

      expect(token.tokenId).toBe('token123');
      expect(token.userId).toBe(validUserId);
      expect(token.metadata).toEqual({ purpose: 'qr_auth' });
    });
  });

  describe('equals', () => {
    it('should return true for equal tokens', () => {
      const token1 = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const token2 = new AuthToken(
        token1.tokenId,
        token1.userId,
        token1.token,
        token1.expiresAt,
        token1.createdAt,
        token1.isUsed,
        token1.usedAt,
        token1.metadata
      );

      expect(token1.equals(token2)).toBe(true);
    });

    it('should return false for different tokens', () => {
      const token1 = AuthToken.create(validUserId, validToken, validExpirySeconds);
      const token2 = AuthToken.create(validUserId, 'different-token', validExpirySeconds);

      expect(token1.equals(token2)).toBe(false);
    });
  });

  describe('validation', () => {
    it('should throw error for invalid token ID', () => {
      expect(() => {
        new AuthToken('', validUserId, validToken, new Date(), new Date());
      }).toThrow('Token ID is required');
    });

    it('should throw error for invalid dates', () => {
      const now = new Date();
      const past = new Date(now.getTime() - 1000);

      expect(() => {
        new AuthToken('token123', validUserId, validToken, past, now);
      }).toThrow('Expiry date must be after creation date');
    });
  });
});
