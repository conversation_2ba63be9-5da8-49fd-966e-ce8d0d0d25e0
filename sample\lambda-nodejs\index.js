/**
 * Sample Lambda function handler
 * Provides a simple HTTP-like interface with routing
 */

const handler = async (event, context) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  try {
    // Extract path from event (API Gateway sends the full path)
    const path = event.path || event.rawPath || '/sample';
    const httpMethod = event.httpMethod || event.requestContext?.http?.method || 'GET';

    console.log(`Processing ${httpMethod} request for path: ${path}`);
    console.log('Event pathParameters:', JSON.stringify(event.pathParameters, null, 2));
    console.log('Full event:', JSON.stringify(event, null, 2));

    // Simple routing - handle both /sample and /sample/ (proxy pattern)
    // For API Gateway proxy: /sample/ -> proxy = "", /sample -> path = "/sample"
    const isRootSampleRequest = (path === '/sample' || path === '/sample/') && httpMethod === 'GET';

    if (isRootSampleRequest) {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
        },
        body: JSON.stringify({
          message: 'Hello World!',
          timestamp: new Date().toISOString(),
          path: path,
          method: httpMethod,
          requestId: context.awsRequestId,
          version: '1.0.0'
        })
      };
    }

    // Health check endpoint
    if (path === '/sample/health' && httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        })
      };
    }

    // Default 404 response
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Not Found',
        message: `Path ${path} not found`,
        availableRoutes: ['/sample', '/sample/', '/sample/health'],
        note: 'Available at https://api.uat.ezychat.ai/sample and https://api.uat.ezychat.ai/sample/health'
      })
    };

  } catch (error) {
    console.error('Error processing request:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        requestId: context.awsRequestId
      })
    };
  }
};

module.exports = { handler };
