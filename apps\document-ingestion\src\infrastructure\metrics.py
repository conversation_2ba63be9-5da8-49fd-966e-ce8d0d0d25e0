"""
Metrics Collection Infrastructure for Document Ingestion Pipeline

This module provides metrics collection implementation for monitoring and observability.
"""

import time
from typing import Any, Dict

from ..domain.interfaces import <PERSON>ogger, IMetricsCollector


class SimpleMetricsCollector(IMetricsCollector):
    """Simple in-memory metrics collector for Lambda environment"""

    def __init__(self, logger: ILogger):
        self._logger = logger
        self._metrics = {"counters": {}, "timers": {}, "errors": {}, "successes": {}}

    def record_processing_time(self, operation: str, duration_ms: float, **tags) -> None:
        """Record processing time metric"""
        metric_key = f"{operation}_{self._tags_to_key(tags)}"

        if metric_key not in self._metrics["timers"]:
            self._metrics["timers"][metric_key] = []

        self._metrics["timers"][metric_key].append(duration_ms)

        self._logger.debug(
            f"Recorded processing time for {operation}",
            operation=operation,
            duration_ms=duration_ms,
            **tags,
        )

    def record_error(self, error_type: str, **tags) -> None:
        """Record error metric"""
        metric_key = f"{error_type}_{self._tags_to_key(tags)}"

        if metric_key not in self._metrics["errors"]:
            self._metrics["errors"][metric_key] = 0

        self._metrics["errors"][metric_key] += 1

        self._logger.debug(f"Recorded error: {error_type}", error_type=error_type, **tags)

    def record_success(self, operation: str, **tags) -> None:
        """Record success metric"""
        metric_key = f"{operation}_{self._tags_to_key(tags)}"

        if metric_key not in self._metrics["successes"]:
            self._metrics["successes"][metric_key] = 0

        self._metrics["successes"][metric_key] += 1

        self._logger.debug(f"Recorded success for {operation}", operation=operation, **tags)

    def increment_counter(self, metric_name: str, **tags) -> None:
        """Increment counter metric"""
        metric_key = f"{metric_name}_{self._tags_to_key(tags)}"

        if metric_key not in self._metrics["counters"]:
            self._metrics["counters"][metric_key] = 0

        self._metrics["counters"][metric_key] += 1

        self._logger.debug(f"Incremented counter: {metric_name}", metric_name=metric_name, **tags)

    def get_metrics_summary(self) -> dict[str, Any]:
        """Get summary of all collected metrics"""
        summary = {
            "total_operations": sum(self._metrics["successes"].values())
            + sum(self._metrics["errors"].values()),
            "total_errors": sum(self._metrics["errors"].values()),
            "total_successes": sum(self._metrics["successes"].values()),
            "error_rate": 0.0,
            "avg_processing_times": {},
            "error_breakdown": self._metrics["errors"].copy(),
            "success_breakdown": self._metrics["successes"].copy(),
            "counter_breakdown": self._metrics["counters"].copy(),
        }

        # Calculate error rate
        if summary["total_operations"] > 0:
            summary["error_rate"] = summary["total_errors"] / summary["total_operations"]

        # Calculate average processing times
        for key, times in self._metrics["timers"].items():
            if times:
                summary["avg_processing_times"][key] = sum(times) / len(times)

        return summary

    def reset_metrics(self) -> None:
        """Reset all metrics"""
        self._metrics = {"counters": {}, "timers": {}, "errors": {}, "successes": {}}

        self._logger.debug("Reset all metrics")

    def _tags_to_key(self, tags: dict[str, Any]) -> str:
        """Convert tags dictionary to a string key"""
        if not tags:
            return "default"

        # Sort tags for consistent key generation
        sorted_tags = sorted(tags.items())
        return "_".join(f"{k}:{v}" for k, v in sorted_tags)


class CloudWatchMetricsCollector(IMetricsCollector):
    """CloudWatch metrics collector for production use"""

    def __init__(self, logger: ILogger, namespace: str = "DocumentIngestion"):
        self._logger = logger
        self._namespace = namespace
        self._metrics_buffer = []

        try:
            import boto3

            self._cloudwatch = boto3.client("cloudwatch")
        except ImportError:
            self._logger.warning("boto3 not available, falling back to logging metrics")
            self._cloudwatch = None

    def record_processing_time(self, operation: str, duration_ms: float, **tags) -> None:
        """Record processing time metric"""
        self._put_metric(
            metric_name=f"{operation}_duration",
            value=duration_ms,
            unit="Milliseconds",
            dimensions=tags,
        )

    def record_error(self, error_type: str, **tags) -> None:
        """Record error metric"""
        self._put_metric(
            metric_name="errors",
            value=1,
            unit="Count",
            dimensions={"error_type": error_type, **tags},
        )

    def record_success(self, operation: str, **tags) -> None:
        """Record success metric"""
        self._put_metric(
            metric_name="successes",
            value=1,
            unit="Count",
            dimensions={"operation": operation, **tags},
        )

    def increment_counter(self, metric_name: str, **tags) -> None:
        """Increment counter metric"""
        self._put_metric(metric_name=metric_name, value=1, unit="Count", dimensions=tags)

    def _put_metric(self, metric_name: str, value: float, unit: str, dimensions: dict[str, Any]):
        """Put metric to CloudWatch"""
        if not self._cloudwatch:
            # Fallback to logging
            self._logger.info(
                f"Metric: {metric_name}",
                metric_name=metric_name,
                value=value,
                unit=unit,
                dimensions=dimensions,
            )
            return

        try:
            # Convert dimensions to CloudWatch format
            cw_dimensions = [{"Name": str(k), "Value": str(v)} for k, v in dimensions.items()]

            self._cloudwatch.put_metric_data(
                Namespace=self._namespace,
                MetricData=[
                    {
                        "MetricName": metric_name,
                        "Value": value,
                        "Unit": unit,
                        "Dimensions": cw_dimensions,
                        "Timestamp": time.time(),
                    }
                ],
            )

        except Exception as e:
            self._logger.error(f"Failed to put metric to CloudWatch: {e}", error=e)


def create_metrics_collector(logger: ILogger, use_cloudwatch: bool = False) -> IMetricsCollector:
    """Factory function to create appropriate metrics collector"""
    if use_cloudwatch:
        return CloudWatchMetricsCollector(logger)
    else:
        return SimpleMetricsCollector(logger)
