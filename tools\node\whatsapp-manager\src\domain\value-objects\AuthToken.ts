import { v4 as uuidv4 } from 'uuid';

/**
 * AuthToken value object for JWT-based authentication tokens
 * Handles token lifecycle, validation, and business logic
 */
export class AuthToken {
  constructor(
    public readonly tokenId: string,
    public readonly userId: string,
    public readonly token: string,
    public readonly expiresAt: Date,
    public readonly createdAt: Date,
    public readonly isUsed: boolean = false,
    public readonly usedAt?: Date,
    public readonly metadata?: Record<string, any>
  ) {
    this.validateToken();
  }

  /**
   * Factory method to create a new auth token
   */
  static create(
    userId: string,
    token: string,
    expirySeconds: number = 300,
    metadata?: Record<string, any>
  ): AuthToken {
    // Validate inputs using centralized validation
    AuthToken.validateInputs(userId, token, expirySeconds);

    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expirySeconds * 1000));

    return new AuthToken(
      uuidv4(),
      userId.trim(),
      token,
      expiresAt,
      now,
      false,
      undefined,
      metadata
    );
  }

  /**
   * Centralized validation for token inputs
   */
  private static validateInputs(userId: string, token: string, expirySeconds?: number): void {
    if (!userId || userId.trim().length === 0) {
      throw new AuthTokenError('User ID is required');
    }

    if (!token || token.trim().length === 0) {
      throw new AuthTokenError('Token is required');
    }

    if (expirySeconds !== undefined && (expirySeconds <= 0 || expirySeconds > 86400)) { // Max 24 hours
      throw new AuthTokenError('Expiry seconds must be between 1 and 86400 (24 hours)');
    }
  }

  /**
   * Factory method to create from stored data
   */
  static fromStorage(data: {
    tokenId: string;
    userId: string;
    token: string;
    expiresAt: string | Date;
    createdAt: string | Date;
    isUsed?: boolean;
    usedAt?: string | Date;
    metadata?: Record<string, any>;
  }): AuthToken {
    return new AuthToken(
      data.tokenId,
      data.userId,
      data.token,
      typeof data.expiresAt === 'string' ? new Date(data.expiresAt) : data.expiresAt,
      typeof data.createdAt === 'string' ? new Date(data.createdAt) : data.createdAt,
      data.isUsed || false,
      data.usedAt ? (typeof data.usedAt === 'string' ? new Date(data.usedAt) : data.usedAt) : undefined,
      data.metadata
    );
  }

  /**
   * Validate token properties
   */
  private validateToken(): void {
    if (!this.tokenId || this.tokenId.trim().length === 0) {
      throw new AuthTokenError('Token ID is required');
    }

    if (!this.userId || this.userId.trim().length === 0) {
      throw new AuthTokenError('User ID is required');
    }

    if (!this.token || this.token.trim().length === 0) {
      throw new AuthTokenError('Token is required');
    }

    if (!this.expiresAt || !(this.expiresAt instanceof Date)) {
      throw new AuthTokenError('Valid expiry date is required');
    }

    if (!this.createdAt || !(this.createdAt instanceof Date)) {
      throw new AuthTokenError('Valid creation date is required');
    }

    if (this.expiresAt <= this.createdAt) {
      throw new AuthTokenError('Expiry date must be after creation date');
    }
  }

  /**
   * Check if token is expired
   */
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Check if token is valid for use
   */
  isValid(): boolean {
    return !this.isExpired() && !this.isUsed;
  }

  /**
   * Mark token as used
   */
  markAsUsed(): AuthToken {
    if (this.isUsed) {
      throw new AuthTokenUsedError('Token is already used');
    }

    if (this.isExpired()) {
      throw new AuthTokenExpiredError('Cannot use expired token');
    }

    return new AuthToken(
      this.tokenId,
      this.userId,
      this.token,
      this.expiresAt,
      this.createdAt,
      true,
      new Date(),
      this.metadata
    );
  }

  /**
   * Get remaining time in seconds
   */
  getRemainingTimeSeconds(): number {
    if (this.isExpired()) {
      return 0;
    }

    const now = new Date();
    return Math.floor((this.expiresAt.getTime() - now.getTime()) / 1000);
  }

  /**
   * Get TTL for DynamoDB (Unix timestamp)
   */
  getTTL(): number {
    return Math.floor(this.expiresAt.getTime() / 1000);
  }

  /**
   * Convert to JSON for storage
   */
  toJSON(): Record<string, any> {
    return {
      tokenId: this.tokenId,
      userId: this.userId,
      token: this.token,
      expiresAt: this.expiresAt.toISOString(),
      createdAt: this.createdAt.toISOString(),
      isUsed: this.isUsed,
      usedAt: this.usedAt?.toISOString(),
      metadata: this.metadata,
      ttl: this.getTTL()
    };
  }

  /**
   * Convert to DynamoDB item format
   */
  toDynamoDBItem(): Record<string, any> {
    return {
      PK: `AUTH_TOKEN#${this.tokenId}`,
      SK: `USER#${this.userId}`,
      GSI1PK: `USER#${this.userId}`,
      GSI1SK: `TOKEN#${this.createdAt.toISOString()}`,
      tokenId: this.tokenId,
      userId: this.userId,
      token: this.token,
      expiresAt: this.expiresAt.toISOString(),
      createdAt: this.createdAt.toISOString(),
      isUsed: this.isUsed,
      usedAt: this.usedAt?.toISOString(),
      metadata: this.metadata ? JSON.stringify(this.metadata) : undefined,
      ttl: this.getTTL()
    };
  }

  /**
   * Create from DynamoDB item
   */
  static fromDynamoDBItem(item: Record<string, any>): AuthToken {
    return new AuthToken(
      item['tokenId'],
      item['userId'],
      item['token'],
      new Date(item['expiresAt']),
      new Date(item['createdAt']),
      item['isUsed'] || false,
      item['usedAt'] ? new Date(item['usedAt']) : undefined,
      item['metadata'] ? JSON.parse(item['metadata']) : undefined
    );
  }

  /**
   * Check equality with another AuthToken
   */
  equals(other: AuthToken): boolean {
    return this.tokenId === other.tokenId && this.token === other.token;
  }

  /**
   * Get a string representation
   */
  toString(): string {
    return `AuthToken(${this.tokenId}, ${this.userId}, expires: ${this.expiresAt.toISOString()}, used: ${this.isUsed})`;
  }
}

/**
 * Custom error classes for AuthToken operations
 */
export class AuthTokenError extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'AuthTokenError';
  }
}

export class AuthTokenExpiredError extends AuthTokenError {
  constructor(message: string = 'Auth token has expired') {
    super(message, 'TOKEN_EXPIRED');
    this.name = 'AuthTokenExpiredError';
  }
}

export class AuthTokenUsedError extends AuthTokenError {
  constructor(message: string = 'Auth token has already been used') {
    super(message, 'TOKEN_USED');
    this.name = 'AuthTokenUsedError';
  }
}

export class AuthTokenNotFoundError extends AuthTokenError {
  constructor(message: string = 'Auth token not found') {
    super(message, 'TOKEN_NOT_FOUND');
    this.name = 'AuthTokenNotFoundError';
  }
}
