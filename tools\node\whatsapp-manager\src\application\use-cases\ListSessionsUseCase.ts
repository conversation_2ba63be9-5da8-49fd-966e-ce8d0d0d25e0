import { inject, injectable } from 'tsyringe';
import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { IWhatsAppService } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';
import { SessionStatus } from '../../domain/entities/SessionEntity';
// Removed unused import

/**
 * Request interface for listing sessions
 */
export interface ListSessionsRequest {
  status?: SessionStatus;
  limit?: number;
  offset?: number;
  includeInactive?: boolean;
  sortBy?: 'createdAt' | 'updatedAt' | 'connectedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Response interface for listing sessions
 */
export interface ListSessionsResponse {
  sessions: SessionSummary[];
  totalCount: number;
  activeCount: number;
  connectedCount: number;
  qrPendingCount: number;
  disconnectedCount: number;
  errorCount: number;
  pagination: {
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

/**
 * Session summary interface
 */
export interface SessionSummary {
  sessionId: string;
  userId: string;
  status: SessionStatus;
  phoneNumber?: string;
  connectedAt?: Date;
  disconnectedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  canReconnect: boolean;
  deviceName?: string;
  browserName?: string;
  lastActivity?: Date;
}

/**
 * Use case for listing WhatsApp sessions
 */
@injectable()
export class ListSessionsUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('IWhatsAppService') private whatsappService: IWhatsAppService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Execute the list sessions use case
   */
  async execute(request: ListSessionsRequest = {}): Promise<ListSessionsResponse> {
    const {
      status,
      limit = 50,
      offset = 0,
      includeInactive = true,
      sortBy = 'updatedAt',
      sortOrder = 'desc'
    } = request;

    this.logger.debug('Listing sessions', {
      status,
      limit,
      offset,
      includeInactive,
      sortBy,
      sortOrder
    });

    this.validateRequest(request);

    try {
      let sessions;
      let totalCount;

      // Get sessions based on status filter
      if (status) {
        sessions = await this.sessionRepository.findByStatus(status);
        totalCount = await this.sessionRepository.countByStatus(status);
      } else if (!includeInactive) {
        sessions = await this.sessionRepository.findAllActive();
        totalCount = sessions.length;
      } else {
        // For now, we'll use a scan operation. In production, consider implementing pagination
        sessions = await this.getAllSessions();
        totalCount = await this.sessionRepository.count();
      }

      // Sort sessions
      sessions = this.sortSessions(sessions, sortBy, sortOrder);

      // Apply pagination
      const paginatedSessions = sessions.slice(offset, offset + limit);

      // Convert to session summaries
      const sessionSummaries = await Promise.all(
        paginatedSessions.map(session => this.convertToSummary(session))
      );

      // Calculate counts
      const counts = await this.calculateCounts();

      const response: ListSessionsResponse = {
        sessions: sessionSummaries,
        totalCount,
        activeCount: counts.activeCount,
        connectedCount: counts.connectedCount,
        qrPendingCount: counts.qrPendingCount,
        disconnectedCount: counts.disconnectedCount,
        errorCount: counts.errorCount,
        pagination: {
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      };

      this.logger.debug('Sessions listed successfully', {
        returnedCount: sessionSummaries.length,
        totalCount,
        activeCount: counts.activeCount
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to list sessions', {
        error: (error as Error).message,
        request
      });
      throw error;
    }
  }

  /**
   * Get active sessions only
   */
  async getActiveSessions(): Promise<ListSessionsResponse> {
    return this.execute({
      includeInactive: false,
      sortBy: 'connectedAt',
      sortOrder: 'desc'
    });
  }

  /**
   * Get sessions by status
   */
  async getSessionsByStatus(status: SessionStatus): Promise<ListSessionsResponse> {
    return this.execute({
      status,
      includeInactive: true
    });
  }

  /**
   * Get session statistics
   */
  async getSessionStatistics(): Promise<{
    totalSessions: number;
    activeCount: number;
    connectedCount: number;
    qrPendingCount: number;
    disconnectedCount: number;
    errorCount: number;
    averageSessionAge: number;
    oldestSession?: Date;
    newestSession?: Date;
  }> {
    this.logger.debug('Getting session statistics');

    try {
      const [
        totalSessions,
        counts,
        allSessions
      ] = await Promise.all([
        this.sessionRepository.count(),
        this.calculateCounts(),
        this.getAllSessions()
      ]);

      let averageSessionAge = 0;
      let oldestSession: Date | undefined;
      let newestSession: Date | undefined;

      if (allSessions.length > 0) {
        const now = new Date();
        const totalAge = allSessions.reduce((sum, session) => {
          return sum + (now.getTime() - session.createdAt.getTime());
        }, 0);
        averageSessionAge = totalAge / allSessions.length;

        const sortedByCreation = allSessions.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
        oldestSession = sortedByCreation[0]?.createdAt;
        newestSession = sortedByCreation[sortedByCreation.length - 1]?.createdAt;
      }

      return {
        totalSessions,
        ...counts,
        averageSessionAge,
        oldestSession,
        newestSession
      };

    } catch (error) {
      this.logger.error('Failed to get session statistics', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Get all sessions (internal method)
   */
  private async getAllSessions() {
    // This is a simplified implementation. In production, you'd want to implement
    // proper pagination at the repository level to avoid loading all sessions into memory
    const statuses: SessionStatus[] = ['connected', 'qr_pending', 'connecting', 'disconnected', 'error', 'initializing'];
    const allSessions = [];

    for (const status of statuses) {
      const sessions = await this.sessionRepository.findByStatus(status);
      allSessions.push(...sessions);
    }

    return allSessions;
  }

  /**
   * Sort sessions based on criteria
   */
  private sortSessions(sessions: any[], sortBy: string, sortOrder: string) {
    return sessions.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'createdAt':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'updatedAt':
          aValue = a.updatedAt.getTime();
          bValue = b.updatedAt.getTime();
          break;
        case 'connectedAt':
          aValue = a.connectedAt?.getTime() || 0;
          bValue = b.connectedAt?.getTime() || 0;
          break;
        default:
          aValue = a.updatedAt.getTime();
          bValue = b.updatedAt.getTime();
      }

      if (sortOrder === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });
  }

  /**
   * Convert session entity to summary
   */
  private async convertToSummary(session: any): Promise<SessionSummary> {
    // Get real-time activity if available
    let lastActivity = session.updatedAt;
    try {
      const isActive = await this.whatsappService.isSessionActive(session.userId);
      if (isActive) {
        lastActivity = new Date();
      }
    } catch (error) {
      // Ignore errors, use repository data
    }

    return {
      sessionId: session.sessionId,
      userId: session.userId,
      status: session.status,
      phoneNumber: session.phoneNumber || undefined,
      connectedAt: session.connectedAt,
      disconnectedAt: session.disconnectedAt,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      isActive: session.isActive(),
      canReconnect: session.canReconnect(),
      deviceName: session.deviceName,
      browserName: session.browserName,
      lastActivity
    };
  }

  /**
   * Calculate session counts by status
   */
  private async calculateCounts(): Promise<{
    activeCount: number;
    connectedCount: number;
    qrPendingCount: number;
    disconnectedCount: number;
    errorCount: number;
  }> {
    const [
      connectedCount,
      qrPendingCount,
      disconnectedCount,
      errorCount
    ] = await Promise.all([
      this.sessionRepository.countByStatus('connected'),
      this.sessionRepository.countByStatus('qr_pending'),
      this.sessionRepository.countByStatus('disconnected'),
      this.sessionRepository.countByStatus('error')
    ]);

    const activeCount = connectedCount + qrPendingCount;

    return {
      activeCount,
      connectedCount,
      qrPendingCount,
      disconnectedCount,
      errorCount
    };
  }

  /**
   * Validate request parameters
   */
  private validateRequest(request: ListSessionsRequest): void {
    if (request.limit !== undefined) {
      if (typeof request.limit !== 'number' || request.limit < 1 || request.limit > 1000) {
        throw new Error('Limit must be a number between 1 and 1000');
      }
    }

    if (request.offset !== undefined) {
      if (typeof request.offset !== 'number' || request.offset < 0) {
        throw new Error('Offset must be a non-negative number');
      }
    }

    if (request.sortBy && !['createdAt', 'updatedAt', 'connectedAt'].includes(request.sortBy)) {
      throw new Error('SortBy must be one of: createdAt, updatedAt, connectedAt');
    }

    if (request.sortOrder && !['asc', 'desc'].includes(request.sortOrder)) {
      throw new Error('SortOrder must be either asc or desc');
    }

    if (request.status && !['initializing', 'qr_pending', 'connecting', 'connected', 'disconnected', 'error'].includes(request.status)) {
      throw new Error('Invalid status value');
    }
  }
}
