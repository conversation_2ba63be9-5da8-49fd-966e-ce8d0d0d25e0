# Network Foundation for EzyChat Infrastructure
# This file manages the VPC, subnets, NAT Gateway, and routing configuration

# Create network foundation instead of using default VPC
module "network" {
  source = "../../modules/network_foundation"

  env                  = var.env
  vpc_cidr             = var.vpc_cidr
  public_subnet_count  = var.public_subnet_count
  private_subnet_count = var.private_subnet_count

  enable_nat_gateway   = var.enable_nat_gateway
  nat_gateway_strategy = var.nat_gateway_strategy

  tags = local.this.tags

  vpc_tags = {
    Name = "${var.env}-ezychat-vpc"
  }

  public_subnet_tags = {
    Type    = "Public"
    Purpose = "ALB and ECS tasks"
  }

  private_subnet_tags = {
    Type    = "Private"
    Purpose = "Reserved for secure ECS deployment"
  }
}

# Network Outputs
output "vpc_id" {
  value       = module.network.vpc_id
  description = "ID of the VPC"
}

output "vpc_cidr_block" {
  value       = module.network.vpc_cidr_block
  description = "CIDR block of the VPC"
}

output "public_subnet_ids" {
  value       = module.network.public_subnet_ids
  description = "List of public subnet IDs"
}

output "private_subnet_ids" {
  value       = module.network.private_subnet_ids
  description = "List of private subnet IDs"
}

output "nat_gateway_public_ips" {
  value       = module.network.nat_gateway_public_ips
  description = "Public IPs of NAT Gateways"
}

output "internet_gateway_id" {
  value       = module.network.internet_gateway_id
  description = "ID of the Internet Gateway"
}

output "availability_zones" {
  value       = module.network.availability_zones
  description = "List of availability zones used"
}
