module "core" {
  source = "../core"

  env = "uat"

  # Network Configuration for UAT Environment
  # Using 10.0.x.x range for UAT (recommended CIDR allocation)
  vpc_cidr             = "10.0.0.0/16" # UAT: 10.0.0.0/16 (65,536 IPs)
  public_subnet_count  = 2             # 2 public subnets for ALB
  private_subnet_count = 2             # 2 private subnets for ECS tasks

  # NAT Gateway Configuration
  enable_nat_gateway   = false    # UAT: Disabled for cost savings (not needed with public subnets)
  nat_gateway_strategy = "single" # Not used when NAT Gateway is disabled
  use_private_subnets  = false    # UAT: Use public subnets for cost savings

  # Production Recommendations (commented for reference):
  # vpc_cidr             = "********/16"    # Prod: ********/16 (separate from UAT)
  # public_subnet_count  = 3                # 3 AZs for high availability
  # private_subnet_count = 3                # 3 private subnets for production workloads
  # enable_nat_gateway   = true             # Prod: Enable NAT Gateway for private subnets
  # nat_gateway_strategy = "per_az"         # High availability: NAT Gateway per AZ (~$135/month)
  # use_private_subnets  = true             # Prod: Use private subnets for security

  # Domain Naming:
  # UAT: {api_subdomain}.uat.{domain_name} (e.g., api.uat.ezychat.ai)
  # Prod: {api_subdomain}.{domain_name} (e.g., api.ezychat.ai)

  tags = {
    project = "ezychat"
  }

  providers = {
    aws        = aws
    aws.master = aws.master
  }
}

# Pass through all outputs from core module
output "core" {
  value       = module.core
  description = "All outputs from the core module"
}
