terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
    random = {
      source  = "hashicorp/random"
      version = ">= 3.0.0"
    }
  }
}

resource "random_id" "unique_suffix" {
  byte_length = 2
}

locals {
  name_prefix = "${var.env}-${var.app_name}-${random_id.unique_suffix.hex}"
}

# Create IAM role for EventBridge Scheduler to execute Lambda
resource "aws_iam_role" "scheduler_role" {
  name = "${local.name_prefix}-ecs-scheduler-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "scheduler.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-ecs-scheduler-role"
    }
  )
}

# Create IAM policy for EventBridge Scheduler to invoke Lambda
resource "aws_iam_policy" "scheduler_policy" {
  name        = "${local.name_prefix}-ecs-scheduler-policy"
  description = "Policy for ECS Scheduler to invoke Lambda"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "lambda:InvokeFunction"
        ]
        Effect   = "Allow"
        Resource = aws_lambda_function.ecs_scheduler.arn
      }
    ]
  })
}

# Attach policy to scheduler role
resource "aws_iam_role_policy_attachment" "scheduler_policy_attachment" {
  role       = aws_iam_role.scheduler_role.name
  policy_arn = aws_iam_policy.scheduler_policy.arn
}

# Create IAM role for Lambda function
resource "aws_iam_role" "lambda_role" {
  name = "${local.name_prefix}-ecs-scheduler-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-ecs-scheduler-lambda-role"
    }
  )
}

# Create IAM policy for Lambda function
resource "aws_iam_policy" "lambda_policy" {
  name        = "${local.name_prefix}-ecs-scheduler-lambda-policy"
  description = "Policy for ECS Scheduler Lambda function"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecs:ListServices",
          "ecs:DescribeServices",
          "ecs:UpdateService",
          "ecs:ListTagsForResource",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Attach policy to Lambda role
resource "aws_iam_role_policy_attachment" "lambda_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

# Create Lambda function for scaling ECS services
resource "aws_lambda_function" "ecs_scheduler" {
  function_name = "${local.name_prefix}-ecs-scheduler"
  role          = aws_iam_role.lambda_role.arn
  handler       = "index.handler"
  runtime       = "nodejs18.x"
  timeout       = 30
  memory_size   = 128

  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      ECS_CLUSTER_NAME = var.ecs_cluster_name
      TAG_KEY          = var.tag_key
      TAG_VALUE        = var.tag_value
    }
  }

  tags = merge(
    var.tags,
    {
      Name = "${local.name_prefix}-ecs-scheduler"
    }
  )
}

# Create EventBridge Scheduler for scaling down
resource "aws_scheduler_schedule" "scale_down" {
  name                = "${local.name_prefix}-ecs-scale-down"
  description         = "Scale down ECS services with tag ${var.tag_key}=${var.tag_value}"
  schedule_expression = var.scale_down_expression
  state               = var.enabled ? "ENABLED" : "DISABLED"

  flexible_time_window {
    mode = "OFF"
  }

  target {
    arn      = aws_lambda_function.ecs_scheduler.arn
    role_arn = aws_iam_role.scheduler_role.arn

    input = jsonencode({
      action       = "scale_down",
      desiredCount = 0
    })
  }

  # EventBridge Scheduler doesn't support tags
}

# Create EventBridge Scheduler for scaling up
resource "aws_scheduler_schedule" "scale_up" {
  name                = "${local.name_prefix}-ecs-scale-up"
  description         = "Scale up ECS services with tag ${var.tag_key}=${var.tag_value}"
  schedule_expression = var.scale_up_expression
  state               = var.enabled ? "ENABLED" : "DISABLED"

  flexible_time_window {
    mode = "OFF"
  }

  target {
    arn      = aws_lambda_function.ecs_scheduler.arn
    role_arn = aws_iam_role.scheduler_role.arn

    input = jsonencode({
      action       = "scale_up",
      desiredCount = var.default_desired_count
    })
  }

  # EventBridge Scheduler doesn't support tags
}
