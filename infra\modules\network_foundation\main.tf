# Get available availability zones
data "aws_availability_zones" "available" {
  state = "available"
}

# Calculate availability zones to use
locals {
  # Use provided AZs or all available AZs
  availability_zones = length(var.availability_zones) > 0 ? var.availability_zones : data.aws_availability_zones.available.names

  # Calculate total subnets needed
  total_subnets = var.public_subnet_count + var.private_subnet_count

  # Calculate subnet size based on VPC CIDR and total subnets needed
  # Add buffer for future expansion (multiply by 2)
  subnet_bits = ceil(log(local.total_subnets * 2, 2))

  # Common tags
  common_tags = merge(var.tags, {
    Environment = var.env
    ManagedBy   = "Terraform"
  })
}

# Create VPC
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  tags = merge(local.common_tags, var.vpc_tags, {
    Name = "${var.env}-vpc"
  })
}

# Create Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${var.env}-igw"
  })
}

# Create public subnets
resource "aws_subnet" "public" {
  count = var.public_subnet_count

  vpc_id                  = aws_vpc.main.id
  cidr_block              = cidrsubnet(var.vpc_cidr, local.subnet_bits, count.index)
  availability_zone       = local.availability_zones[count.index % length(local.availability_zones)]
  map_public_ip_on_launch = true

  tags = merge(local.common_tags, var.public_subnet_tags, {
    Name = "${var.env}-public-subnet-${count.index + 1}"
    Type = "Public"
    AZ   = local.availability_zones[count.index % length(local.availability_zones)]
  })
}

# Create private subnets
resource "aws_subnet" "private" {
  count = var.private_subnet_count

  vpc_id            = aws_vpc.main.id
  cidr_block        = cidrsubnet(var.vpc_cidr, local.subnet_bits, var.public_subnet_count + count.index)
  availability_zone = local.availability_zones[count.index % length(local.availability_zones)]

  tags = merge(local.common_tags, var.private_subnet_tags, {
    Name = "${var.env}-private-subnet-${count.index + 1}"
    Type = "Private"
    AZ   = local.availability_zones[count.index % length(local.availability_zones)]
  })
}

# Create Elastic IPs for NAT Gateways
resource "aws_eip" "nat" {
  count = var.enable_nat_gateway ? (var.nat_gateway_strategy == "per_az" ? var.private_subnet_count : 1) : 0

  domain     = "vpc"
  depends_on = [aws_internet_gateway.main]

  tags = merge(local.common_tags, {
    Name = "${var.env}-nat-eip-${count.index + 1}"
  })
}

# Create NAT Gateways
resource "aws_nat_gateway" "main" {
  count = var.enable_nat_gateway ? (var.nat_gateway_strategy == "per_az" ? var.private_subnet_count : 1) : 0

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = var.nat_gateway_strategy == "per_az" ? aws_subnet.public[count.index % var.public_subnet_count].id : aws_subnet.public[0].id

  tags = merge(local.common_tags, {
    Name = "${var.env}-nat-gateway-${count.index + 1}"
  })

  depends_on = [aws_internet_gateway.main]
}

# Create route table for public subnets
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }

  tags = merge(local.common_tags, {
    Name = "${var.env}-public-rt"
    Type = "Public"
  })
}

# Associate public subnets with public route table
resource "aws_route_table_association" "public" {
  count = var.public_subnet_count

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

# Create route tables for private subnets
resource "aws_route_table" "private" {
  count = var.private_subnet_count > 0 ? (var.nat_gateway_strategy == "per_az" ? var.private_subnet_count : 1) : 0

  vpc_id = aws_vpc.main.id

  # Add route to NAT Gateway if enabled
  dynamic "route" {
    for_each = var.enable_nat_gateway ? [1] : []
    content {
      cidr_block     = "0.0.0.0/0"
      nat_gateway_id = var.nat_gateway_strategy == "per_az" ? aws_nat_gateway.main[count.index].id : aws_nat_gateway.main[0].id
    }
  }

  tags = merge(local.common_tags, {
    Name = "${var.env}-private-rt-${count.index + 1}"
    Type = "Private"
  })
}

# Associate private subnets with private route tables
resource "aws_route_table_association" "private" {
  count = var.private_subnet_count

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = var.nat_gateway_strategy == "per_az" ? aws_route_table.private[count.index].id : aws_route_table.private[0].id
}
