import { Request, Response } from 'express';
import { injectable, inject } from 'tsyringe';
import { GenerateQrLinkUseCase, GenerateQrLinkRequest } from '../../application/use-cases/GenerateQrLinkUseCase';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';
import { AuthService } from '../../infrastructure/services/AuthService';
import { AuthTokenNotFoundError, AuthTokenExpiredError, AuthTokenUsedError } from '../../domain/value-objects/AuthToken';
import { ErrorMappingService } from '../../shared/errors/error-mapping.service';
import { DI_TOKENS } from '../../di/tokens';

/**
 * Controller for authentication-related endpoints
 * Handles QR token generation, validation, and management
 */
@injectable()
export class AuthController {
  constructor(
    @inject(DI_TOKENS.GenerateQrLinkUseCase) private readonly generateQrLinkUseCase: GenerateQrLinkUseCase,
    @inject(DI_TOKENS.IAuthTokenRepository) private readonly authTokenRepository: IAuthTokenRepository,
    @inject(DI_TOKENS.AuthService) private readonly authService: AuthService
  ) {}

  /**
   * POST /api/auth/qr-link
   * Generate a new QR authentication link
   */
  async generateQrLink(req: Request, res: Response): Promise<void> {
    try {
      const request: GenerateQrLinkRequest = {
        userId: req.body.userId,
        expirySeconds: req.body.expirySeconds,
        maxActiveTokens: req.body.maxActiveTokens,
        metadata: req.body.metadata,
        baseUrl: req.body.baseUrl
      };

      const result = await this.generateQrLinkUseCase.execute(request);

      if (result.success) {
        res.status(201).json({
          success: true,
          message: 'QR authentication link generated successfully',
          data: result.data
        });
      } else {
        const statusCode = ErrorMappingService.getStatusCode(result.error);
        res.status(statusCode).json({
          success: false,
          message: result.error?.message || 'Failed to generate QR link',
          error: result.error
        });
      }
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to generate QR authentication link');
    }
  }

  /**
   * GET /api/auth/qr-link/:tokenId
   * Get QR authentication link details by token ID
   */
  async getQrLinkDetails(req: Request, res: Response): Promise<void> {
    try {
      const { tokenId } = req.params;

      if (!tokenId) {
        res.status(400).json({
          success: false,
          message: 'Token ID is required',
          error: { code: 'INVALID_TOKEN_ID', message: 'Token ID parameter is missing' }
        });
        return;
      }

      const token = await this.authTokenRepository.findByTokenId(tokenId);

      if (!token) {
        res.status(404).json({
          success: false,
          message: 'QR authentication link not found',
          error: { code: 'TOKEN_NOT_FOUND', message: `Token with ID ${tokenId} not found` }
        });
        return;
      }

      const qrAuthUrl = this.authService.generateQrAuthUrl(token.token);

      res.status(200).json({
        success: true,
        message: 'QR authentication link details retrieved successfully',
        data: {
          tokenId: token.tokenId,
          userId: token.userId,
          qrAuthUrl,
          expiresAt: token.expiresAt.toISOString(),
          expiresInSeconds: token.getRemainingTimeSeconds(),
          createdAt: token.createdAt.toISOString(),
          isExpired: token.isExpired(),
          isUsed: token.isUsed,
          usedAt: token.usedAt?.toISOString(),
          metadata: token.metadata
        }
      });
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to get QR link details');
    }
  }

  /**
   * POST /api/auth/qr-link/:tokenId/validate
   * Validate a QR authentication token
   */
  async validateQrToken(req: Request, res: Response): Promise<void> {
    try {
      const { tokenId } = req.params;

      if (!tokenId) {
        res.status(400).json({
          success: false,
          message: 'Token ID is required',
          error: { code: 'INVALID_TOKEN_ID', message: 'Token ID parameter is missing' }
        });
        return;
      }

      const token = await this.authTokenRepository.validateToken(tokenId);

      res.status(200).json({
        success: true,
        message: 'Token is valid',
        data: {
          tokenId: token.tokenId,
          userId: token.userId,
          expiresAt: token.expiresAt.toISOString(),
          expiresInSeconds: token.getRemainingTimeSeconds(),
          isValid: token.isValid(),
          metadata: token.metadata
        }
      });
    } catch (error) {
      if (error instanceof AuthTokenNotFoundError) {
        res.status(404).json({
          success: false,
          message: 'Token not found',
          error: { code: 'TOKEN_NOT_FOUND', message: error.message }
        });
      } else if (error instanceof AuthTokenExpiredError) {
        res.status(410).json({
          success: false,
          message: 'Token has expired',
          error: { code: 'TOKEN_EXPIRED', message: error.message }
        });
      } else if (error instanceof AuthTokenUsedError) {
        res.status(409).json({
          success: false,
          message: 'Token has already been used',
          error: { code: 'TOKEN_USED', message: error.message }
        });
      } else {
        this.handleControllerError(res, error, 'Failed to validate token');
      }
    }
  }

  /**
   * POST /api/auth/qr-link/:tokenId/use
   * Mark a QR authentication token as used
   */
  async useQrToken(req: Request, res: Response): Promise<void> {
    try {
      const { tokenId } = req.params;

      if (!tokenId) {
        res.status(400).json({
          success: false,
          message: 'Token ID is required',
          error: { code: 'INVALID_TOKEN_ID', message: 'Token ID parameter is missing' }
        });
        return;
      }

      const usedToken = await this.authTokenRepository.markAsUsed(tokenId);

      res.status(200).json({
        success: true,
        message: 'Token marked as used successfully',
        data: {
          tokenId: usedToken.tokenId,
          userId: usedToken.userId,
          usedAt: usedToken.usedAt?.toISOString(),
          expiresAt: usedToken.expiresAt.toISOString(),
          metadata: usedToken.metadata
        }
      });
    } catch (error) {
      if (error instanceof AuthTokenNotFoundError) {
        res.status(404).json({
          success: false,
          message: 'Token not found',
          error: { code: 'TOKEN_NOT_FOUND', message: error.message }
        });
      } else if (error instanceof AuthTokenExpiredError) {
        res.status(410).json({
          success: false,
          message: 'Cannot use expired token',
          error: { code: 'TOKEN_EXPIRED', message: error.message }
        });
      } else if (error instanceof AuthTokenUsedError) {
        res.status(409).json({
          success: false,
          message: 'Token has already been used',
          error: { code: 'TOKEN_USED', message: error.message }
        });
      } else {
        this.handleControllerError(res, error, 'Failed to use token');
      }
    }
  }

  /**
   * GET /api/auth/users/:userId/tokens
   * Get all tokens for a specific user
   */
  async getUserTokens(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const includeExpired = req.query['includeExpired'] === 'true';

      if (!userId) {
        res.status(400).json({
          success: false,
          message: 'User ID is required',
          error: { code: 'INVALID_USER_ID', message: 'User ID parameter is missing' }
        });
        return;
      }

      const tokens = await this.authTokenRepository.findByUserId(userId, includeExpired);

      const tokenData = tokens.map(token => ({
        tokenId: token.tokenId,
        expiresAt: token.expiresAt.toISOString(),
        expiresInSeconds: token.getRemainingTimeSeconds(),
        createdAt: token.createdAt.toISOString(),
        isExpired: token.isExpired(),
        isUsed: token.isUsed,
        usedAt: token.usedAt?.toISOString(),
        metadata: token.metadata
      }));

      res.status(200).json({
        success: true,
        message: 'User tokens retrieved successfully',
        data: {
          userId,
          tokens: tokenData,
          totalCount: tokens.length,
          activeCount: tokens.filter(t => !t.isExpired() && !t.isUsed).length,
          expiredCount: tokens.filter(t => t.isExpired()).length,
          usedCount: tokens.filter(t => t.isUsed).length
        }
      });
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to get user tokens');
    }
  }

  /**
   * DELETE /api/auth/users/:userId/tokens
   * Revoke all tokens for a specific user
   */
  async revokeUserTokens(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId) {
        res.status(400).json({
          success: false,
          message: 'User ID is required',
          error: { code: 'INVALID_USER_ID', message: 'User ID parameter is missing' }
        });
        return;
      }

      const deletedCount = await this.generateQrLinkUseCase.revokeUserTokens(userId);

      res.status(200).json({
        success: true,
        message: `Successfully revoked ${deletedCount} tokens for user ${userId}`,
        data: {
          userId,
          revokedTokens: deletedCount
        }
      });
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to revoke user tokens');
    }
  }

  /**
   * DELETE /api/auth/qr-link/:tokenId
   * Delete a specific QR authentication token
   */
  async deleteQrToken(req: Request, res: Response): Promise<void> {
    try {
      const { tokenId } = req.params;

      if (!tokenId) {
        res.status(400).json({
          success: false,
          message: 'Token ID is required',
          error: { code: 'INVALID_TOKEN_ID', message: 'Token ID parameter is missing' }
        });
        return;
      }

      const deleted = await this.authTokenRepository.delete(tokenId);

      if (deleted) {
        res.status(200).json({
          success: true,
          message: 'QR authentication token deleted successfully',
          data: { tokenId }
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Token not found',
          error: { code: 'TOKEN_NOT_FOUND', message: `Token with ID ${tokenId} not found` }
        });
      }
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to delete token');
    }
  }

  /**
   * GET /api/auth/statistics
   * Get authentication statistics
   */
  async getStatistics(_req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.generateQrLinkUseCase.getStatistics();

      res.status(200).json({
        success: true,
        message: 'Authentication statistics retrieved successfully',
        data: stats
      });
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to get statistics');
    }
  }

  /**
   * POST /api/auth/cleanup
   * Cleanup expired tokens
   */
  async cleanupExpiredTokens(req: Request, res: Response): Promise<void> {
    try {
      const batchSize = parseInt(req.body.batchSize || '100', 10);
      
      if (batchSize <= 0 || batchSize > 1000) {
        res.status(400).json({
          success: false,
          message: 'Batch size must be between 1 and 1000',
          error: { code: 'INVALID_BATCH_SIZE', message: 'Invalid batch size parameter' }
        });
        return;
      }

      const deletedCount = await this.generateQrLinkUseCase.cleanupExpiredTokens(batchSize);

      res.status(200).json({
        success: true,
        message: `Successfully cleaned up ${deletedCount} expired tokens`,
        data: { deletedTokens: deletedCount }
      });
    } catch (error) {
      this.handleControllerError(res, error, 'Failed to cleanup expired tokens');
    }
  }

  /**
   * Handle controller errors consistently using centralized error mapping
   */
  private handleControllerError(res: Response, error: any, defaultMessage: string): void {
    // Log error with appropriate level
    if (ErrorMappingService.shouldLogAsWarning(error)) {
      console.warn(`AuthController Warning: ${defaultMessage}`, error.message);
    } else {
      console.error(`AuthController Error: ${defaultMessage}`, error);
    }

    // Create standardized error response
    const errorResponse = ErrorMappingService.createErrorResponse(error);

    // Override message if default message is more descriptive
    if (defaultMessage && (!error.message || error.message === 'An unexpected error occurred')) {
      errorResponse.message = defaultMessage;
      errorResponse.error.message = defaultMessage;
    }

    res.status(errorResponse.error.statusCode).json(errorResponse);
  }
}
