import { injectable, inject } from 'tsyringe';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';
import { AuthService } from '../../infrastructure/services/AuthService';
import { AuthToken, AuthTokenError } from '../../domain/value-objects/AuthToken';
import { DI_TOKENS } from '../../di/tokens';

/**
 * Request DTO for generating QR authentication link
 */
export interface GenerateQrLinkRequest {
  userId: string;
  expirySeconds?: number;
  maxActiveTokens?: number;
  metadata?: Record<string, any>;
  baseUrl?: string;
}

/**
 * Response DTO for QR authentication link generation
 */
export interface GenerateQrLinkResponse {
  success: boolean;
  data?: {
    tokenId: string;
    token: string;
    qrAuthUrl: string;
    expiresAt: string;
    expiresInSeconds: number;
    userId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * Use case for generating QR authentication links
 * Handles business logic for token creation, validation, and cleanup
 */
@injectable()
export class GenerateQrLinkUseCase {
  constructor(
    @inject(DI_TOKENS.IAuthTokenRepository) private readonly authTokenRepository: IAuthTokenRepository,
    @inject(DI_TOKENS.AuthService) private readonly authService: AuthService
  ) {}

  /**
   * Execute the use case to generate a QR authentication link
   */
  async execute(request: GenerateQrLinkRequest): Promise<GenerateQrLinkResponse> {
    try {
      // Validate input
      this.validateRequest(request);

      // Check and enforce max active tokens limit
      await this.enforceMaxActiveTokens(request.userId, request.maxActiveTokens);

      // Generate JWT token
      const jwtToken = this.authService.generateQrAuthToken(
        request.userId,
        request.expirySeconds
      );

      // Combine all metadata upfront
      const combinedMetadata = {
        ...request.metadata,
        ...this.authService.createTokenMetadata()
      };

      // Create AuthToken domain object with combined metadata
      const authToken = this.authService.createAuthTokenFromJWT(jwtToken);
      const finalAuthToken = AuthToken.create(
        authToken.userId,
        authToken.token,
        authToken.getRemainingTimeSeconds(),
        Object.keys(combinedMetadata).length > 0 ? combinedMetadata : undefined
      );

      // Store in repository
      await this.authTokenRepository.create(finalAuthToken);

      // Generate QR authentication URL
      const qrAuthUrl = this.authService.generateQrAuthUrl(jwtToken, request.baseUrl);

      return this.buildSuccessResponse(finalAuthToken, jwtToken, qrAuthUrl);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Validate the request parameters
   */
  private validateRequest(request: GenerateQrLinkRequest): void {
    if (!request) {
      throw new AuthTokenError('Request is required', 'INVALID_REQUEST');
    }

    if (!request.userId || request.userId.trim().length === 0) {
      throw new AuthTokenError('User ID is required', 'INVALID_USER_ID');
    }

    if (request.userId.trim().length > 255) {
      throw new AuthTokenError('User ID must be 255 characters or less', 'INVALID_USER_ID');
    }

    if (request.expirySeconds !== undefined) {
      if (typeof request.expirySeconds !== 'number' || 
          request.expirySeconds <= 0 || 
          request.expirySeconds > 86400) {
        throw new AuthTokenError(
          'Expiry seconds must be a number between 1 and 86400 (24 hours)',
          'INVALID_EXPIRY'
        );
      }
    }

    if (request.maxActiveTokens !== undefined) {
      if (typeof request.maxActiveTokens !== 'number' || 
          request.maxActiveTokens < 0 || 
          request.maxActiveTokens > 100) {
        throw new AuthTokenError(
          'Max active tokens must be a number between 0 and 100',
          'INVALID_MAX_TOKENS'
        );
      }
    }

    if (request.metadata && typeof request.metadata !== 'object') {
      throw new AuthTokenError('Metadata must be an object', 'INVALID_METADATA');
    }

    if (request.baseUrl && typeof request.baseUrl !== 'string') {
      throw new AuthTokenError('Base URL must be a string', 'INVALID_BASE_URL');
    }

    // Validate base URL format if provided
    if (request.baseUrl) {
      try {
        new URL(request.baseUrl);
      } catch {
        throw new AuthTokenError('Base URL must be a valid URL', 'INVALID_BASE_URL');
      }
    }
  }

  /**
   * Enforce maximum active tokens limit for a user
   */
  private async enforceMaxActiveTokens(userId: string, maxActiveTokens?: number): Promise<void> {
    const maxTokens = maxActiveTokens ?? parseInt(process.env['QR_TOKEN_MAX_ACTIVE'] || '5', 10);
    
    if (maxTokens <= 0) {
      return; // No limit
    }

    const activeTokenCount = await this.authTokenRepository.countActiveTokensByUserId(userId);
    
    if (activeTokenCount >= maxTokens) {
      // Clean up expired tokens first
      await this.cleanupExpiredTokensForUser(userId);
      
      // Check again after cleanup
      const activeTokenCountAfterCleanup = await this.authTokenRepository.countActiveTokensByUserId(userId);
      
      if (activeTokenCountAfterCleanup >= maxTokens) {
        throw new AuthTokenError(
          `Maximum active tokens limit (${maxTokens}) reached for user ${userId}`,
          'MAX_TOKENS_EXCEEDED'
        );
      }
    }
  }

  /**
   * Clean up expired tokens for a specific user
   */
  private async cleanupExpiredTokensForUser(userId: string): Promise<void> {
    try {
      const allTokens = await this.authTokenRepository.findByUserId(userId, true);
      const expiredTokens = allTokens.filter(token => token.isExpired());
      
      if (expiredTokens.length > 0) {
        const expiredTokenIds = expiredTokens.map(token => token.tokenId);
        await this.authTokenRepository.batchDelete(expiredTokenIds);
      }
    } catch (error) {
      // Log error but don't fail the main operation
      console.warn(`Failed to cleanup expired tokens for user ${userId}:`, error.message);
    }
  }

  /**
   * Build success response for QR link generation
   */
  private buildSuccessResponse(
    authToken: AuthToken,
    jwtToken: string,
    qrAuthUrl: string
  ): GenerateQrLinkResponse {
    return {
      success: true,
      data: {
        tokenId: authToken.tokenId,
        token: jwtToken,
        qrAuthUrl,
        expiresAt: authToken.expiresAt.toISOString(),
        expiresInSeconds: authToken.getRemainingTimeSeconds(),
        userId: authToken.userId,
        createdAt: authToken.createdAt.toISOString(),
        metadata: authToken.metadata
      }
    };
  }

  /**
   * Handle errors and convert to response format
   */
  private handleError(error: any): GenerateQrLinkResponse {
    if (error instanceof AuthTokenError) {
      return {
        success: false,
        error: {
          code: error.code || 'AUTH_TOKEN_ERROR',
          message: error.message,
          details: {
            name: error.name,
            stack: process.env['NODE_ENV'] === 'development' ? error.stack : undefined
          }
        }
      };
    }

    // Handle other types of errors
    const errorCode = this.getErrorCode(error);
    const errorMessage = error.message || 'An unexpected error occurred';

    return {
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
        details: {
          name: error.name,
          stack: process.env['NODE_ENV'] === 'development' ? error.stack : undefined
        }
      }
    };
  }

  /**
   * Get appropriate error code based on error type
   */
  private getErrorCode(error: any): string {
    if (error.name === 'ValidationError') {
      return 'VALIDATION_ERROR';
    }
    
    if (error.name === 'ConditionalCheckFailedException') {
      return 'TOKEN_ALREADY_EXISTS';
    }
    
    if (error.message?.includes('DynamoDB') || error.message?.includes('AWS')) {
      return 'DATABASE_ERROR';
    }
    
    if (error.message?.includes('JWT') || error.message?.includes('token')) {
      return 'TOKEN_ERROR';
    }
    
    return 'INTERNAL_ERROR';
  }

  /**
   * Get use case statistics for monitoring
   */
  async getStatistics(): Promise<{
    totalTokensGenerated: number;
    activeTokens: number;
    expiredTokens: number;
    usedTokens: number;
    averageTokenLifetime: number;
  }> {
    try {
      const stats = await this.authTokenRepository.getStatistics();
      
      return {
        totalTokensGenerated: stats.totalTokens,
        activeTokens: stats.activeTokens,
        expiredTokens: stats.expiredTokens,
        usedTokens: stats.usedTokens,
        averageTokenLifetime: 0 // TODO: Calculate from token data
      };
    } catch (error) {
      throw new AuthTokenError(`Failed to get statistics: ${error.message}`);
    }
  }

  /**
   * Cleanup expired tokens across all users
   */
  async cleanupExpiredTokens(batchSize: number = 100): Promise<number> {
    try {
      return await this.authTokenRepository.deleteExpiredTokens(batchSize);
    } catch (error) {
      throw new AuthTokenError(`Failed to cleanup expired tokens: ${error.message}`);
    }
  }

  /**
   * Revoke all active tokens for a user
   */
  async revokeUserTokens(userId: string): Promise<number> {
    try {
      if (!userId || userId.trim().length === 0) {
        throw new AuthTokenError('User ID is required', 'INVALID_USER_ID');
      }

      return await this.authTokenRepository.deleteByUserId(userId);
    } catch (error) {
      if (error instanceof AuthTokenError) {
        throw error;
      }
      throw new AuthTokenError(`Failed to revoke user tokens: ${error.message}`);
    }
  }
}
