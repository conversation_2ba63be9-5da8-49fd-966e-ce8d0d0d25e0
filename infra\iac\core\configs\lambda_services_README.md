# Lambda Services Configuration

This directory contains the centralized Lambda services configuration used across all environments.

## Configuration Structure

### Single Configuration File
- **File**: `lambda_services.yaml`
- **Purpose**: Defines all Lambda services and their settings
- **Scope**: Applied to all environments
- **Contains**:
  - Service definitions
  - Resource settings (timeout, memory)
  - API Gateway routes
  - Container image URIs

## Simple Configuration Approach

The configuration uses a single YAML file with minimal settings. Environment-specific differences (like image tags, secrets, VPC settings) are handled through Terraform variables and environment-specific infrastructure code.

## Example Configuration

### Simple Configuration (`core/configs/lambda_services.yaml`)
```yaml
# WhatsApp Personal Lambda
whatsapp-personal-lambda:
  image_repository_uri: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-personal-lambda:latest"
  timeout: 30
  memory_size: 512

  api_gateway:
    routes:
      - path: "/whatsapp-api/{proxy+}"
        method: "ANY"
      - path: "/whatsapp-api/health"
        method: "GET"

# Notification Service
notification-service:
  image_repository_uri: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/notification-service:latest"
  timeout: 30
  memory_size: 512

  api_gateway:
    routes:
      - path: "/notifications"
        method: "POST"
      - path: "/notifications/{id}"
        method: "GET"

# Health Check Service
health-check:
  image_repository_uri: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/health-check:latest"
  timeout: 10
  memory_size: 256

  api_gateway:
    routes:
      - path: "/health"
        method: "GET"
```

## Benefits of This Structure

1. **Simplicity**: Single configuration file, easy to understand and maintain
2. **Consistency**: All services defined in one place
3. **Maintainability**: Changes apply to all environments
4. **Clarity**: No complex merging logic or overrides

## Adding New Services

1. **Add Service**: Define the service in `core/configs/lambda_services.yaml`
2. **Deploy**: The service will be automatically included in all environments

## Configuration Fields

### Required Fields
- `image_repository_uri`: ECR repository URI for the Lambda container
- `timeout`: Function timeout in seconds
- `memory_size`: Memory allocation in MB
- `api_gateway.routes`: List of API Gateway routes

### Optional Fields
- `environment_variables`: Environment variables for the Lambda function
- `secrets`: AWS Secrets Manager ARNs
- `vpc_config`: VPC configuration for the Lambda function
- `iam_policy`: Custom IAM policy JSON
- `tags`: Additional resource tags

Environment-specific values (like different image tags for UAT vs Production) should be handled through Terraform variables in the environment-specific infrastructure code.
