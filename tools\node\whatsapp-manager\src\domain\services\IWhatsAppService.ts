import { EventEmitter } from 'events';
import { SessionStatus, AuthenticationState } from '../entities/SessionEntity';

/**
 * WhatsApp service interface for managing Baileys connections
 */
export interface IWhatsAppService extends EventEmitter {
  /**
   * Start a new WhatsApp session
   */
  startSession(userId: string): Promise<StartSessionResult>;

  /**
   * Reconnect an existing session
   */
  reconnectSession(userId: string): Promise<StartSessionResult>;

  /**
   * Regenerate QR code for pending session
   */
  regenerateQR(userId: string): Promise<{ qrCode: string }>;

  /**
   * Get current session status
   */
  getSessionStatus(userId: string): Promise<SessionStatus>;

  /**
   * Terminate and cleanup session
   */
  terminateSession(userId: string): Promise<void>;

  /**
   * Restore all persisted sessions on startup
   */
  restoreAllSessions(): Promise<number>;

  /**
   * Check if session is active and connected
   */
  isSessionActive(userId: string): Promise<boolean>;

  /**
   * Get active session count
   */
  getActiveSessionCount(): Promise<number>;

  /**
   * Get all active user IDs
   */
  getActiveUserIds(): Promise<string[]>;

  /**
   * Send a test message (for connection verification)
   */
  sendTestMessage(userId: string, phoneNumber: string, message: string): Promise<void>;

  /**
   * Shutdown the WhatsApp service and cleanup all resources
   */
  shutdown(): Promise<void>;

  // Event emitter interface
  on(event: 'qr', listener: (userId: string, qr: string) => void): this;
  on(event: 'connecting', listener: (userId: string) => void): this;
  on(event: 'connected', listener: (userId: string, phoneNumber: string) => void): this;
  on(event: 'disconnected', listener: (userId: string, reason: DisconnectReason) => void): this;
  on(event: 'error', listener: (userId: string, error: Error) => void): this;
  on(event: 'auth_state_updated', listener: (userId: string, authState: AuthenticationState) => void): this;
  on(event: 'message', listener: (userId: string, message: any) => void): this;

  emit(event: 'qr', userId: string, qr: string): boolean;
  emit(event: 'connecting', userId: string): boolean;
  emit(event: 'connected', userId: string, phoneNumber: string): boolean;
  emit(event: 'disconnected', userId: string, reason: DisconnectReason): boolean;
  emit(event: 'error', userId: string, error: Error): boolean;
  emit(event: 'auth_state_updated', userId: string, authState: AuthenticationState): boolean;
  emit(event: 'message', userId: string, message: any): boolean;
}

/**
 * Result of starting a session
 */
export interface StartSessionResult {
  sessionId: string;
  qrCode?: string;
  status: SessionStatus;
  phoneNumber?: string;
}

/**
 * Disconnect reason enumeration
 */
export interface DisconnectReason {
  reason: 'connection_closed' | 'connection_lost' | 'connection_replaced' | 'logged_out' | 'restart_required' | 'timed_out';
  message?: string;
}

/**
 * WhatsApp connection configuration
 */
export interface WhatsAppConnectionConfig {
  userId: string;
  deviceName?: string;
  browserName?: string;
  qrTimeoutMs?: number;
  reconnectAttempts?: number;
  reconnectDelayMs?: number;
  enableLogging?: boolean;
}

/**
 * Session connection state
 */
export interface SessionConnectionState {
  userId: string;
  status: SessionStatus;
  phoneNumber?: string;
  connectedAt?: Date;
  lastActivity?: Date;
  qrCode?: string;
  qrExpiresAt?: Date;
  reconnectAttempts: number;
  lastError?: string;
}

/**
 * WhatsApp message interface (simplified)
 */
export interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  type: 'text' | 'image' | 'audio' | 'video' | 'document';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Connection manager interface for handling multiple Baileys instances
 */
export interface IConnectionManager {
  /**
   * Create a new Baileys connection
   */
  createConnection(config: WhatsAppConnectionConfig): Promise<BaileysConnection>;

  /**
   * Get existing connection
   */
  getConnection(userId: string): BaileysConnection | null;

  /**
   * Close and remove connection
   */
  closeConnection(userId: string): Promise<void>;

  /**
   * Get all active connections
   */
  getAllConnections(): Map<string, BaileysConnection>;

  /**
   * Close all connections
   */
  closeAllConnections(): Promise<void>;

  /**
   * Get connection count
   */
  getConnectionCount(): number;
}

/**
 * Baileys connection wrapper interface
 */
export interface BaileysConnection {
  userId: string;
  socket: any; // Baileys Socket
  status: SessionStatus;
  phoneNumber?: string;
  qrCode?: string;
  lastActivity: Date;
  reconnectAttempts: number;
  
  // Connection methods
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  reconnect(): Promise<void>;
  generateQR(): Promise<string>;
  
  // Message methods
  sendMessage(to: string, message: any): Promise<void>;
  
  // Event handling
  on(event: string, listener: (...args: any[]) => void): void;
  off(event: string, listener: (...args: any[]) => void): void;
  emit(event: string, ...args: any[]): boolean;
}

/**
 * Auth state adapter interface for Baileys
 */
export interface IAuthStateAdapter {
  /**
   * Load authentication state from storage
   */
  loadAuthState(userId: string): Promise<AuthenticationState | null>;

  /**
   * Save authentication state to storage
   */
  saveAuthState(userId: string, authState: AuthenticationState): Promise<void>;

  /**
   * Clear authentication state
   */
  clearAuthState(userId: string): Promise<void>;

  /**
   * Check if auth state exists
   */
  hasAuthState(userId: string): Promise<boolean>;
}

/**
 * QR code service interface
 */
export interface IQRCodeService {
  /**
   * Generate QR code as base64 data URL
   */
  generateQRCode(data: string): Promise<string>;

  /**
   * Generate QR code as SVG
   */
  generateQRCodeSVG(data: string): Promise<string>;

  /**
   * Validate QR code data
   */
  validateQRData(data: string): boolean;
}

/**
 * Session event types
 */
export type SessionEventType = 
  | 'session_created'
  | 'qr_generated' 
  | 'qr_scanned'
  | 'connecting'
  | 'connected'
  | 'disconnected'
  | 'error'
  | 'auth_updated'
  | 'message_received'
  | 'message_sent';

/**
 * Session event interface
 */
export interface SessionEvent {
  type: SessionEventType;
  userId: string;
  sessionId: string;
  timestamp: Date;
  data?: any;
  metadata?: Record<string, any>;
}

/**
 * Session event handler interface
 */
export interface ISessionEventHandler {
  /**
   * Handle session events
   */
  handleEvent(event: SessionEvent): Promise<void>;

  /**
   * Subscribe to specific event types
   */
  subscribe(eventType: SessionEventType, handler: (event: SessionEvent) => Promise<void>): void;

  /**
   * Unsubscribe from event types
   */
  unsubscribe(eventType: SessionEventType, handler: (event: SessionEvent) => Promise<void>): void;

  /**
   * Get event history for a session
   */
  getEventHistory(userId: string, limit?: number): Promise<SessionEvent[]>;
}
