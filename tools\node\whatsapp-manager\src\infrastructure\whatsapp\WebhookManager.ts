import { inject, injectable } from 'tsyringe';
import axios, { AxiosInstance, AxiosResponse } from 'axios';

import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';
import { WhatsAppMessage } from '../../domain/entities/WhatsAppMessage';
import { RateLimiter } from './RateLimiter';

/**
 * Webhook configuration
 */
export interface WebhookConfig {
  url: string;
  secret?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enabledEvents: string[];
  headers?: Record<string, string>;
}

/**
 * Webhook payload
 */
export interface WebhookPayload {
  event: string;
  timestamp: string;
  userId: string;
  sessionId: string;
  data: any;
  signature?: string;
}

/**
 * Webhook delivery result
 */
export interface WebhookDeliveryResult {
  success: boolean;
  statusCode?: number;
  responseTime: number;
  error?: string;
  attempt: number;
}

/**
 * Webhook manager for delivering events to external systems
 * Handles webhook delivery with retries, rate limiting, and monitoring
 */
@injectable()
export class WebhookManager {
  private readonly httpClient: AxiosInstance;
  private readonly webhookConfigs = new Map<string, WebhookConfig>();
  private readonly deliveryQueue: Array<{
    userId: string;
    payload: WebhookPayload;
    config: WebhookConfig;
    attempts: number;
    nextRetry: Date;
  }> = [];
  private readonly processingInterval: NodeJS.Timeout;
  private readonly maxQueueSize: number;

  constructor(
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService,
    @inject('RateLimiter') private rateLimiter: RateLimiter
  ) {
    this.maxQueueSize = this.config.getOptional('WEBHOOK_MAX_QUEUE_SIZE', 10000);

    // Create HTTP client with default configuration
    this.httpClient = axios.create({
      timeout: this.config.getOptional('WEBHOOK_DEFAULT_TIMEOUT_MS', 30000),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WhatsApp-Manager-Webhook/1.0'
      }
    });

    // Start processing queue
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, this.config.getOptional('WEBHOOK_PROCESSING_INTERVAL_MS', 5000));

    this.logger.info('WebhookManager initialized', {
      maxQueueSize: this.maxQueueSize
    });
  }

  /**
   * Register webhook configuration for user
   */
  registerWebhook(userId: string, config: WebhookConfig): void {
    try {
      // Validate webhook configuration
      this.validateWebhookConfig(config);

      this.webhookConfigs.set(userId, config);

      this.logger.info('Webhook registered', {
        userId,
        url: config.url,
        enabledEvents: config.enabledEvents,
        timeout: config.timeout
      });

    } catch (error) {
      this.logger.error('Failed to register webhook', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Unregister webhook for user
   */
  unregisterWebhook(userId: string): boolean {
    const existed = this.webhookConfigs.delete(userId);
    
    if (existed) {
      // Remove pending deliveries for this user
      const initialLength = this.deliveryQueue.length;
      for (let i = this.deliveryQueue.length - 1; i >= 0; i--) {
        if (this.deliveryQueue[i].userId === userId) {
          this.deliveryQueue.splice(i, 1);
        }
      }

      this.logger.info('Webhook unregistered', {
        userId,
        removedQueueItems: initialLength - this.deliveryQueue.length
      });
    }

    return existed;
  }

  /**
   * Send webhook for session event
   */
  async sendSessionWebhook(
    userId: string,
    sessionId: string,
    event: string,
    data: any
  ): Promise<void> {
    const config = this.webhookConfigs.get(userId);
    if (!config || !config.enabledEvents.includes(event)) {
      return; // No webhook configured or event not enabled
    }

    const payload: WebhookPayload = {
      event,
      timestamp: new Date().toISOString(),
      userId,
      sessionId,
      data,
      signature: config.secret ? this.generateSignature(data, config.secret) : undefined
    };

    await this.queueWebhook(userId, payload, config);
  }

  /**
   * Send webhook for message event
   */
  async sendMessageWebhook(
    userId: string,
    sessionId: string,
    message: WhatsAppMessage,
    event: 'message.received' | 'message.sent' | 'message.updated'
  ): Promise<void> {
    const config = this.webhookConfigs.get(userId);
    if (!config || !config.enabledEvents.includes(event)) {
      return;
    }

    const payload: WebhookPayload = {
      event,
      timestamp: new Date().toISOString(),
      userId,
      sessionId,
      data: {
        messageId: message.messageId,
        from: message.from,
        to: message.to,
        type: message.type,
        content: message.content,
        timestamp: message.timestamp,
        direction: message.direction,
        status: message.status,
        hasMedia: message.hasMedia(),
        mediaInfo: message.mediaInfo,
        locationInfo: message.locationInfo,
        contactInfo: message.contactInfo
      },
      signature: config.secret ? this.generateSignature(message, config.secret) : undefined
    };

    await this.queueWebhook(userId, payload, config);
  }

  /**
   * Queue webhook for delivery
   */
  private async queueWebhook(
    userId: string,
    payload: WebhookPayload,
    config: WebhookConfig
  ): Promise<void> {
    try {
      // Check queue size limit
      if (this.deliveryQueue.length >= this.maxQueueSize) {
        this.logger.warn('Webhook queue is full, dropping oldest items', {
          queueSize: this.deliveryQueue.length,
          maxSize: this.maxQueueSize
        });
        
        // Remove oldest items (FIFO)
        this.deliveryQueue.splice(0, Math.floor(this.maxQueueSize * 0.1));
      }

      // Add to queue
      this.deliveryQueue.push({
        userId,
        payload,
        config,
        attempts: 0,
        nextRetry: new Date()
      });

      this.logger.debug('Webhook queued for delivery', {
        userId,
        event: payload.event,
        queueSize: this.deliveryQueue.length
      });

    } catch (error) {
      this.logger.error('Failed to queue webhook', {
        userId,
        event: payload.event,
        error: (error as Error).message
      });
    }
  }

  /**
   * Process webhook delivery queue
   */
  private async processQueue(): Promise<void> {
    if (this.deliveryQueue.length === 0) {
      return;
    }

    const now = new Date();
    const itemsToProcess = this.deliveryQueue.filter(item => item.nextRetry <= now);

    if (itemsToProcess.length === 0) {
      return;
    }

    this.logger.debug('Processing webhook queue', {
      totalItems: this.deliveryQueue.length,
      itemsToProcess: itemsToProcess.length
    });

    // Process items in parallel (with concurrency limit)
    const concurrency = this.config.getOptional('WEBHOOK_CONCURRENCY', 5);
    const batches = this.chunkArray(itemsToProcess, concurrency);

    for (const batch of batches) {
      await Promise.allSettled(
        batch.map(item => this.deliverWebhook(item))
      );
    }
  }

  /**
   * Deliver individual webhook
   */
  private async deliverWebhook(item: {
    userId: string;
    payload: WebhookPayload;
    config: WebhookConfig;
    attempts: number;
    nextRetry: Date;
  }): Promise<void> {
    const startTime = Date.now();
    item.attempts++;

    try {
      // Check rate limits
      const rateLimitResult = await this.rateLimiter.checkUserLimit(item.userId, 'webhooks');
      if (!rateLimitResult.allowed) {
        this.logger.warn('Webhook rate limit exceeded', {
          userId: item.userId,
          event: item.payload.event
        });
        
        // Reschedule for later
        item.nextRetry = new Date(Date.now() + 60000); // 1 minute delay
        return;
      }

      // Prepare request
      const headers = {
        ...item.config.headers,
        'X-Webhook-Signature': item.payload.signature
      };

      // Send webhook
      const response: AxiosResponse = await this.httpClient.post(
        item.config.url,
        item.payload,
        {
          headers,
          timeout: item.config.timeout
        }
      );

      const responseTime = Date.now() - startTime;

      // Check if delivery was successful
      if (response.status >= 200 && response.status < 300) {
        // Success - remove from queue
        const index = this.deliveryQueue.indexOf(item);
        if (index !== -1) {
          this.deliveryQueue.splice(index, 1);
        }

        this.logger.info('Webhook delivered successfully', {
          userId: item.userId,
          event: item.payload.event,
          statusCode: response.status,
          responseTime,
          attempt: item.attempts
        });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.logger.warn('Webhook delivery failed', {
        userId: item.userId,
        event: item.payload.event,
        attempt: item.attempts,
        maxAttempts: item.config.retryAttempts,
        responseTime,
        error: (error as Error).message
      });

      // Check if we should retry
      if (item.attempts < item.config.retryAttempts) {
        // Schedule retry with exponential backoff
        const delay = item.config.retryDelay * Math.pow(2, item.attempts - 1);
        item.nextRetry = new Date(Date.now() + delay);
        
        this.logger.debug('Webhook scheduled for retry', {
          userId: item.userId,
          event: item.payload.event,
          nextRetry: item.nextRetry,
          delay
        });
      } else {
        // Max attempts reached - remove from queue
        const index = this.deliveryQueue.indexOf(item);
        if (index !== -1) {
          this.deliveryQueue.splice(index, 1);
        }

        this.logger.error('Webhook delivery failed permanently', {
          userId: item.userId,
          event: item.payload.event,
          totalAttempts: item.attempts
        });
      }
    }
  }

  /**
   * Validate webhook configuration
   */
  private validateWebhookConfig(config: WebhookConfig): void {
    if (!config.url || !config.url.startsWith('http')) {
      throw new Error('Invalid webhook URL');
    }

    if (config.timeout < 1000 || config.timeout > 60000) {
      throw new Error('Webhook timeout must be between 1-60 seconds');
    }

    if (config.retryAttempts < 0 || config.retryAttempts > 10) {
      throw new Error('Retry attempts must be between 0-10');
    }

    if (!Array.isArray(config.enabledEvents) || config.enabledEvents.length === 0) {
      throw new Error('At least one event must be enabled');
    }
  }

  /**
   * Generate webhook signature
   */
  private generateSignature(data: any, secret: string): string {
    const crypto = require('crypto');
    const payload = JSON.stringify(data);
    return crypto.createHmac('sha256', secret).update(payload).digest('hex');
  }

  /**
   * Chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Get webhook statistics
   */
  getStatistics(): {
    registeredWebhooks: number;
    queueSize: number;
    maxQueueSize: number;
    webhookConfigs: Array<{ userId: string; url: string; enabledEvents: string[] }>;
  } {
    return {
      registeredWebhooks: this.webhookConfigs.size,
      queueSize: this.deliveryQueue.length,
      maxQueueSize: this.maxQueueSize,
      webhookConfigs: Array.from(this.webhookConfigs.entries()).map(([userId, config]) => ({
        userId,
        url: config.url,
        enabledEvents: config.enabledEvents
      }))
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    this.webhookConfigs.clear();
    this.deliveryQueue.length = 0;
    
    this.logger.info('WebhookManager destroyed');
  }
}
