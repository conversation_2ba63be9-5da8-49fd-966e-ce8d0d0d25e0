# AWS Application Load Balancer (ALB) Terraform Module

This module creates an AWS Application Load Balancer with configurable settings and sensible defaults.

## Features

- Creates an Application Load Balancer with environment and application name-based naming
- Configurable internal/external facing
- Optional HTTP and HTTPS listeners
- Default security group creation with configurable CIDR blocks
- Default target group with customizable health check settings
- Support for access logs
- Comprehensive tagging support

## Usage

### Basic Usage

```hcl
module "web_alb" {
  source   = "../../modules/alb"
  env      = "prod"
  app_name = "web"
  
  # All other settings use defaults
}
```

### External ALB with HTTPS

```hcl
module "api_alb" {
  source         = "../../modules/alb"
  env            = "prod"
  app_name       = "api"
  internal       = false
  certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/abcd1234-ef56-gh78-ij90-klmn1234pqrs"
  
  tags = {
    Service     = "API Gateway"
    Environment = "Production"
  }
}
```

### Internal ALB with Custom VPC and Subnets

```hcl
module "internal_alb" {
  source     = "../../modules/alb"
  env        = "dev"
  app_name   = "backend"
  internal   = true
  vpc_id     = "vpc-12345678"
  subnet_ids = ["subnet-12345678", "subnet-87654321"]
  
  # Custom security group
  security_group_ids = ["sg-12345678"]
  
  # Custom target group settings
  target_group_port     = 8080
  target_group_protocol = "HTTP"
  health_check_path     = "/health"
  
  tags = {
    Environment = "Development"
    ManagedBy   = "Terraform"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | >= 5.0.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| env | Environment name (e.g., dev, staging, prod) | `string` | n/a | yes |
| app_name | Application name | `string` | n/a | yes |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |
| internal | If true, the LB will be internal | `bool` | `false` | no |
| vpc_id | VPC ID where the load balancer will be deployed | `string` | `null` | no |
| subnet_ids | A list of subnet IDs to attach to the LB | `list(string)` | `null` | no |
| security_group_ids | A list of security group IDs to assign to the LB | `list(string)` | `null` | no |
| allowed_cidr_blocks | List of CIDR blocks to allow in the ALB security group | `list(string)` | `["0.0.0.0/0"]` | no |
| enable_deletion_protection | If true, deletion of the load balancer will be disabled via the AWS API | `bool` | `false` | no |
| idle_timeout | The time in seconds that the connection is allowed to be idle | `number` | `60` | no |
| drop_invalid_header_fields | Indicates whether HTTP headers with header fields that are not valid are removed by the load balancer | `bool` | `true` | no |
| enable_http2 | Indicates whether HTTP/2 is enabled in the load balancer | `bool` | `true` | no |
| access_logs_bucket | The S3 bucket name to store the logs in | `string` | `null` | no |
| access_logs_prefix | The S3 bucket prefix. Logs are stored in the root if not configured. | `string` | `null` | no |
| create_http_listener | Whether to create HTTP listener | `bool` | `true` | no |
| http_listener_type | The type of routing action for HTTP listener | `string` | `"redirect"` | no |
| fixed_response_content | The content for fixed-response action | `string` | `"OK"` | no |
| create_https_listener | Whether to create HTTPS listener | `bool` | `true` | no |
| certificate_arn | The ARN of the default SSL server certificate | `string` | `null` | no |
| ssl_policy | The name of the SSL Policy for the listener | `string` | `"ELBSecurityPolicy-2016-08"` | no |
| default_target_group_arn | The ARN of the default target group | `string` | `null` | no |
| create_default_target_group | Whether to create default target group | `bool` | `true` | no |
| target_group_port | Port for the default target group | `number` | `80` | no |
| target_group_protocol | Protocol for the default target group | `string` | `"HTTP"` | no |
| target_type | Type of target that you must specify when registering targets | `string` | `"instance"` | no |
| health_check_interval | Approximate amount of time, in seconds, between health checks | `number` | `30` | no |
| health_check_path | Destination for the health check request | `string` | `"/"` | no |
| health_check_port | Port to use to connect with the target | `string` | `"traffic-port"` | no |
| health_check_protocol | Protocol to use to connect with the target | `string` | `"HTTP"` | no |
| health_check_timeout | Amount of time, in seconds, during which no response means a failed health check | `number` | `5` | no |
| health_check_healthy_threshold | Number of consecutive health checks successes required before considering an unhealthy target healthy | `number` | `3` | no |
| health_check_unhealthy_threshold | Number of consecutive health check failures required before considering the target unhealthy | `number` | `3` | no |
| health_check_matcher | HTTP codes to use when checking for a successful response from a target | `string` | `"200-299"` | no |

## Outputs

| Name | Description |
|------|-------------|
| alb_id | The ID of the load balancer |
| alb_arn | The ARN of the load balancer |
| alb_dns_name | The DNS name of the load balancer |
| alb_zone_id | The canonical hosted zone ID of the load balancer |
| http_listener_arn | The ARN of the HTTP listener |
| https_listener_arn | The ARN of the HTTPS listener |
| default_target_group_arn | The ARN of the default target group |
| security_group_id | The ID of the security group created for the load balancer |
| alb_name | The name of the load balancer |
