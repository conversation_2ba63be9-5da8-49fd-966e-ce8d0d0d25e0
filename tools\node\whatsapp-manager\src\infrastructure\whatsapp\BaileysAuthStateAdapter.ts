import { inject, injectable } from 'tsyringe';
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';
import {
  AuthenticationCreds,
  SignalDataTypeMap,
  AuthenticationState,
  initAuthCreds
} from '@whiskeysockets/baileys';

import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { IAuthStateAdapter } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';
import { IConfigService } from '../../shared/config/interfaces';

/**
 * Enhanced Baileys authentication state adapter with encryption
 * Implements the Baileys auth state interface using our session repository
 */
@injectable()
export class BaileysAuthStateAdapter implements IAuthStateAdapter {
  private readonly scryptAsync = promisify(scrypt);
  private readonly encryptionKey: string;
  private readonly algorithm = 'aes-256-gcm';

  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('ILoggerService') private logger: ILoggerService,
    @inject('IConfigService') private config: IConfigService
  ) {
    this.encryptionKey = this.config.get('ENCRYPTION_KEY');
    if (!this.encryptionKey) {
      throw new Error('ENCRYPTION_KEY environment variable is required for auth state encryption');
    }
  }

  /**
   * Encrypt sensitive data
   */
  private async encrypt(data: string): Promise<string> {
    try {
      const salt = randomBytes(16);
      const key = (await this.scryptAsync(this.encryptionKey, salt, 32)) as Buffer;
      const iv = randomBytes(16);

      const cipher = createCipheriv(this.algorithm, key, iv);
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag();

      // Combine salt, iv, authTag, and encrypted data
      const combined = salt.toString('hex') + ':' + iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
      return combined;
    } catch (error) {
      this.logger.error('Encryption failed', { error: (error as Error).message });
      throw new Error('Failed to encrypt auth state data');
    }
  }

  /**
   * Create authentication credentials using Baileys
   */
  private createAuthCreds(): AuthenticationCreds {
    return initAuthCreds();
  }

  /**
   * Decrypt sensitive data
   */
  private async decrypt(encryptedData: string): Promise<string> {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 4) {
        throw new Error('Invalid encrypted data format');
      }

      const salt = Buffer.from(parts[0], 'hex');
      const iv = Buffer.from(parts[1], 'hex');
      const authTag = Buffer.from(parts[2], 'hex');
      const encrypted = parts[3];

      const key = (await this.scryptAsync(this.encryptionKey, salt, 32)) as Buffer;

      const decipher = createDecipheriv(this.algorithm, key, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Decryption failed', { error: (error as Error).message });
      throw new Error('Failed to decrypt auth state data');
    }
  }

  /**
   * Load authentication state from storage with decryption
   */
  async loadAuthState(userId: string): Promise<AuthenticationState | null> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session || !session.authState) {
        return null;
      }

      // If authState is a string, it's encrypted
      if (typeof session.authState === 'string') {
        const decryptedData = await this.decrypt(session.authState);
        return JSON.parse(decryptedData);
      }

      // Legacy unencrypted data
      return session.authState;
    } catch (error) {
      this.logger.error('Failed to load auth state', {
        userId,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Save authentication state to storage with encryption
   */
  async saveAuthState(userId: string, authState: AuthenticationState): Promise<void> {
    try {
      // Encrypt the auth state before storing
      const serializedAuthState = JSON.stringify(authState);
      const encryptedAuthState = await this.encrypt(serializedAuthState);

      await this.sessionRepository.updateAuthState(userId, encryptedAuthState as any);
      this.logger.debug('Auth state saved and encrypted', { userId });
    } catch (error) {
      this.logger.error('Failed to save auth state', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState(userId: string): Promise<void> {
    try {
      await this.sessionRepository.updateAuthState(userId, null as any);
      this.logger.debug('Auth state cleared', { userId });
    } catch (error) {
      this.logger.error('Failed to clear auth state', {
        userId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Check if auth state exists
   */
  async hasAuthState(userId: string): Promise<boolean> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      return !!(session && session.authState);
    } catch (error) {
      this.logger.error('Failed to check auth state existence', {
        userId,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Create Baileys-compatible auth state object with real functionality
   */
  async createBaileysAuthState(userId: string): Promise<{
    state: AuthenticationState;
    saveCreds: () => Promise<void>;
    saveState: () => Promise<void>;
  }> {
    // Load existing auth state or create new one
    let authState = await this.loadAuthState(userId);

    if (!authState) {
      // For new users, start with minimal auth state for QR generation
      authState = {
        creds: this.createAuthCreds(),
        keys: this.createBaileysKeyStore(userId)
      };

      this.logger.info('Fresh auth state created for QR generation', { 
        userId,
        hasRegistrationId: !!(authState.creds as any).registrationId,
        hasSignedIdentityKey: !!(authState.creds as any).signedIdentityKey
      });
    } else {
      // Ensure keys store is properly initialized for existing auth state
      if (!authState.keys || typeof (authState.keys as any).get !== 'function') {
        authState.keys = this.createBaileysKeyStore(userId);
        this.logger.info('Auth state keys store reinitialized', { userId });
      }
    }

    // Create save functions for Baileys
    const saveCreds = async () => {
      try {
        await this.saveAuthState(userId, authState!);
        this.logger.debug('Credentials saved', { userId });
      } catch (error) {
        this.logger.error('Failed to save credentials', {
          userId,
          error: (error as Error).message
        });
        throw error;
      }
    };

    const saveState = async () => {
      try {
        await this.saveAuthState(userId, authState!);
        this.logger.debug('State saved', { userId });
      } catch (error) {
        this.logger.error('Failed to save state', {
          userId,
          error: (error as Error).message
        });
        throw error;
      }
    };

    return {
      state: authState,
      saveCreds,
      saveState
    };
  }

  /**
   * Create Baileys-compatible key store with persistent storage
   */
  private createBaileysKeyStore(userId: string): any {
    const keys: { [key: string]: any } = {};

    return {
      get: async (type: keyof SignalDataTypeMap, ids: string[]) => {
        try {
          const data: { [id: string]: any } = {};

          for (const id of ids) {
            const key = `${String(type)}:${String(id)}`;
            if (keys[key]) {
              data[id] = keys[key];
            }
          }

          this.logger.debug('Keys retrieved', {
            userId,
            type: String(type),
            count: Object.keys(data).length
          });

          return data;
        } catch (error) {
          this.logger.error('Failed to get keys', {
            userId,
            type: String(type),
            error: (error as Error).message
          });
          return {};
        }
      },

      set: async (data: any) => {
        try {
          let keysUpdated = 0;

          for (const category in data) {
            if (Object.prototype.hasOwnProperty.call(data, category)) {
              for (const id in data[category]) {
                if (Object.prototype.hasOwnProperty.call(data[category], id)) {
                  const key = `${String(category)}:${String(id)}`;
                  const value = data[category][id];

                  // Handle Buffer serialization
                  if (value && typeof value === 'object' && value.type === 'Buffer') {
                    keys[key] = Buffer.from(value.data);
                  } else {
                    keys[key] = value;
                  }

                  keysUpdated++;
                }
              }
            }
          }

          this.logger.debug('Keys updated', {
            userId,
            keysUpdated
          });

          // Trigger auth state save to persist key changes
          const currentAuthState = await this.loadAuthState(userId);
          if (currentAuthState) {
            // Keys are already updated in the store, no need to reassign
            await this.saveAuthState(userId, currentAuthState);
          }
        } catch (error) {
          this.logger.error('Failed to set keys', {
            userId,
            error: (error as Error).message
          });
          throw error;
        }
      }
    };
  }

  /**
   * Serialize auth state for storage
   */
  static serializeAuthState(authState: AuthenticationState): string {
    return JSON.stringify(authState);
  }

  /**
   * Deserialize auth state from storage
   */
  static deserializeAuthState(serialized: string): AuthenticationState {
    return JSON.parse(serialized);
  }

  /**
   * Validate auth state integrity
   */
  validateAuthState(authState: AuthenticationState): boolean {
    try {
      if (!authState || typeof authState !== 'object') {
        return false;
      }

      if (!authState.creds || typeof authState.creds !== 'object') {
        return false;
      }

      if (!authState.keys || typeof authState.keys !== 'object') {
        return false;
      }

      // Basic validation for mock auth state
      if (!authState.creds || typeof authState.creds !== 'object') {
        this.logger.warn('Invalid credentials in auth state');
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Auth state validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Create a backup of auth state
   */
  async backupAuthState(userId: string): Promise<string | null> {
    try {
      const authState = await this.loadAuthState(userId);
      if (!authState) {
        return null;
      }

      return BaileysAuthStateAdapter.serializeAuthState(authState);
    } catch (error) {
      this.logger.error('Failed to backup auth state', {
        userId,
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Restore auth state from backup
   */
  async restoreAuthState(userId: string, backup: string): Promise<boolean> {
    try {
      const authState = BaileysAuthStateAdapter.deserializeAuthState(backup);
      
      if (!this.validateAuthState(authState)) {
        this.logger.error('Invalid auth state in backup', { userId });
        return false;
      }

      await this.saveAuthState(userId, authState);
      this.logger.info('Auth state restored from backup', { userId });
      return true;
    } catch (error) {
      this.logger.error('Failed to restore auth state from backup', {
        userId,
        error: (error as Error).message
      });
      return false;
    }
  }

  /**
   * Get auth state metadata
   */
  async getAuthStateMetadata(userId: string): Promise<{
    exists: boolean;
    createdAt?: Date;
    lastUpdated?: Date;
    phoneNumber?: string;
    deviceId?: string;
  }> {
    try {
      const session = await this.sessionRepository.findByUserId(userId);
      
      if (!session || !session.authState) {
        return { exists: false };
      }

      return {
        exists: true,
        createdAt: session.createdAt,
        lastUpdated: session.updatedAt,
        phoneNumber: session.phoneNumber || undefined,
        deviceId: session.authState.creds?.registrationId?.toString()
      };
    } catch (error) {
      this.logger.error('Failed to get auth state metadata', {
        userId,
        error: (error as Error).message
      });
      return { exists: false };
    }
  }



  /**
   * Clean up expired auth states
   */
  async cleanupExpiredAuthStates(): Promise<number> {
    try {
      const expiredSessions = await this.sessionRepository.findExpiredSessions();
      let cleanedCount = 0;

      for (const session of expiredSessions) {
        try {
          await this.clearAuthState(session.userId);
          cleanedCount++;
        } catch (error) {
          this.logger.warn('Failed to cleanup auth state for expired session', {
            userId: session.userId,
            error: (error as Error).message
          });
        }
      }

      this.logger.info('Auth state cleanup completed', {
        expiredSessions: expiredSessions.length,
        cleanedCount
      });

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired auth states', {
        error: (error as Error).message
      });
      return 0;
    }
  }
}
