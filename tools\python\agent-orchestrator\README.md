# Agent Orchestrator

A Python-based service that orchestrates AI agents, handles context management, and processes various document formats for the ezychat platform.

## Purpose

The Agent Orchestrator serves as the central intelligence hub that:
- Processes incoming messages and determines appropriate responses
- Manages conversation context and memory
- Handles document parsing (PDF, images, text)
- Interfaces with various AI models and services
- Coordinates with other tools in the workflow

## Key Features

- **Document Processing**: PDF parsing, image analysis, text extraction
- **Vector Embeddings**: Generate and manage embeddings for semantic search
- **Context Management**: Maintain conversation history and context
- **Agent Coordination**: Route requests to appropriate specialized agents
- **Memory Management**: Store and retrieve relevant conversation context

## API Endpoints

### Core Endpoints
- `POST /process` - Main endpoint for processing messages
- `POST /parse/pdf` - Parse PDF documents
- `POST /parse/image` - Process image content
- `POST /embeddings` - Generate vector embeddings
- `GET /context/{conversation_id}` - Retrieve conversation context
- `GET /health` - Health check

### Request/Response Examples

#### Process Message
```python
# Request
{
    "conversation_id": "conv_123",
    "message": "Can you analyze this document?",
    "attachments": [
        {
            "type": "pdf",
            "url": "https://example.com/doc.pdf"
        }
    ],
    "context": {
        "user_id": "user_456",
        "channel": "whatsapp"
    }
}

# Response
{
    "response": "I've analyzed the document. Here are the key points...",
    "actions": [
        {
            "type": "send_message",
            "target": "whatsapp-manager",
            "payload": {...}
        }
    ],
    "context_updated": true
}
```

## Dependencies

- FastAPI for web framework
- LangChain for agent coordination
- PyPDF2/pdfplumber for PDF processing
- Pillow for image processing
- OpenAI/Anthropic SDKs for AI models
- ChromaDB/Pinecone for vector storage
- Redis for caching and session management

## Environment Variables

- `OPENAI_API_KEY` - OpenAI API key
- `ANTHROPIC_API_KEY` - Anthropic API key
- `VECTOR_DB_URL` - Vector database connection string
- `REDIS_URL` - Redis connection string
- `PORT` - Service port (default: 8000)

## Development

```bash
# Setup virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --port 8000
```

## Testing

```bash
# Run tests
pytest tests/

# Run with coverage
pytest --cov=src tests/
```

## Deployment

This service is containerized and deployed to ECS. See `/infra/terraform/ecs/` for deployment configuration.