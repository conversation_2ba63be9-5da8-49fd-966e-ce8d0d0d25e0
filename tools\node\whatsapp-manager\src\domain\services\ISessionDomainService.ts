import { SessionEntity, SessionStatus } from '../entities/SessionEntity';

/**
 * Session domain service interface
 * Contains business logic that doesn't belong to a single entity
 */
export interface ISessionDomainService {
  /**
   * Validate if a new session can be created for the user
   */
  validateSessionCreation(userId: string): Promise<void>;

  /**
   * Calculate TTL timestamp for session expiration
   */
  calculateTTL(createdAt: Date, ttlHours?: number): number;

  /**
   * Check if an existing session can be reused
   */
  isSessionReusable(session: SessionEntity): boolean;

  /**
   * Determine if a new session can be created given existing sessions
   */
  canCreateNewSession(userId: string, existingSessions: SessionEntity[]): boolean;

  /**
   * Calculate session health score based on various factors
   */
  calculateSessionHealth(session: SessionEntity): SessionHealthScore;

  /**
   * Determine if a session should be automatically cleaned up
   */
  shouldCleanupSession(session: SessionEntity): boolean;

  /**
   * Get recommended action for a session based on its state
   */
  getRecommendedAction(session: SessionEntity): SessionAction;

  /**
   * Validate session transition from one status to another
   */
  validateStatusTransition(currentStatus: SessionStatus, newStatus: SessionStatus): boolean;

  /**
   * Calculate optimal reconnection delay based on failure history
   */
  calculateReconnectionDelay(failureCount: number): number;
}

/**
 * Session health score interface
 */
export interface SessionHealthScore {
  score: number; // 0-100
  factors: {
    uptime: number;
    stability: number;
    authState: number;
    lastActivity: number;
  };
  issues: string[];
  recommendations: string[];
}

/**
 * Recommended actions for session management
 */
export type SessionAction = 
  | 'maintain' 
  | 'reconnect' 
  | 'cleanup' 
  | 'refresh_auth' 
  | 'terminate' 
  | 'monitor';

/**
 * Session validation rules interface
 */
export interface ISessionValidationService {
  /**
   * Validate user ID format and constraints
   */
  validateUserId(userId: string): ValidationResult;

  /**
   * Validate session configuration
   */
  validateSessionConfig(config: SessionConfig): ValidationResult;

  /**
   * Validate authentication state integrity
   */
  validateAuthState(authState: any): ValidationResult;

  /**
   * Validate phone number format
   */
  validatePhoneNumber(phoneNumber: string): ValidationResult;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Session configuration interface
 */
export interface SessionConfig {
  userId: string;
  deviceName?: string;
  browserName?: string;
  maxRetries?: number;
  timeoutSeconds?: number;
  enableQRRefresh?: boolean;
}

/**
 * Session metrics service interface
 */
export interface ISessionMetricsService {
  /**
   * Record session creation event
   */
  recordSessionCreated(userId: string, sessionId: string): void;

  /**
   * Record session connection event
   */
  recordSessionConnected(userId: string, sessionId: string, duration: number): void;

  /**
   * Record session disconnection event
   */
  recordSessionDisconnected(userId: string, sessionId: string, reason: string): void;

  /**
   * Record authentication event
   */
  recordAuthEvent(userId: string, sessionId: string, success: boolean): void;

  /**
   * Record QR code generation event
   */
  recordQRGenerated(userId: string, sessionId: string): void;

  /**
   * Get session metrics for a user
   */
  getUserMetrics(userId: string): Promise<UserSessionMetrics>;

  /**
   * Get overall system metrics
   */
  getSystemMetrics(): Promise<SystemSessionMetrics>;
}

/**
 * User session metrics interface
 */
export interface UserSessionMetrics {
  userId: string;
  totalSessions: number;
  successfulConnections: number;
  failedConnections: number;
  averageSessionDuration: number;
  totalUptime: number;
  lastActivity: Date;
  qrGenerationCount: number;
}

/**
 * System session metrics interface
 */
export interface SystemSessionMetrics {
  totalUsers: number;
  activeSessions: number;
  totalSessions: number;
  averageSessionsPerUser: number;
  systemUptime: number;
  successRate: number;
  averageConnectionTime: number;
  peakConcurrentSessions: number;
  qrGenerationRate: number;
}

/**
 * Session lifecycle service interface
 */
export interface ISessionLifecycleService {
  /**
   * Handle session initialization
   */
  initializeSession(userId: string, config: SessionConfig): Promise<SessionEntity>;

  /**
   * Handle session connection
   */
  handleSessionConnection(sessionId: string, phoneNumber: string): Promise<void>;

  /**
   * Handle session disconnection
   */
  handleSessionDisconnection(sessionId: string, reason: string): Promise<void>;

  /**
   * Handle session error
   */
  handleSessionError(sessionId: string, error: Error): Promise<void>;

  /**
   * Handle session cleanup
   */
  handleSessionCleanup(sessionId: string): Promise<void>;

  /**
   * Get session lifecycle events
   */
  getLifecycleEvents(sessionId: string): Promise<SessionLifecycleEvent[]>;
}

/**
 * Session lifecycle event interface
 */
export interface SessionLifecycleEvent {
  sessionId: string;
  userId: string;
  event: 'created' | 'connected' | 'disconnected' | 'error' | 'cleanup';
  timestamp: Date;
  data?: any;
  metadata?: Record<string, any>;
}
