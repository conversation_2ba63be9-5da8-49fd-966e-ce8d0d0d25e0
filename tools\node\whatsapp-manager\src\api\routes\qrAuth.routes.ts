import { Router } from 'express';
import { container } from 'tsyringe';
import { Qr<PERSON>uthController } from '../controllers/QrAuthController';
import { rateLimiter } from '../middleware/rateLimiter';

const router = Router();

/**
 * QR Authentication Routes
 * These routes handle the frontend QR authentication flow
 */

/**
 * @route GET /auth/qr
 * @description Handle QR authentication from scanned QR codes
 * @access Public
 * @param {string} token - JWT token from QR code query parameter
 * @returns {Object} Authentication result with token validation status
 * 
 * @example
 * GET /auth/qr?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * 
 * Success Response (200):
 * {
 *   "success": true,
 *   "message": "QR authentication successful",
 *   "data": {
 *     "tokenId": "qr_1234567890_abcdef",
 *     "userId": "user123",
 *     "expiresAt": "2024-01-15T10:35:00.000Z",
 *     "expiresInSeconds": 240,
 *     "createdAt": "2024-01-15T10:30:00.000Z",
 *     "metadata": { "purpose": "qr_auth" },
 *     "instructions": {
 *       "message": "Authentication token is valid. Use the /use endpoint to mark it as used.",
 *       "useEndpoint": "/api/auth/qr-link/qr_1234567890_abcdef/use",
 *       "method": "POST"
 *     }
 *   }
 * }
 * 
 * Error Responses:
 * - 400: Invalid token parameter
 * - 401: Invalid or expired JWT token
 * - 404: Token not found in database
 * - 409: Token already used
 * - 410: Token expired
 */
router.get('/qr', 
  rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Allow 100 QR scans per 15 minutes per IP
    message: 'Too many QR authentication attempts. Please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  }),
  (req, res) => {
    const qrAuthController = container.resolve(QrAuthController);
    return qrAuthController.handleQrAuth(req, res);
  }
);

/**
 * @route GET /auth/qr/:tokenId
 * @description Get QR authentication details by token ID
 * @access Public
 * @param {string} tokenId - Authentication token ID
 * @returns {Object} Token details with QR auth URL
 * 
 * @example
 * GET /auth/qr/qr_1234567890_abcdef
 * 
 * Success Response (200):
 * {
 *   "success": true,
 *   "message": "Authentication token details retrieved successfully",
 *   "data": {
 *     "tokenId": "qr_1234567890_abcdef",
 *     "userId": "user123",
 *     "qrAuthUrl": "http://localhost:3000/auth/qr?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "expiresAt": "2024-01-15T10:35:00.000Z",
 *     "expiresInSeconds": 240,
 *     "createdAt": "2024-01-15T10:30:00.000Z",
 *     "isExpired": false,
 *     "isUsed": false,
 *     "metadata": { "purpose": "qr_auth" },
 *     "instructions": {
 *       "message": "Use this URL in a QR code or access it directly for authentication.",
 *       "useEndpoint": "/api/auth/qr-link/qr_1234567890_abcdef/use",
 *       "method": "POST"
 *     }
 *   }
 * }
 * 
 * Error Responses:
 * - 400: Invalid token ID
 * - 404: Token not found
 * - 409: Token already used
 * - 410: Token expired
 */
router.get('/qr/:tokenId',
  rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50, // Allow 50 token detail requests per 15 minutes per IP
    message: 'Too many token detail requests. Please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  }),
  (req, res) => {
    const qrAuthController = container.resolve(QrAuthController);
    return qrAuthController.handleQrAuthByTokenId(req, res);
  }
);

/**
 * @route POST /auth/qr/validate
 * @description Validate QR authentication token from request body
 * @access Public
 * @body {string} token - JWT token to validate
 * @returns {Object} Token validation result
 * 
 * @example
 * POST /auth/qr/validate
 * Content-Type: application/json
 * {
 *   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 * 
 * Success Response (200):
 * {
 *   "success": true,
 *   "message": "Authentication token is valid",
 *   "data": {
 *     "tokenId": "qr_1234567890_abcdef",
 *     "userId": "user123",
 *     "expiresAt": "2024-01-15T10:35:00.000Z",
 *     "expiresInSeconds": 240,
 *     "isValid": true,
 *     "metadata": { "purpose": "qr_auth" }
 *   }
 * }
 * 
 * Error Responses:
 * - 400: Invalid token in request body
 * - 401: Invalid or expired JWT token
 * - 404: Token not found
 * - 409: Token already used
 * - 410: Token expired
 */
router.post('/qr/validate',
  rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 30, // Allow 30 validation requests per 15 minutes per IP
    message: 'Too many token validation attempts. Please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  }),
  (req, res) => {
    const qrAuthController = container.resolve(QrAuthController);
    return qrAuthController.validateQrAuthToken(req, res);
  }
);

export { router as qrAuthRoutes };
