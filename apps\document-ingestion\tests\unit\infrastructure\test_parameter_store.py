"""
Unit tests for AWS SSM Parameter Store integration
"""

import json
import os
from unittest.mock import Mock, patch

from botocore.exceptions import ClientError, NoCredentialsError

from src.infrastructure.parameter_store import (
    ParameterStoreManager,
    get_all_parameters,
    get_all_secrets,
    get_openai_api_key,
    get_supabase_anon_key,
    get_supabase_service_role_key,
    get_supabase_url,
    parameter_store_manager,
    secrets_manager,
)


class TestParameterStoreManager:
    """Test ParameterStoreManager class"""

    def setup_method(self):
        """Setup for each test method"""
        self.manager = ParameterStoreManager()
        self.manager.clear_cache()

    def test_init(self):
        """Test ParameterStoreManager initialization"""
        manager = ParameterStoreManager()
        assert manager._client is None
        assert manager._cache == {}
        assert isinstance(manager._is_aws_environment, bool)

    def test_detect_aws_environment_lambda(self):
        """Test AWS environment detection for Lambda"""
        with patch.dict(os.environ, {"AWS_LAMBDA_FUNCTION_NAME": "test-function"}):
            manager = ParameterStoreManager()
            assert manager._is_aws_environment is True

    def test_detect_aws_environment_execution_env(self):
        """Test AWS environment detection for execution environment"""
        with patch.dict(os.environ, {"AWS_EXECUTION_ENV": "AWS_Lambda_python3.9"}):
            manager = ParameterStoreManager()
            assert manager._is_aws_environment is True

    def test_detect_aws_environment_credentials(self):
        """Test AWS environment detection with explicit credentials"""
        with patch.dict(
            os.environ, {"AWS_ACCESS_KEY_ID": "test-key", "AWS_SECRET_ACCESS_KEY": "test-secret"}
        ):
            manager = ParameterStoreManager()
            assert manager._is_aws_environment is True

    def test_detect_aws_environment_localstack(self):
        """Test AWS environment detection for LocalStack"""
        with patch.dict(os.environ, {"AWS_ENDPOINT_URL": "http://localhost:4566"}):
            manager = ParameterStoreManager()
            assert manager._is_aws_environment is True

    def test_detect_aws_environment_false(self):
        """Test AWS environment detection returns False when no AWS indicators"""
        # Clear any AWS-related environment variables
        env_vars_to_clear = [
            "AWS_LAMBDA_FUNCTION_NAME",
            "AWS_EXECUTION_ENV",
            "AWS_ACCESS_KEY_ID",
            "AWS_SECRET_ACCESS_KEY",
            "AWS_ENDPOINT_URL",
        ]
        # Create a clean environment without AWS variables
        clean_env = {k: v for k, v in os.environ.items() if k not in env_vars_to_clear}
        with patch.dict(os.environ, clean_env, clear=True):
            manager = ParameterStoreManager()
            assert manager._is_aws_environment is False

    @patch("boto3.client")
    def test_client_initialization_aws(self, mock_boto3_client):
        """Test SSM client initialization for AWS"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client

        with patch.dict(os.environ, {"AWS_REGION": "us-west-2"}):
            manager = ParameterStoreManager()
            client = manager.client

            mock_boto3_client.assert_called_once_with("ssm", region_name="us-west-2")
            assert client == mock_client

    @patch("boto3.client")
    def test_client_initialization_localstack(self, mock_boto3_client):
        """Test SSM client initialization for LocalStack"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client

        with patch.dict(
            os.environ,
            {
                "AWS_ENDPOINT_URL": "http://localhost:4566",
                "AWS_REGION": "us-east-1",
                "AWS_ACCESS_KEY_ID": "test",
                "AWS_SECRET_ACCESS_KEY": "test",
            },
        ):
            manager = ParameterStoreManager()
            client = manager.client

            mock_boto3_client.assert_called_once_with(
                "ssm",
                endpoint_url="http://localhost:4566",
                region_name="us-east-1",
                aws_access_key_id="test",
                aws_secret_access_key="test",
            )
            assert client == mock_client

    @patch("boto3.client")
    def test_client_initialization_error(self, mock_boto3_client):
        """Test SSM client initialization with error"""
        mock_boto3_client.side_effect = NoCredentialsError()

        manager = ParameterStoreManager()
        client = manager.client

        assert client is None

    def test_convert_legacy_secret_name_to_parameter_name(self):
        """Test legacy secret name to SSM parameter name conversion"""
        test_cases = [
            ("lambda/openai-api-key", "/ezychat/document-ingestion/openai/api-key"),
            ("lambda/supabase-url", "/ezychat/document-ingestion/supabase/url"),
            (
                "lambda/supabase-service-role-key",
                "/ezychat/document-ingestion/supabase/service-role-key",
            ),
            ("lambda/supabase-anon-key", "/ezychat/document-ingestion/supabase/anon-key"),
            ("unknown-secret", "unknown-secret"),  # Should return as-is for unknown secrets
        ]

        for legacy_name, expected_ssm_name in test_cases:
            result = self.manager._convert_legacy_secret_name_to_parameter_name(legacy_name)
            assert result == expected_ssm_name

    def test_get_parameter_from_cache(self):
        """Test parameter retrieval from cache"""
        # Pre-populate cache
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = "cached-value"

        result = self.manager.get_parameter("lambda/openai-api-key")
        assert result == "cached-value"

    @patch.dict(os.environ, {"TEST_ENV_VAR": "env-value"})
    def test_get_parameter_fallback_to_env(self):
        """Test parameter retrieval fallback to environment variable"""
        # Simulate non-AWS environment
        self.manager._is_aws_environment = False

        result = self.manager.get_parameter("lambda/openai-api-key", "TEST_ENV_VAR")
        assert result == "env-value"

    def test_get_parameter_not_found(self):
        """Test parameter retrieval when parameter not found"""
        # Simulate non-AWS environment with no fallback
        self.manager._is_aws_environment = False

        result = self.manager.get_parameter("lambda/openai-api-key")
        assert result is None

    @patch("boto3.client")
    def test_get_parameter_from_ssm_string(self, mock_boto3_client):
        """Test parameter retrieval from SSM Parameter Store (string value)"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.return_value = {"Parameter": {"Value": "test-api-key"}}

        self.manager._is_aws_environment = True
        result = self.manager.get_parameter("lambda/openai-api-key")

        mock_client.get_parameter.assert_called_once_with(
            Name="/ezychat/document-ingestion/openai/api-key", WithDecryption=True
        )
        assert result == "test-api-key"
        # Verify caching
        assert self.manager._cache["/ezychat/document-ingestion/openai/api-key"] == "test-api-key"

    @patch("boto3.client")
    def test_get_parameter_from_ssm_json(self, mock_boto3_client):
        """Test parameter retrieval from SSM Parameter Store (JSON value)"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        json_value = {"key1": "value1", "key2": "value2"}
        mock_client.get_parameter.return_value = {"Parameter": {"Value": json.dumps(json_value)}}

        self.manager._is_aws_environment = True
        result = self.manager.get_parameter("lambda/openai-api-key")

        assert result == json_value
        # Verify caching
        assert self.manager._cache["/ezychat/document-ingestion/openai/api-key"] == json_value

    @patch("boto3.client")
    def test_get_parameter_ssm_parameter_not_found(self, mock_boto3_client):
        """Test parameter retrieval when SSM parameter not found"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.side_effect = ClientError(
            {"Error": {"Code": "ParameterNotFound"}}, "GetParameter"
        )

        self.manager._is_aws_environment = True
        result = self.manager.get_parameter("lambda/openai-api-key")

        assert result is None

    @patch("boto3.client")
    def test_get_parameter_ssm_other_error(self, mock_boto3_client):
        """Test parameter retrieval with other SSM errors"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.side_effect = ClientError(
            {"Error": {"Code": "AccessDenied"}}, "GetParameter"
        )

        self.manager._is_aws_environment = True
        result = self.manager.get_parameter("lambda/openai-api-key")

        assert result is None

    @patch("boto3.client")
    @patch.dict(os.environ, {"FALLBACK_VAR": "fallback-value"})
    def test_get_parameter_ssm_error_with_fallback(self, mock_boto3_client):
        """Test parameter retrieval with SSM error and environment fallback"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.side_effect = ClientError(
            {"Error": {"Code": "ParameterNotFound"}}, "GetParameter"
        )

        self.manager._is_aws_environment = True
        result = self.manager.get_parameter("lambda/openai-api-key", "FALLBACK_VAR")

        assert result == "fallback-value"
        # Verify caching of fallback value
        assert self.manager._cache["/ezychat/document-ingestion/openai/api-key"] == "fallback-value"

    def test_get_secret_backward_compatibility(self):
        """Test get_secret method for backward compatibility"""
        # Pre-populate cache
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = "cached-value"

        result = self.manager.get_secret("lambda/openai-api-key")
        assert result == "cached-value"

    def test_get_parameter_string(self):
        """Test get_parameter_string method"""
        # Pre-populate cache with string value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = "string-value"

        result = self.manager.get_parameter_string("lambda/openai-api-key")
        assert result == "string-value"

    def test_get_parameter_string_non_string_value(self):
        """Test get_parameter_string with non-string value"""
        # Pre-populate cache with non-string value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = {"key": "value"}

        result = self.manager.get_parameter_string("lambda/openai-api-key")
        assert result == ""

    def test_get_parameter_string_not_found(self):
        """Test get_parameter_string when parameter not found"""
        self.manager._is_aws_environment = False

        result = self.manager.get_parameter_string("lambda/openai-api-key")
        assert result == ""

    def test_get_secret_string_backward_compatibility(self):
        """Test get_secret_string method for backward compatibility"""
        # Pre-populate cache with string value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = "string-value"

        result = self.manager.get_secret_string("lambda/openai-api-key")
        assert result == "string-value"

    def test_get_parameter_json(self):
        """Test get_parameter_json method"""
        # Pre-populate cache with JSON value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        json_data = {"api_key": "test-key", "model": "gpt-3.5-turbo"}
        self.manager._cache[ssm_param_name] = json_data

        result = self.manager.get_parameter_json("lambda/openai-api-key", "api_key")
        assert result == "test-key"

        result = self.manager.get_parameter_json("lambda/openai-api-key", "model")
        assert result == "gpt-3.5-turbo"

        result = self.manager.get_parameter_json("lambda/openai-api-key", "nonexistent")
        assert result == ""

    def test_get_parameter_json_non_dict_value(self):
        """Test get_parameter_json with non-dict value"""
        # Pre-populate cache with non-dict value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        self.manager._cache[ssm_param_name] = "string-value"

        result = self.manager.get_parameter_json("lambda/openai-api-key", "key")
        assert result == ""

    def test_get_secret_json_backward_compatibility(self):
        """Test get_secret_json method for backward compatibility"""
        # Pre-populate cache with JSON value
        ssm_param_name = "/ezychat/document-ingestion/openai/api-key"
        json_data = {"api_key": "test-key"}
        self.manager._cache[ssm_param_name] = json_data

        result = self.manager.get_secret_json("lambda/openai-api-key", "api_key")
        assert result == "test-key"

    def test_clear_cache(self):
        """Test cache clearing functionality"""
        # Pre-populate cache
        self.manager._cache["param1"] = "value1"
        self.manager._cache["param2"] = "value2"

        assert len(self.manager._cache) == 2

        self.manager.clear_cache()

        assert len(self.manager._cache) == 0


class TestGlobalInstances:
    """Test global instances and backward compatibility"""

    def test_parameter_store_manager_instance(self):
        """Test parameter_store_manager global instance"""
        assert isinstance(parameter_store_manager, ParameterStoreManager)

    def test_secrets_manager_backward_compatibility(self):
        """Test secrets_manager backward compatibility alias"""
        assert secrets_manager is parameter_store_manager

    def test_backward_compatibility_methods(self):
        """Test that backward compatibility methods work"""
        # Test that secrets_manager has the same methods as parameter_store_manager
        assert hasattr(secrets_manager, "get_secret")
        assert hasattr(secrets_manager, "get_secret_string")
        assert hasattr(secrets_manager, "get_secret_json")
        assert hasattr(secrets_manager, "get_parameter")
        assert hasattr(secrets_manager, "get_parameter_string")
        assert hasattr(secrets_manager, "get_parameter_json")


class TestConvenienceFunctions:
    """Test convenience functions for specific parameters"""

    def setup_method(self):
        """Setup for each test method"""
        parameter_store_manager.clear_cache()

    @patch.dict(os.environ, {"OPENAI_API_KEY": "env-openai-key"})
    def test_get_openai_api_key_from_env(self):
        """Test get_openai_api_key from environment"""
        parameter_store_manager._is_aws_environment = False

        result = get_openai_api_key()
        assert result == "env-openai-key"

    @patch.dict(os.environ, {"SUPABASE_URL": "https://env.supabase.co"})
    def test_get_supabase_url_from_env(self):
        """Test get_supabase_url from environment"""
        parameter_store_manager._is_aws_environment = False

        result = get_supabase_url()
        assert result == "https://env.supabase.co"

    @patch.dict(os.environ, {"SUPABASE_SERVICE_ROLE_KEY": "env-service-key"})
    def test_get_supabase_service_role_key_from_env(self):
        """Test get_supabase_service_role_key from environment"""
        parameter_store_manager._is_aws_environment = False

        result = get_supabase_service_role_key()
        assert result == "env-service-key"

    @patch.dict(os.environ, {"SUPABASE_ANON_KEY": "env-anon-key"})
    def test_get_supabase_anon_key_from_env(self):
        """Test get_supabase_anon_key from environment"""
        parameter_store_manager._is_aws_environment = False

        result = get_supabase_anon_key()
        assert result == "env-anon-key"

    @patch("boto3.client")
    def test_get_openai_api_key_from_ssm(self, mock_boto3_client):
        """Test get_openai_api_key from SSM Parameter Store"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.return_value = {"Parameter": {"Value": "ssm-openai-key"}}

        parameter_store_manager._is_aws_environment = True
        parameter_store_manager.clear_cache()

        result = get_openai_api_key()

        mock_client.get_parameter.assert_called_once_with(
            Name="/ezychat/document-ingestion/openai/api-key", WithDecryption=True
        )
        assert result == "ssm-openai-key"

    def test_get_all_parameters(self):
        """Test get_all_parameters function"""
        # Pre-populate cache to simulate having parameters
        parameter_store_manager._cache["/ezychat/document-ingestion/openai/api-key"] = "test-key"
        parameter_store_manager._cache[
            "/ezychat/document-ingestion/supabase/url"
        ] = "https://test.supabase.co"

        result = get_all_parameters()

        assert isinstance(result, dict)
        assert "has_openai_key" in result
        assert "has_supabase_url" in result
        assert "has_supabase_service_key" in result
        assert "has_supabase_anon_key" in result
        assert "is_aws_environment" in result
        assert "parameters_cached" in result

        assert result["has_openai_key"] is True
        assert result["has_supabase_url"] is True
        assert isinstance(result["is_aws_environment"], bool)
        assert isinstance(result["parameters_cached"], int)

    def test_get_all_secrets_backward_compatibility(self):
        """Test get_all_secrets backward compatibility function"""
        result = get_all_secrets()

        # Should return the same as get_all_parameters
        expected = get_all_parameters()
        assert result == expected

    def test_convenience_functions_empty_values(self):
        """Test convenience functions with empty/missing values"""
        parameter_store_manager._is_aws_environment = False
        parameter_store_manager.clear_cache()

        # Clear any environment variables
        with patch.dict(os.environ, {}, clear=True):
            assert get_openai_api_key() == ""
            assert get_supabase_url() == ""
            assert get_supabase_service_role_key() == ""
            assert get_supabase_anon_key() == ""


class TestIntegrationScenarios:
    """Test integration scenarios and edge cases"""

    def setup_method(self):
        """Setup for each test method"""
        parameter_store_manager.clear_cache()

    @patch("boto3.client")
    @patch.dict(os.environ, {"OPENAI_API_KEY": "fallback-key"})
    def test_ssm_failure_with_env_fallback(self, mock_boto3_client):
        """Test SSM failure with environment variable fallback"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.side_effect = ClientError(
            {"Error": {"Code": "ParameterNotFound"}}, "GetParameter"
        )

        # Clear cache and reset client to ensure clean state
        parameter_store_manager.clear_cache()
        parameter_store_manager._client = None
        parameter_store_manager._is_aws_environment = True

        result = get_openai_api_key()
        assert result == "fallback-key"

    @patch("boto3.client")
    def test_multiple_parameter_retrieval_caching(self, mock_boto3_client):
        """Test that multiple retrievals use caching"""
        mock_client = Mock()
        mock_boto3_client.return_value = mock_client
        mock_client.get_parameter.return_value = {"Parameter": {"Value": "cached-value"}}

        # Clear cache and reset client to ensure clean state
        parameter_store_manager.clear_cache()
        parameter_store_manager._client = None
        parameter_store_manager._is_aws_environment = True

        # First call should hit SSM
        result1 = get_openai_api_key()
        assert result1 == "cached-value"
        assert mock_client.get_parameter.call_count == 1

        # Second call should use cache
        result2 = get_openai_api_key()
        assert result2 == "cached-value"
        assert mock_client.get_parameter.call_count == 1  # No additional calls

    def test_parameter_name_conversion_edge_cases(self):
        """Test parameter name conversion with edge cases"""
        manager = ParameterStoreManager()

        # Test with already converted parameter name
        result = manager._convert_legacy_secret_name_to_parameter_name(
            "/ezychat/document-ingestion/openai/api-key"
        )
        assert result == "/ezychat/document-ingestion/openai/api-key"

        # Test with completely unknown parameter name
        result = manager._convert_legacy_secret_name_to_parameter_name("unknown/parameter")
        assert result == "unknown/parameter"

        # Test with empty string
        result = manager._convert_legacy_secret_name_to_parameter_name("")
        assert result == ""
