"""
Event Publishing Implementations for Document Ingestion Pipeline

This module provides concrete implementations of event publishing interfaces
for domain events and notifications.
"""

import json
import time
from typing import Any, Dict

from ..domain.entities import Document, ProcessingResult
from ..domain.interfaces import IEventPublisher, ILogger


class LoggingEventPublisher(IEventPublisher):
    """Simple event publisher that logs events"""

    def __init__(self, logger: ILogger):
        self._logger = logger

    async def publish_document_processing_started(self, document: Document) -> None:
        """Publish document processing started event"""
        event_data = {
            "event_type": "document_processing_started",
            "timestamp": time.time(),
            "user_id": str(document.user_id),
            "document_id": str(document.id),
            "s3_location": document.s3_location.uri,
            "original_filename": document.original_filename,
            "status": document.status.value,
            "processing_stage": document.processing_stage.value,
        }

        self._logger.info(
            "Document processing started event",
            event_type="document_processing_started",
            user_id=str(document.user_id),
            document_id=str(document.id),
            event_data=event_data,
        )

    async def publish_document_processing_completed(self, result: ProcessingResult) -> None:
        """Publish document processing completed event"""
        event_data = {
            "event_type": "document_processing_completed",
            "timestamp": time.time(),
            "user_id": str(result.user_id),
            "document_id": str(result.document_id),
            "success": result.success,
            "processed_rows": result.processed_rows,
            "processing_time_ms": result.processing_time_ms,
        }

        self._logger.info(
            "Document processing completed event",
            event_type="document_processing_completed",
            user_id=str(result.user_id),
            document_id=str(result.document_id),
            success=result.success,
            event_data=event_data,
        )

    async def publish_document_processing_failed(self, result: ProcessingResult) -> None:
        """Publish document processing failed event"""
        event_data = {
            "event_type": "document_processing_failed",
            "timestamp": time.time(),
            "user_id": str(result.user_id),
            "document_id": str(result.document_id),
            "success": result.success,
            "error_message": result.error_message,
            "processing_time_ms": result.processing_time_ms,
        }

        self._logger.error(
            "Document processing failed event",
            event_type="document_processing_failed",
            user_id=str(result.user_id),
            document_id=str(result.document_id),
            error_message=result.error_message,
            event_data=event_data,
        )


class SNSEventPublisher(IEventPublisher):
    """AWS SNS event publisher for production use"""

    def __init__(self, logger: ILogger, topic_arn: str):
        self._logger = logger
        self._topic_arn = topic_arn
        self._sns_client = None
        self._initialize_sns_client()

    def _initialize_sns_client(self):
        """Initialize SNS client"""
        try:
            import boto3

            self._sns_client = boto3.client("sns")
        except ImportError:
            self._logger.warning("boto3 not available, falling back to logging events")
            self._sns_client = None

    async def publish_document_processing_started(self, document: Document) -> None:
        """Publish document processing started event to SNS"""
        event_data = {
            "event_type": "document_processing_started",
            "timestamp": time.time(),
            "user_id": str(document.user_id),
            "document_id": str(document.id),
            "s3_location": document.s3_location.uri,
            "original_filename": document.original_filename,
            "status": document.status.value,
            "processing_stage": document.processing_stage.value,
        }

        await self._publish_to_sns(event_data)

        # Also log the event
        self._logger.info(
            "Document processing started event published",
            event_type="document_processing_started",
            user_id=str(document.user_id),
            document_id=str(document.id),
        )

    async def publish_document_processing_completed(self, result: ProcessingResult) -> None:
        """Publish document processing completed event to SNS"""
        event_data = {
            "event_type": "document_processing_completed",
            "timestamp": time.time(),
            "user_id": str(result.user_id),
            "document_id": str(result.document_id),
            "success": result.success,
            "processed_rows": result.processed_rows,
            "processing_time_ms": result.processing_time_ms,
        }

        await self._publish_to_sns(event_data)

        # Also log the event
        self._logger.info(
            "Document processing completed event published",
            event_type="document_processing_completed",
            user_id=str(result.user_id),
            document_id=str(result.document_id),
            success=result.success,
        )

    async def publish_document_processing_failed(self, result: ProcessingResult) -> None:
        """Publish document processing failed event to SNS"""
        event_data = {
            "event_type": "document_processing_failed",
            "timestamp": time.time(),
            "user_id": str(result.user_id),
            "document_id": str(result.document_id),
            "success": result.success,
            "error_message": result.error_message,
            "processing_time_ms": result.processing_time_ms,
        }

        await self._publish_to_sns(event_data)

        # Also log the event
        self._logger.error(
            "Document processing failed event published",
            event_type="document_processing_failed",
            user_id=str(result.user_id),
            document_id=str(result.document_id),
            error_message=result.error_message,
        )

    async def _publish_to_sns(self, event_data: dict[str, Any]) -> None:
        """Publish event data to SNS topic"""
        if not self._sns_client:
            # Fallback to logging if SNS is not available
            self._logger.info("SNS not available, logging event instead", event_data=event_data)
            return

        try:
            message = json.dumps(event_data, default=str)

            self._sns_client.publish(
                TopicArn=self._topic_arn,
                Message=message,
                Subject=f"Document Processing Event: {event_data.get('event_type', 'unknown')}",
            )

        except Exception as e:
            self._logger.error(f"Failed to publish event to SNS: {e}", error=e)
            # Don't raise the exception to avoid breaking the main flow


def create_event_publisher(
    logger: ILogger, use_sns: bool = False, topic_arn: str = None
) -> IEventPublisher:
    """Factory function to create appropriate event publisher"""
    if use_sns and topic_arn:
        return SNSEventPublisher(logger, topic_arn)
    else:
        return LoggingEventPublisher(logger)
