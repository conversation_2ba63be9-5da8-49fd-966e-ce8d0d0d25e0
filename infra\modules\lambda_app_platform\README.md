# Lambda App Platform Module

This Terraform module creates a comprehensive Lambda application platform with a single API Gateway that routes traffic to multiple Lambda functions. It supports YAML configuration files for Lambda settings and provides flexible domain binding capabilities.

## Features

- **Single API Gateway**: Creates one API Gateway that routes to multiple Lambda functions
- **YAML Configuration**: Supports comprehensive Lambda configuration through YAML files
- **Domain Binding**: Optional custom domain binding with Route 53 integration
- **Provider Flexibility**: Supports provider aliases for Route 53 and resource provisioning
- **Container & Zip Support**: Supports both container-based and zip-based Lambda deployments
- **VPC Integration**: Optional VPC configuration for Lambda functions
- **IAM Management**: Automatic IAM role creation with customizable policies

## Usage

### Basic Usage

```hcl
module "lambda_platform" {
  source = "../../modules/lambda_app_platform"
  
  env      = "uat"
  app_name = "api"
  
  # Load Lambda services from YAML configuration
  lambda_services_config = yamldecode(file("${path.module}/configs/lambda_services.yaml"))
  
  # Optional domain binding
  domain_name      = "api.uat.ezychat.ai"
  certificate_arn  = module.certificate.certificate_arn
  route53_zone_id  = data.aws_route53_zone.main.zone_id
  
  tags = {
    Environment = "UAT"
    Project     = "EzyChat"
  }
  
  providers = {
    aws.route53_region = aws.master
  }
}
```

### YAML Configuration Schema

Create a `lambda_services.yaml` file with the following structure:

```yaml
# Example Lambda service configuration
whatsapp-api:
  # Container-based Lambda
  image_repository_uri: "123456789012.dkr.ecr.us-east-1.amazonaws.com/whatsapp-api:latest"
  timeout: 30
  memory_size: 512
  
  # Environment variables
  environment_variables:
    NODE_ENV: "production"
    API_VERSION: "v1"
  
  # Secrets from AWS Secrets Manager
  secrets:
    DATABASE_URL: "arn:aws:secretsmanager:us-east-1:123456789012:secret:db-url"
  
  # API Gateway routes
  api_gateway:
    routes:
      - path: "/whatsapp/{proxy+}"
        method: "ANY"
      - path: "/whatsapp/health"
        method: "GET"

# Zip-based Lambda example
notification-service:
  # Zip-based Lambda
  filename: "notification-service.zip"
  handler: "index.handler"
  runtime: "nodejs18.x"
  timeout: 15
  memory_size: 256
  
  # VPC configuration
  vpc_config:
    subnet_ids: ["subnet-12345", "subnet-67890"]
    security_group_ids: ["sg-abcdef"]
  
  # Custom IAM policy
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": ["sns:Publish"],
          "Resource": "*"
        }
      ]
    }
  
  # API Gateway routes
  api_gateway:
    routes:
      - path: "/notifications"
        method: "POST"
```

## Configuration Options

### Lambda Function Settings

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `image_repository_uri` | string | ECR repository URI for container-based Lambda | null |
| `filename` | string | Path to zip file for zip-based Lambda | null |
| `handler` | string | Lambda function handler | "index.handler" |
| `runtime` | string | Lambda runtime | "nodejs18.x" |
| `timeout` | number | Function timeout in seconds | 30 |
| `memory_size` | number | Memory allocation in MB | 512 |
| `environment_variables` | map | Environment variables | {} |
| `secrets` | map | Secrets from AWS Secrets Manager | {} |

### API Gateway Routes

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `path` | string | API Gateway resource path | required |
| `method` | string | HTTP method (GET, POST, ANY, etc.) | required |
| `integration_type` | string | Integration type | "AWS_PROXY" |

### VPC Configuration

| Field | Type | Description |
|-------|------|-------------|
| `subnet_ids` | list(string) | List of subnet IDs |
| `security_group_ids` | list(string) | List of security group IDs |

## Outputs

| Name | Description |
|------|-------------|
| `api_gateway_url` | The invoke URL of the API Gateway |
| `custom_domain_name` | Custom domain name (if configured) |
| `lambda_function_arns` | Map of Lambda function ARNs |
| `api_endpoints` | Summary of API endpoints and configuration |

## Examples

### Environment-Specific Deployment

#### UAT Environment
```hcl
# infra/iac/aws-ezychat-uat-tf/lambda_app_platform.tf
module "lambda_platform" {
  source = "../../modules/lambda_app_platform"
  
  env      = "uat"
  app_name = "api"
  
  lambda_services_config = yamldecode(file("${path.module}/configs/lambda_services.yaml"))
  
  # UAT domain binding
  domain_name     = "api.uat.ezychat.ai"
  certificate_arn = module.certificate.certificate_arn
  route53_zone_id = data.aws_route53_zone.main.zone_id
  
  providers = {
    aws.route53_region = aws.master
  }
}
```

#### Production Environment
```hcl
# infra/iac/aws-ezychat-prod-tf/lambda_app_platform.tf
module "lambda_platform" {
  source = "../../modules/lambda_app_platform"
  
  env      = "prod"
  app_name = "api"
  
  lambda_services_config = yamldecode(file("${path.module}/configs/lambda_services.yaml"))
  
  # Production domain binding
  domain_name     = "api.ezychat.ai"
  certificate_arn = module.certificate.certificate_arn
  route53_zone_id = data.aws_route53_zone.main.zone_id
  
  providers = {
    aws.route53_region = aws.master
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | >= 5.0 |

## Providers

| Name | Version | Alias |
|------|---------|-------|
| aws | >= 5.0 | default |
| aws | >= 5.0 | route53_region |
