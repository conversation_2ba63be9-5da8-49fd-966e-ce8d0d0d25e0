# Network Foundation Module

This Terraform module provisions a complete VPC networking infrastructure with configurable public and private subnets, NAT Gateways, and routing tables.

## Features

- **VPC with Internet Gateway**: Creates a VPC with configurable CIDR block and attached Internet Gateway
- **Multi-AZ Support**: Automatically distributes subnets across availability zones for high availability
- **Flexible Subnet Configuration**: Configurable number of public and private subnets
- **Automatic CIDR Calculation**: Automatically calculates subnet CIDR blocks based on VPC CIDR and subnet count
- **NAT Gateway Options**: 
  - Option to enable/disable NAT Gateway creation
  - Choice between single NAT Gateway (cost-optimized) or one per AZ (high availability)
- **Comprehensive Routing**: Automatic route table creation and association for public and private subnets
- **Flexible Tagging**: Support for environment-specific and custom tags

## Usage

### Basic Usage

```hcl
module "network" {
  source = "../../modules/network_foundation"
  
  env                   = "uat"
  vpc_cidr             = "10.0.0.0/16"
  public_subnet_count  = 2
  private_subnet_count = 2
  
  tags = {
    Project = "EzyChat"
    Owner   = "Platform Team"
  }
}
```

### Advanced Usage

```hcl
module "network" {
  source = "../../modules/network_foundation"
  
  env                   = "prod"
  vpc_cidr             = "10.1.0.0/16"
  public_subnet_count  = 3
  private_subnet_count = 3
  
  # NAT Gateway configuration
  enable_nat_gateway    = true
  nat_gateway_strategy  = "per_az"  # or "single"
  
  # Custom availability zones
  availability_zones = ["ap-southeast-5a", "ap-southeast-5b", "ap-southeast-5c"]
  
  # Custom tags
  tags = {
    Project     = "EzyChat"
    Environment = "Production"
    Owner       = "Platform Team"
  }
  
  vpc_tags = {
    Name = "prod-main-vpc"
  }
  
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
  }
  
  private_subnet_tags = {
    "kubernetes.io/role/internal-elb" = "1"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | >= 4.0.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 4.0.0 |

## Resources

| Name | Type |
|------|------|
| aws_vpc.main | resource |
| aws_internet_gateway.main | resource |
| aws_subnet.public | resource |
| aws_subnet.private | resource |
| aws_eip.nat | resource |
| aws_nat_gateway.main | resource |
| aws_route_table.public | resource |
| aws_route_table.private | resource |
| aws_route_table_association.public | resource |
| aws_route_table_association.private | resource |
| aws_availability_zones.available | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| env | Environment name (e.g., dev, staging, prod) | `string` | n/a | yes |
| vpc_cidr | CIDR block for the VPC | `string` | `"10.0.0.0/16"` | no |
| public_subnet_count | Number of public subnets to create | `number` | `2` | no |
| private_subnet_count | Number of private subnets to create | `number` | `2` | no |
| enable_nat_gateway | Enable NAT Gateway for private subnets | `bool` | `true` | no |
| nat_gateway_strategy | NAT Gateway deployment strategy: 'single' or 'per_az' | `string` | `"single"` | no |
| enable_dns_hostnames | Enable DNS hostnames in the VPC | `bool` | `true` | no |
| enable_dns_support | Enable DNS support in the VPC | `bool` | `true` | no |
| availability_zones | List of availability zones to use | `list(string)` | `[]` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |
| vpc_tags | Additional tags for the VPC | `map(string)` | `{}` | no |
| public_subnet_tags | Additional tags for public subnets | `map(string)` | `{}` | no |
| private_subnet_tags | Additional tags for private subnets | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| vpc_arn | ARN of the VPC |
| internet_gateway_id | ID of the Internet Gateway |
| internet_gateway_arn | ARN of the Internet Gateway |
| public_subnet_ids | List of IDs of the public subnets |
| public_subnet_arns | List of ARNs of the public subnets |
| public_subnet_cidr_blocks | List of CIDR blocks of the public subnets |
| public_subnet_availability_zones | List of availability zones of the public subnets |
| private_subnet_ids | List of IDs of the private subnets |
| private_subnet_arns | List of ARNs of the private subnets |
| private_subnet_cidr_blocks | List of CIDR blocks of the private subnets |
| private_subnet_availability_zones | List of availability zones of the private subnets |
| nat_gateway_ids | List of IDs of the NAT Gateways |
| nat_gateway_public_ips | List of public Elastic IP addresses associated with the NAT Gateways |
| nat_gateway_private_ips | List of private IP addresses of the NAT Gateways |
| public_route_table_id | ID of the public route table |
| private_route_table_ids | List of IDs of the private route tables |
| availability_zones | List of availability zones used |
| nat_gateway_strategy | NAT Gateway deployment strategy used |
| nat_gateway_enabled | Whether NAT Gateway is enabled |

## Architecture

### Network Layout

The module creates the following network architecture:

```
VPC (10.0.0.0/16)
├── Public Subnets (Internet accessible)
│   ├── 10.0.0.0/19 (AZ-a)
│   ├── *********/19 (AZ-b)
│   └── *********/19 (AZ-c)
├── Private Subnets (NAT Gateway access)
│   ├── *********/19 (AZ-a)
│   ├── **********/19 (AZ-b)
│   └── **********/19 (AZ-c)
└── Route Tables
    ├── Public RT → Internet Gateway
    └── Private RT(s) → NAT Gateway(s)
```

### NAT Gateway Strategies

#### Single NAT Gateway (Cost-Optimized)
- One NAT Gateway in the first public subnet
- All private subnets route through this single NAT Gateway
- Lower cost but single point of failure

#### Per-AZ NAT Gateway (High Availability)
- One NAT Gateway per availability zone
- Each private subnet routes through its AZ's NAT Gateway
- Higher cost but no single point of failure

## Examples

### Cost-Optimized Setup
```hcl
module "network" {
  source = "../../modules/network_foundation"

  env                   = "dev"
  vpc_cidr             = "10.0.0.0/16"
  public_subnet_count  = 2
  private_subnet_count = 2
  enable_nat_gateway   = true
  nat_gateway_strategy = "single"
}
```

### High Availability Setup
```hcl
module "network" {
  source = "../../modules/network_foundation"

  env                   = "prod"
  vpc_cidr             = "10.1.0.0/16"
  public_subnet_count  = 3
  private_subnet_count = 3
  enable_nat_gateway   = true
  nat_gateway_strategy = "per_az"
}
```

### Public-Only Setup (No Private Subnets)
```hcl
module "network" {
  source = "../../modules/network_foundation"

  env                   = "test"
  vpc_cidr             = "10.2.0.0/16"
  public_subnet_count  = 2
  private_subnet_count = 0
  enable_nat_gateway   = false
}
```
