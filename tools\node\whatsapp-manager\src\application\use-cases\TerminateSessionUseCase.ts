import { inject, injectable } from 'tsyringe';
import { ISessionRepository, SessionNotFoundError } from '../../domain/repositories/ISessionRepository';
import { IWhatsAppService } from '../../domain/services/IWhatsAppService';
import { ILoggerService } from '../../shared/logging/interfaces';

/**
 * Request interface for terminating a session
 */
export interface TerminateSessionRequest {
  userId: string;
  reason?: string;
  forceDelete?: boolean; // Force delete even if WhatsApp service fails
}

/**
 * Response interface for session termination
 */
export interface TerminateSessionResponse {
  userId: string;
  sessionId: string;
  terminated: boolean;
  reason?: string;
  terminatedAt: Date;
}

/**
 * Use case for terminating WhatsApp sessions
 */
@injectable()
export class TerminateSessionUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('IWhatsAppService') private whatsappService: IWhatsAppService,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Execute the terminate session use case
   */
  async execute(request: TerminateSessionRequest): Promise<TerminateSessionResponse> {
    const { userId, reason, forceDelete = false } = request;
    
    this.logger.info('Terminating session', { 
      userId, 
      reason,
      forceDelete
    });

    this.validateRequest(request);

    try {
      // Find session in repository
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        throw new SessionNotFoundError(`Session not found for user: ${userId}`);
      }

      const terminatedAt = new Date();
      let whatsappTerminated = false;
      let repositoryUpdated = false;

      // Attempt to terminate WhatsApp connection
      try {
        const isActive = await this.whatsappService.isSessionActive(userId);
        if (isActive) {
          await this.whatsappService.terminateSession(userId);
          whatsappTerminated = true;
          this.logger.info('WhatsApp connection terminated', { userId });
        } else {
          whatsappTerminated = true; // No active connection to terminate
          this.logger.debug('No active WhatsApp connection to terminate', { userId });
        }
      } catch (error) {
        this.logger.error('Failed to terminate WhatsApp connection', {
          userId,
          error: (error as Error).message
        });

        if (!forceDelete) {
          throw new Error(`Failed to terminate WhatsApp connection: ${(error as Error).message}`);
        }
        
        this.logger.warn('Continuing with repository cleanup despite WhatsApp termination failure', {
          userId,
          forceDelete
        });
      }

      // Update session status in repository
      try {
        await this.sessionRepository.updateSessionStatus(userId, 'disconnected', {
          disconnectedAt: terminatedAt
        });
        repositoryUpdated = true;
        this.logger.debug('Session status updated to disconnected', { userId });
      } catch (error) {
        this.logger.error('Failed to update session status', {
          userId,
          error: (error as Error).message
        });

        if (!forceDelete) {
          throw new Error(`Failed to update session status: ${(error as Error).message}`);
        }
      }

      // Delete session from repository
      try {
        await this.sessionRepository.delete(userId);
        this.logger.info('Session deleted from repository', { userId });
      } catch (error) {
        this.logger.error('Failed to delete session from repository', {
          userId,
          error: (error as Error).message
        });

        if (!forceDelete) {
          throw new Error(`Failed to delete session: ${(error as Error).message}`);
        }
      }

      const response: TerminateSessionResponse = {
        userId: session.userId,
        sessionId: session.sessionId,
        terminated: whatsappTerminated,
        reason: reason || 'Manual termination',
        terminatedAt
      };

      this.logger.info('Session termination completed', {
        userId,
        sessionId: session.sessionId,
        whatsappTerminated,
        repositoryUpdated,
        reason: response.reason
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to terminate session', {
        userId,
        error: (error as Error).message,
        stack: (error as Error).stack
      });
      throw error;
    }
  }

  /**
   * Terminate multiple sessions
   */
  async executeMany(requests: TerminateSessionRequest[]): Promise<TerminateSessionResponse[]> {
    this.logger.info('Terminating multiple sessions', { 
      count: requests.length,
      userIds: requests.map(r => r.userId)
    });

    const results: TerminateSessionResponse[] = [];
    const errors: { userId: string; error: string }[] = [];

    // Process sessions in parallel with controlled concurrency
    const concurrency = 5;
    const chunks = this.chunkArray(requests, concurrency);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (request) => {
        try {
          const result = await this.execute(request);
          results.push(result);
        } catch (error) {
          errors.push({
            userId: request.userId,
            error: (error as Error).message
          });
          this.logger.error('Failed to terminate session in batch', {
            userId: request.userId,
            error: (error as Error).message
          });
        }
      });

      await Promise.all(chunkPromises);
    }

    this.logger.info('Batch session termination completed', {
      totalRequests: requests.length,
      successful: results.length,
      failed: errors.length,
      errors
    });

    return results;
  }

  /**
   * Terminate all sessions for cleanup
   */
  async terminateAllSessions(reason?: string): Promise<{
    terminated: number;
    failed: number;
    errors: string[];
  }> {
    this.logger.info('Terminating all sessions', { reason });

    try {
      // Get all active sessions
      const activeSessions = await this.sessionRepository.findAllActive();
      
      if (activeSessions.length === 0) {
        this.logger.info('No active sessions to terminate');
        return { terminated: 0, failed: 0, errors: [] };
      }

      // Create termination requests
      const requests: TerminateSessionRequest[] = activeSessions.map(session => ({
        userId: session.userId,
        reason: reason || 'System shutdown',
        forceDelete: true // Force delete during system shutdown
      }));

      // Execute batch termination
      const results = await this.executeMany(requests);

      const terminated = results.length;
      const failed = activeSessions.length - terminated;
      const errors = activeSessions
        .filter(session => !results.some(r => r.userId === session.userId))
        .map(session => `Failed to terminate session for user: ${session.userId}`);

      this.logger.info('All sessions termination completed', {
        totalSessions: activeSessions.length,
        terminated,
        failed,
        reason
      });

      return { terminated, failed, errors };

    } catch (error) {
      this.logger.error('Failed to terminate all sessions', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Terminate expired sessions
   */
  async terminateExpiredSessions(): Promise<{
    terminated: number;
    failed: number;
    errors: string[];
  }> {
    this.logger.info('Terminating expired sessions');

    try {
      const expiredSessions = await this.sessionRepository.findExpiredSessions();
      
      if (expiredSessions.length === 0) {
        this.logger.info('No expired sessions to terminate');
        return { terminated: 0, failed: 0, errors: [] };
      }

      const requests: TerminateSessionRequest[] = expiredSessions.map(session => ({
        userId: session.userId,
        reason: 'Session expired',
        forceDelete: true
      }));

      const results = await this.executeMany(requests);

      const terminated = results.length;
      const failed = expiredSessions.length - terminated;
      const errors = expiredSessions
        .filter(session => !results.some(r => r.userId === session.userId))
        .map(session => `Failed to terminate expired session for user: ${session.userId}`);

      this.logger.info('Expired sessions cleanup completed', {
        expiredSessions: expiredSessions.length,
        terminated,
        failed
      });

      return { terminated, failed, errors };

    } catch (error) {
      this.logger.error('Failed to terminate expired sessions', {
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Validate request parameters
   */
  private validateRequest(request: TerminateSessionRequest): void {
    if (!request.userId || typeof request.userId !== 'string') {
      throw new Error('User ID is required and must be a string');
    }

    if (request.userId.length < 3 || request.userId.length > 100) {
      throw new Error('User ID must be between 3 and 100 characters');
    }

    if (request.reason && request.reason.length > 200) {
      throw new Error('Reason must not exceed 200 characters');
    }
  }

  /**
   * Utility function to chunk array for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}
