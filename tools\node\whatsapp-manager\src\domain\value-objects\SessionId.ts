import { v4 as uuidv4 } from 'uuid';

/**
 * SessionId value object
 * Ensures session ID validity and provides type safety
 */
export class SessionId {
  private readonly value: string;

  constructor(value: string) {
    this.validateSessionId(value);
    this.value = value;
  }

  /**
   * Generate a new unique session ID
   */
  static generate(): SessionId {
    return new SessionId(uuidv4());
  }

  /**
   * Create SessionId from string with validation
   */
  static fromString(value: string): SessionId {
    return new SessionId(value);
  }

  /**
   * Get the string representation
   */
  toString(): string {
    return this.value;
  }

  /**
   * Get the raw value
   */
  getValue(): string {
    return this.value;
  }

  /**
   * Check equality with another SessionId
   */
  equals(other: SessionId): boolean {
    return this.value === other.value;
  }

  /**
   * Check if this is a valid UUID v4
   */
  isUUID(): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(this.value);
  }

  /**
   * Get a shortened version for display (first 8 characters)
   */
  toShortString(): string {
    return this.value.substring(0, 8);
  }

  /**
   * Validate session ID format
   */
  private validateSessionId(value: string): void {
    if (!value || typeof value !== 'string') {
      throw new Error('SessionId must be a non-empty string');
    }

    if (value.length < 8) {
      throw new Error('SessionId must be at least 8 characters long');
    }

    if (value.length > 100) {
      throw new Error('SessionId must not exceed 100 characters');
    }

    // Allow alphanumeric characters, hyphens, and underscores
    if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
      throw new Error('SessionId can only contain alphanumeric characters, hyphens, and underscores');
    }
  }

  /**
   * JSON serialization
   */
  toJSON(): string {
    return this.value;
  }

  /**
   * Create from JSON
   */
  static fromJSON(json: string): SessionId {
    return new SessionId(json);
  }
}
