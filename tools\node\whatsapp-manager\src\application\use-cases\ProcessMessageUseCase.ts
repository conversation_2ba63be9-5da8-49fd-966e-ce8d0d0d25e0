import { inject, injectable } from 'tsyringe';

import { WhatsAppMessage, MessageFilter } from '../../domain/entities/WhatsAppMessage';
import { ISessionRepository } from '../../domain/repositories/ISessionRepository';
import { ILoggerService } from '../../shared/logging/interfaces';
import { MessageProcessor } from '../../infrastructure/whatsapp/MessageProcessor';
import { RateLimiter } from '../../infrastructure/whatsapp/RateLimiter';
import { BaileysEventEmitter } from '../../infrastructure/whatsapp/BaileysEventEmitter';

/**
 * Process message request
 */
export interface ProcessMessageRequest {
  userId: string;
  sessionId: string;
  rawMessages: any[]; // Baileys raw messages
  options?: {
    enableDeduplication?: boolean;
    enableContentFiltering?: boolean;
    enableMediaProcessing?: boolean;
    maxMessageAge?: number;
  };
}

/**
 * Process message response
 */
export interface ProcessMessageResponse {
  success: boolean;
  processedCount: number;
  skippedCount: number;
  errorCount: number;
  messages: WhatsAppMessage[];
  rateLimitInfo?: {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  };
  message: string;
}

/**
 * Send message request
 */
export interface SendMessageRequest {
  userId: string;
  to: string;
  content: string;
  type?: 'text' | 'image' | 'audio' | 'video' | 'document';
  mediaUrl?: string;
  quotedMessageId?: string;
  metadata?: Record<string, any>;
}

/**
 * Send message response
 */
export interface SendMessageResponse {
  success: boolean;
  messageId?: string;
  message: string;
  rateLimitInfo?: {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  };
}

/**
 * Use case for processing WhatsApp messages
 * Handles incoming message processing, validation, and outbound message sending
 */
@injectable()
export class ProcessMessageUseCase {
  constructor(
    @inject('ISessionRepository') private sessionRepository: ISessionRepository,
    @inject('MessageProcessor') private messageProcessor: MessageProcessor,
    @inject('RateLimiter') private rateLimiter: RateLimiter,
    @inject('BaileysEventEmitter') private eventEmitter: BaileysEventEmitter,
    @inject('ILoggerService') private logger: ILoggerService
  ) {}

  /**
   * Process incoming messages
   */
  async processIncomingMessages(request: ProcessMessageRequest): Promise<ProcessMessageResponse> {
    const { userId, sessionId, rawMessages, options } = request;

    try {
      this.logger.info('Processing incoming messages', {
        userId,
        sessionId,
        messageCount: rawMessages.length
      });

      // Validate request
      this.validateProcessRequest(request);

      // Check rate limits for message processing
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'messages');
      if (!rateLimitResult.allowed) {
        this.logger.warn('Message processing rate limit exceeded', {
          userId,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        });

        return {
          success: false,
          processedCount: 0,
          skippedCount: rawMessages.length,
          errorCount: 0,
          messages: [],
          rateLimitInfo: {
            allowed: false,
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime
          },
          message: 'Rate limit exceeded for message processing'
        };
      }

      // Verify session exists and is active
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        throw new Error(`Session not found for user: ${userId}`);
      }

      if (session.status !== 'connected') {
        this.logger.warn('Attempted to process messages for non-connected session', {
          userId,
          sessionStatus: session.status
        });

        return {
          success: false,
          processedCount: 0,
          skippedCount: rawMessages.length,
          errorCount: 0,
          messages: [],
          message: `Session not connected. Current status: ${session.status}`
        };
      }

      // Process messages
      const processedMessages = await this.messageProcessor.processMessages(
        userId,
        sessionId,
        rawMessages,
        options
      );

      const processedCount = processedMessages.length;
      const skippedCount = rawMessages.length - processedCount;

      // Emit events for processed messages
      for (const message of processedMessages) {
        this.eventEmitter.emitMessageEvent(
          userId,
          'received',
          message,
          {
            sessionId,
            processedAt: new Date().toISOString()
          }
        );
      }

      // Update session last activity
      await this.sessionRepository.updateSessionActivity(userId, new Date());

      this.logger.info('Messages processed successfully', {
        userId,
        sessionId,
        totalMessages: rawMessages.length,
        processedCount,
        skippedCount
      });

      return {
        success: true,
        processedCount,
        skippedCount,
        errorCount: 0,
        messages: processedMessages,
        rateLimitInfo: {
          allowed: true,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        },
        message: `Successfully processed ${processedCount} messages`
      };

    } catch (error) {
      this.logger.error('Failed to process incoming messages', {
        userId,
        sessionId,
        messageCount: rawMessages.length,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'messages');

      return {
        success: false,
        processedCount: 0,
        skippedCount: 0,
        errorCount: rawMessages.length,
        messages: [],
        message: `Failed to process messages: ${(error as Error).message}`
      };
    }
  }

  /**
   * Send outbound message
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    const { userId, to, content, type = 'text', mediaUrl, quotedMessageId, metadata } = request;

    try {
      this.logger.info('Sending message', {
        userId,
        to,
        type,
        hasMedia: !!mediaUrl,
        isReply: !!quotedMessageId
      });

      // Validate request
      this.validateSendRequest(request);

      // Check rate limits for sending messages
      const rateLimitResult = await this.rateLimiter.checkUserLimit(userId, 'messages');
      if (!rateLimitResult.allowed) {
        this.logger.warn('Message sending rate limit exceeded', {
          userId,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        });

        return {
          success: false,
          message: 'Rate limit exceeded for sending messages',
          rateLimitInfo: {
            allowed: false,
            remaining: rateLimitResult.remaining,
            resetTime: rateLimitResult.resetTime
          }
        };
      }

      // Verify session exists and is connected
      const session = await this.sessionRepository.findByUserId(userId);
      if (!session) {
        throw new Error(`Session not found for user: ${userId}`);
      }

      if (session.status !== 'connected') {
        return {
          success: false,
          message: `Session not connected. Current status: ${session.status}`
        };
      }

      // Prepare message data
      // const messageData = this.prepareMessageData(type, content, mediaUrl, quotedMessageId); // Unused for now

      // Send message through WhatsApp service (this would be implemented in the main service)
      // For now, we'll simulate the sending process
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create outbound message entity
      const outboundMessage = WhatsAppMessage.createOutbound(
        session.sessionId,
        userId,
        session.phoneNumber || userId,
        to,
        type,
        content,
        quotedMessageId,
        mediaUrl ? { mimeType: this.getMimeType(type), url: mediaUrl } : undefined,
        undefined,
        undefined,
        metadata
      );

      // Mark as sent
      const sentMessage = outboundMessage.markAsSent(messageId);

      // Emit event
      this.eventEmitter.emitMessageEvent(
        userId,
        'sent',
        sentMessage,
        {
          sessionId: session.sessionId,
          sentAt: new Date().toISOString()
        }
      );

      // Update session last activity
      await this.sessionRepository.updateSessionActivity(userId, new Date());

      this.logger.info('Message sent successfully', {
        userId,
        to,
        messageId,
        type
      });

      return {
        success: true,
        messageId,
        message: 'Message sent successfully',
        rateLimitInfo: {
          allowed: true,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime
        }
      };

    } catch (error) {
      this.logger.error('Failed to send message', {
        userId,
        to,
        type,
        error: (error as Error).message
      });

      // Record failure for rate limiting
      this.rateLimiter.recordFailure(userId, 'messages');

      return {
        success: false,
        message: `Failed to send message: ${(error as Error).message}`
      };
    }
  }

  /**
   * Get messages for user with filtering
   */
  async getMessages(
    userId: string,
    filter?: MessageFilter,
    limit: number = 50,
    offset: number = 0
  ): Promise<{
    messages: WhatsAppMessage[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      this.logger.debug('Getting messages', {
        userId,
        filter,
        limit,
        offset
      });

      // This would typically query a message repository
      // For now, we'll return an empty result
      return {
        messages: [],
        total: 0,
        hasMore: false
      };

    } catch (error) {
      this.logger.error('Failed to get messages', {
        userId,
        error: (error as Error).message
      });

      return {
        messages: [],
        total: 0,
        hasMore: false
      };
    }
  }

  /**
   * Validate process message request
   */
  private validateProcessRequest(request: ProcessMessageRequest): void {
    if (!request.userId || request.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!request.sessionId || request.sessionId.trim().length === 0) {
      throw new Error('Session ID is required');
    }

    if (!Array.isArray(request.rawMessages)) {
      throw new Error('Raw messages must be an array');
    }

    if (request.rawMessages.length > 100) {
      throw new Error('Too many messages to process at once (max 100)');
    }
  }

  /**
   * Validate send message request
   */
  private validateSendRequest(request: SendMessageRequest): void {
    if (!request.userId || request.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    if (!request.to || request.to.trim().length === 0) {
      throw new Error('Recipient is required');
    }

    if (!request.content || request.content.trim().length === 0) {
      if (!request.mediaUrl) {
        throw new Error('Content or media URL is required');
      }
    }

    if (request.content && request.content.length > 4096) {
      throw new Error('Message content is too long (max 4096 characters)');
    }

    // Validate phone number format
    const phoneRegex = /^\d{10,15}@(s\.whatsapp\.net|c\.us|g\.us)$/;
    if (!phoneRegex.test(request.to)) {
      throw new Error('Invalid recipient phone number format');
    }
  }



  /**
   * Get MIME type for message type
   */
  private getMimeType(type: string): string {
    switch (type) {
      case 'image':
        return 'image/jpeg';
      case 'audio':
        return 'audio/ogg';
      case 'video':
        return 'video/mp4';
      case 'document':
        return 'application/octet-stream';
      default:
        return 'text/plain';
    }
  }
}
