# Lambda Node.js Sample - Build Configuration
APP_NAME = lambda-nodejs-sample
AWS_REGION = ap-southeast-5
AWS_ACCOUNT_ID = $(shell aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "************")
ECR_REPOSITORY = $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com/$(APP_NAME)
IMAGE_TAG = latest
LOCAL_IMAGE = $(APP_NAME):$(IMAGE_TAG)

# Default target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  install        - Install dependencies"
	@echo "  build          - Build Docker image locally"
	@echo "  clean          - Clean up local resources"
	@echo "  ecr-login      - Login to ECR"
	@echo "  ecr-create     - Create ECR repository"
	@echo "  push           - Build and push image to ECR"
	@echo "  deploy         - Full deployment (ECR + Lambda)"
	@echo "  deploy-zip     - Deploy using ZIP package (no Docker required)"
	@echo "  push           - Build and push image to ECR"
	@echo "  test-ecr       - Test ECR image by creating/testing/deleting Lambda"
	@echo "  run-local      - Run container locally for testing"

# Install dependencies
.PHONY: install
install:
	npm install

# Build Docker image locally
.PHONY: build
build:
	sudo docker build --platform linux/amd64 -t $(LOCAL_IMAGE) .
	@echo "Built image: $(LOCAL_IMAGE)"

# Clean up
.PHONY: clean
clean:
	npm run clean
	sudo docker rmi $(LOCAL_IMAGE) 2>/dev/null || true

# Login to ECR
.PHONY: ecr-login
ecr-login:
	aws ecr get-login-password --region $(AWS_REGION) | sudo docker login --username AWS --password-stdin $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com

# Create ECR repository
.PHONY: ecr-create
ecr-create:
	@echo "Creating ECR repository: $(APP_NAME) in account $(AWS_ACCOUNT_ID)"
	@aws ecr describe-repositories --repository-names $(APP_NAME) --region $(AWS_REGION) >/dev/null 2>&1 || \
	(echo "Repository doesn't exist, creating..." && \
	aws ecr create-repository \
		--repository-name $(APP_NAME) \
		--region $(AWS_REGION) \
		--image-scanning-configuration scanOnPush=true \
		--encryption-configuration encryptionType=AES256)
	@echo "ECR repository ready: $(ECR_REPOSITORY)"

# Tag and push image to ECR
.PHONY: push
push: build ecr-login
	sudo docker tag $(LOCAL_IMAGE) $(ECR_REPOSITORY):$(IMAGE_TAG)
	sudo docker push $(ECR_REPOSITORY):$(IMAGE_TAG)
	@echo "Pushed to: $(ECR_REPOSITORY):$(IMAGE_TAG)"

# Full deployment
.PHONY: deploy
deploy: ecr-create push
	@echo "Deployment completed!"
	@echo "Image URI: $(ECR_REPOSITORY):$(IMAGE_TAG)"

# Run container locally for testing
.PHONY: run-local
run-local: build
	@echo "Starting container locally on port 9000..."
	@echo "Test with: curl -XPOST 'http://localhost:9000/2015-03-31/functions/function/invocations' -d '{\"path\":\"/sample\",\"httpMethod\":\"GET\"}'"
	sudo docker run -p 9000:8080 $(LOCAL_IMAGE)

# Deploy using ZIP package (no Docker required)
.PHONY: deploy-zip
deploy-zip:
	./deploy.sh

# Test ECR image by creating/testing/deleting Lambda function
.PHONY: test-ecr
test-ecr:
	./test-ecr-lambda.sh

# Show image URI for Lambda function configuration
.PHONY: show-uri
show-uri:
	@echo "ECR Image URI: $(ECR_REPOSITORY):$(IMAGE_TAG)"
