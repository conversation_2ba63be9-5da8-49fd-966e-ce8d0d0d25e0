import { Router } from 'express';
import { container } from 'tsyringe';
import { AuthController } from '../controllers/AuthController';
import { validateRequest } from '../middleware/validation';
import { rateLimiter } from '../middleware/rateLimiter';
import { requestLogger } from '../middleware/requestLogger';
import Jo<PERSON> from 'joi';

/**
 * Validation schemas for auth endpoints
 */
const generateQrLinkSchema = {
  body: Joi.object({
    userId: Joi.string().trim().min(1).max(255).required()
      .messages({
        'string.empty': 'User ID cannot be empty',
        'string.min': 'User ID must be at least 1 character',
        'string.max': 'User ID must be 255 characters or less',
        'any.required': 'User ID is required'
      }),
    expirySeconds: Joi.number().integer().min(1).max(86400).optional()
      .messages({
        'number.base': 'Expiry seconds must be a number',
        'number.integer': 'Expiry seconds must be an integer',
        'number.min': 'Expiry seconds must be at least 1',
        'number.max': 'Expiry seconds must be at most 86400 (24 hours)'
      }),
    maxActiveTokens: Joi.number().integer().min(0).max(100).optional()
      .messages({
        'number.base': 'Max active tokens must be a number',
        'number.integer': 'Max active tokens must be an integer',
        'number.min': 'Max active tokens must be at least 0',
        'number.max': 'Max active tokens must be at most 100'
      }),
    metadata: Joi.object().optional()
      .messages({
        'object.base': 'Metadata must be an object'
      }),
    baseUrl: Joi.string().uri().optional()
      .messages({
        'string.uri': 'Base URL must be a valid URL'
      })
  })
};

const tokenIdParamSchema = {
  params: Joi.object({
    tokenId: Joi.string().trim().min(1).required()
      .messages({
        'string.empty': 'Token ID cannot be empty',
        'string.min': 'Token ID must be at least 1 character',
        'any.required': 'Token ID is required'
      })
  })
};

const userIdParamSchema = {
  params: Joi.object({
    userId: Joi.string().trim().min(1).max(255).required()
      .messages({
        'string.empty': 'User ID cannot be empty',
        'string.min': 'User ID must be at least 1 character',
        'string.max': 'User ID must be 255 characters or less',
        'any.required': 'User ID is required'
      })
  })
};

const getUserTokensSchema = {
  ...userIdParamSchema,
  query: Joi.object({
    includeExpired: Joi.boolean().optional()
      .messages({
        'boolean.base': 'Include expired must be a boolean'
      })
  })
};

const cleanupSchema = {
  body: Joi.object({
    batchSize: Joi.number().integer().min(1).max(1000).optional()
      .messages({
        'number.base': 'Batch size must be a number',
        'number.integer': 'Batch size must be an integer',
        'number.min': 'Batch size must be at least 1',
        'number.max': 'Batch size must be at most 1000'
      })
  })
};

/**
 * Create auth routes with all endpoints
 */
export function createAuthRoutes(): Router {
  const router = Router();
  const authController = container.resolve(AuthController);

  // Apply middleware to all auth routes
  router.use(requestLogger);

  // POST /api/auth/qr-link - Generate QR authentication link
  router.post(
    '/qr-link',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // 10 requests per window per IP
      message: {
        success: false,
        message: 'Too many QR link generation requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(generateQrLinkSchema),
    async (req, res) => {
      await authController.generateQrLink(req, res);
    }
  );

  // GET /api/auth/qr-link/:tokenId - Get QR link details
  router.get(
    '/qr-link/:tokenId',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 50, // 50 requests per window per IP
      message: {
        success: false,
        message: 'Too many requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(tokenIdParamSchema),
    async (req, res) => {
      await authController.getQrLinkDetails(req, res);
    }
  );

  // POST /api/auth/qr-link/:tokenId/validate - Validate QR token
  router.post(
    '/qr-link/:tokenId/validate',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 20, // 20 requests per window per IP
      message: {
        success: false,
        message: 'Too many validation requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(tokenIdParamSchema),
    async (req, res) => {
      await authController.validateQrToken(req, res);
    }
  );

  // POST /api/auth/qr-link/:tokenId/use - Mark token as used
  router.post(
    '/qr-link/:tokenId/use',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 requests per window per IP (should be one-time use)
      message: {
        success: false,
        message: 'Too many token usage requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(tokenIdParamSchema),
    async (req, res) => {
      await authController.useQrToken(req, res);
    }
  );

  // GET /api/auth/users/:userId/tokens - Get user tokens
  router.get(
    '/users/:userId/tokens',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 30, // 30 requests per window per IP
      message: {
        success: false,
        message: 'Too many requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(getUserTokensSchema),
    async (req, res) => {
      await authController.getUserTokens(req, res);
    }
  );

  // DELETE /api/auth/users/:userId/tokens - Revoke user tokens
  router.delete(
    '/users/:userId/tokens',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 requests per window per IP
      message: {
        success: false,
        message: 'Too many revocation requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(userIdParamSchema),
    async (req, res) => {
      await authController.revokeUserTokens(req, res);
    }
  );

  // DELETE /api/auth/qr-link/:tokenId - Delete specific token
  router.delete(
    '/qr-link/:tokenId',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10, // 10 requests per window per IP
      message: {
        success: false,
        message: 'Too many deletion requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(tokenIdParamSchema),
    async (req, res) => {
      await authController.deleteQrToken(req, res);
    }
  );

  // GET /api/auth/statistics - Get auth statistics
  router.get(
    '/statistics',
    rateLimiter({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 20, // 20 requests per window per IP
      message: {
        success: false,
        message: 'Too many requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    async (req, res) => {
      await authController.getStatistics(req, res);
    }
  );

  // POST /api/auth/cleanup - Cleanup expired tokens
  router.post(
    '/cleanup',
    rateLimiter({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 5, // 5 requests per hour per IP
      message: {
        success: false,
        message: 'Too many cleanup requests, please try again later',
        error: { code: 'RATE_LIMIT_EXCEEDED', message: 'Rate limit exceeded' }
      }
    }),
    validateRequest(cleanupSchema),
    async (req, res) => {
      await authController.cleanupExpiredTokens(req, res);
    }
  );

  return router;
}

/**
 * Auth routes configuration
 */
export const authRoutesConfig = {
  prefix: '/auth',
  description: 'Authentication endpoints for QR token management',
  version: '1.0.0',
  endpoints: [
    {
      method: 'POST',
      path: '/qr-link',
      description: 'Generate a new QR authentication link',
      rateLimit: '10 requests per 15 minutes'
    },
    {
      method: 'GET',
      path: '/qr-link/:tokenId',
      description: 'Get QR authentication link details',
      rateLimit: '50 requests per 15 minutes'
    },
    {
      method: 'POST',
      path: '/qr-link/:tokenId/validate',
      description: 'Validate a QR authentication token',
      rateLimit: '20 requests per 15 minutes'
    },
    {
      method: 'POST',
      path: '/qr-link/:tokenId/use',
      description: 'Mark a QR authentication token as used',
      rateLimit: '5 requests per 15 minutes'
    },
    {
      method: 'GET',
      path: '/users/:userId/tokens',
      description: 'Get all tokens for a specific user',
      rateLimit: '30 requests per 15 minutes'
    },
    {
      method: 'DELETE',
      path: '/users/:userId/tokens',
      description: 'Revoke all tokens for a specific user',
      rateLimit: '5 requests per 15 minutes'
    },
    {
      method: 'DELETE',
      path: '/qr-link/:tokenId',
      description: 'Delete a specific QR authentication token',
      rateLimit: '10 requests per 15 minutes'
    },
    {
      method: 'GET',
      path: '/statistics',
      description: 'Get authentication statistics',
      rateLimit: '20 requests per 15 minutes'
    },
    {
      method: 'POST',
      path: '/cleanup',
      description: 'Cleanup expired tokens',
      rateLimit: '5 requests per hour'
    }
  ]
};
